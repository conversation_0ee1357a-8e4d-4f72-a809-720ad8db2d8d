# 🔧 PARK & RENT IMAGE FIX SETUP INSTRUCTIONS

## 📋 FILES TO UPLOAD AND REPLACE

### 1. **config/filesystems.php**
- **Location**: `/home/<USER>/domains/ebisera.com/public_html/api/config/filesystems.php`
- **Action**: Replace existing file
- **Purpose**: Fixes storage URL generation to use correct production URLs

### 2. **.env** (Optional - if you want the updated version)
- **Location**: `/home/<USER>/domains/ebisera.com/public_html/api/.env`
- **Action**: Replace existing file or just add `ASSET_URL=https://ebisera.com/api/public`
- **Purpose**: Ensures correct environment configuration

## 🚀 SETUP STEPS

### Step 1: Upload Files
```bash
# Upload the fixed files to your server
# Replace these files:
# - config/filesystems.php
# - .env (optional)
```

### Step 2: Upload and Run Scripts
```bash
# Upload these scripts to your api directory:
# - fix-database-urls.php
# - test-storage-urls.php
# - clear-caches.php

# Then run them in order:
cd /home/<USER>/domains/ebisera.com/public_html/api

# 1. Clear caches first
php clear-caches.php

# 2. Fix database URLs
php fix-database-urls.php

# 3. Test the configuration
php test-storage-urls.php
```

### Step 3: Test Image Access
```bash
# Test API response
curl https://ebisera.com/api/public/api/cars | grep -o '"https://ebisera.com[^"]*"'

# Test direct image access
curl -I https://ebisera.com/api/public/storage/car-images/gs9azPWSBKYnJ28bAVkYAkhYBcWUVDwU8AFlFBAW.jpg
```

## 🎯 EXPECTED RESULTS

After completing these steps:

✅ **New images uploaded through frontend will have correct URLs**
✅ **Existing images in database will be fixed**
✅ **Images will display properly in the React frontend**
✅ **No more localhost or connection refused errors**

## 🔧 KEY CHANGES MADE

1. **filesystems.php**: Changed storage URL from `/storage` to `/api/public/storage`
2. **.env**: Added `FILESYSTEM_DISK=public` and `ASSET_URL=https://ebisera.com/api/public`
3. **Database**: Fixed all existing image URLs to use production domain
4. **Caches**: Cleared all Laravel caches to apply new configuration

## 🧪 VERIFICATION

After setup, verify these URLs work:
- API: `https://ebisera.com/api/public/api/cars`
- Images: `https://ebisera.com/api/public/storage/car-images/[filename].jpg`
- Frontend: `https://ebisera.com` (images should display)

## 📞 TROUBLESHOOTING

If images still don't work:
1. Check storage link exists: `ls -la public/storage`
2. Check file permissions: `chmod -R 755 storage/app/public/`
3. Verify .env APP_URL is correct: `grep APP_URL .env`
4. Re-run cache clear: `php clear-caches.php`
