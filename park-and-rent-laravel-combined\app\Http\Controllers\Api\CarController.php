<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Car;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CarController extends Controller
{
    /**
     * Display a listing of cars.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Car::where('is_active', true)->with('owner');

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('make', 'like', "%{$search}%")
                      ->orWhere('model', 'like', "%{$search}%")
                      ->orWhere('location', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Filter by location
            if ($request->has('location') && $request->location) {
                $query->where('location', 'like', "%{$request->location}%");
            }

            // Filter by price range
            if ($request->has('min_price') && $request->min_price) {
                $query->where('price_per_day', '>=', $request->min_price);
            }

            if ($request->has('max_price') && $request->max_price) {
                $query->where('price_per_day', '<=', $request->max_price);
            }

            // Sort options
            $sortBy = $request->get('sort_by', 'latest');
            switch ($sortBy) {
                case 'price_low':
                    $query->orderBy('price_per_day', 'asc');
                    break;
                case 'price_high':
                    $query->orderBy('price_per_day', 'desc');
                    break;
                case 'year_new':
                    $query->orderBy('year', 'desc');
                    break;
                case 'year_old':
                    $query->orderBy('year', 'asc');
                    break;
                default:
                    $query->latest();
                    break;
            }

            $cars = $query->paginate($request->get('per_page', 12));

            return response()->json([
                'success' => true,
                'data' => $cars->items(),
                'pagination' => [
                    'current_page' => $cars->currentPage(),
                    'last_page' => $cars->lastPage(),
                    'per_page' => $cars->perPage(),
                    'total' => $cars->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch cars',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified car.
     */
    public function show(Car $car): JsonResponse
    {
        try {
            $car->load('owner');
            
            return response()->json([
                'success' => true,
                'data' => $car
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch car details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created car.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'make' => 'required|string|max:255',
                'model' => 'required|string|max:255',
                'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'color' => 'required|string|max:255',
                'license_plate' => 'required|string|max:255|unique:cars',
                'transmission' => 'required|in:automatic,manual',
                'fuel_type' => 'required|in:petrol,diesel,hybrid,electric',
                'seats' => 'required|integer|min:1|max:50',
                'mileage' => 'required|integer|min:0',
                'price_per_day' => 'required|numeric|min:0',
                'location' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
            ]);

            $carData = $request->all();
            $carData['owner_id'] = auth()->id();
            $carData['is_active'] = true;

            $car = Car::create($carData);

            return response()->json([
                'success' => true,
                'message' => 'Car created successfully',
                'data' => $car
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create car',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified car.
     */
    public function update(Request $request, Car $car): JsonResponse
    {
        try {
            // Check if user owns the car
            if ($car->owner_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $request->validate([
                'make' => 'required|string|max:255',
                'model' => 'required|string|max:255',
                'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'color' => 'required|string|max:255',
                'license_plate' => 'required|string|max:255|unique:cars,license_plate,' . $car->id,
                'transmission' => 'required|in:automatic,manual',
                'fuel_type' => 'required|in:petrol,diesel,hybrid,electric',
                'seats' => 'required|integer|min:1|max:50',
                'mileage' => 'required|integer|min:0',
                'price_per_day' => 'required|numeric|min:0',
                'location' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
            ]);

            $car->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Car updated successfully',
                'data' => $car
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update car',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified car.
     */
    public function destroy(Car $car): JsonResponse
    {
        try {
            // Check if user owns the car
            if ($car->owner_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $car->delete();

            return response()->json([
                'success' => true,
                'message' => 'Car deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete car',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
