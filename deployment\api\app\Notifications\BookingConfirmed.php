<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingConfirmed extends Notification implements ShouldQueue
{
    use Queueable;

    public $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $itemType = $this->booking->item_type === 'car' ? 'Car Rental' : 'Driver Service';
        $startDate = $this->booking->start_time->format('M j, Y \a\t g:i A');
        $endDate = $this->booking->end_time->format('M j, Y \a\t g:i A');

        return (new MailMessage)
            ->subject('Booking Confirmed - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Great news! Your booking has been confirmed.')
            ->line('**Booking Details:**')
            ->line('Type: ' . $itemType)
            ->line('Booking ID: #' . $this->booking->id)
            ->line('Start: ' . $startDate)
            ->line('End: ' . $endDate)
            ->line('Total Price: RWF ' . number_format($this->booking->total_price))
            ->action('View Booking Details', url('/bookings/' . $this->booking->id))
            ->line('Please arrive on time and bring a valid ID.')
            ->line('If you have any questions, please contact <NAME_EMAIL> or 0788613669.')
            ->line('Thank you for choosing Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_confirmed',
            'title' => 'Booking Confirmed',
            'message' => 'Your ' . ($this->booking->item_type === 'car' ? 'car rental' : 'driver service') . ' booking has been confirmed.',
            'action_url' => '/bookings/' . $this->booking->id,
        ];
    }
}
