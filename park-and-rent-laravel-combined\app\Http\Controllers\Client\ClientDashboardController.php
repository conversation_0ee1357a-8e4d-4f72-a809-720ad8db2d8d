<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use Illuminate\Http\Request;

class ClientDashboardController extends Controller
{
    /**
     * Display the client dashboard.
     */
    public function index()
    {
        $user = auth()->user();

        // Get user's recent bookings
        $recentBookings = Booking::where('user_id', $user->id)
            ->with(['item'])
            ->latest()
            ->take(5)
            ->get();

        // Get stats
        $stats = [
            'total_bookings' => Booking::where('user_id', $user->id)->count(),
            'active_bookings' => Booking::where('user_id', $user->id)
                ->whereIn('status', ['pending', 'confirmed'])
                ->count(),
            'completed_bookings' => Booking::where('user_id', $user->id)
                ->where('status', 'completed')
                ->count(),
            'total_spent' => Booking::where('user_id', $user->id)
                ->where('status', 'completed')
                ->sum('total_price'),
        ];

        return view('client.dashboard', compact('recentBookings', 'stats'));
    }
}
