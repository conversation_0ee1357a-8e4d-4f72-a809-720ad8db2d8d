@extends('layouts.app')

@section('title', 'My Cars')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Cars</h1>
                <p class="mt-2 text-gray-600">Manage your car listings and bookings</p>
            </div>
            <a href="{{ route('owner.cars.create') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium">
                Add New Car
            </a>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        {{ session('success') }}
                    </p>
                </div>
            </div>
        </div>
        @endif

        @if($cars->count() > 0)
            <!-- Cars Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($cars as $car)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Car Image -->
                    <div class="h-48 bg-gray-200">
                        @if($car->images && count($car->images) > 0)
                            <img class="h-full w-full object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                        @else
                            <div class="h-full w-full flex items-center justify-center bg-gray-300">
                                <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            </div>
                        @endif
                    </div>

                    <!-- Car Info -->
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $car->make }} {{ $car->model }}</h3>
                                <p class="text-sm text-gray-600">{{ $car->year }} • {{ $car->color }}</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($car->is_active)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Car Details -->
                        <div class="grid grid-cols-2 gap-3 text-sm text-gray-600 mb-4">
                            <div>
                                <span class="font-medium">Transmission:</span>
                                <span class="ml-1">{{ ucfirst($car->transmission) }}</span>
                            </div>
                            <div>
                                <span class="font-medium">Fuel:</span>
                                <span class="ml-1">{{ ucfirst($car->fuel_type) }}</span>
                            </div>
                            <div>
                                <span class="font-medium">Seats:</span>
                                <span class="ml-1">{{ $car->seats }}</span>
                            </div>
                            <div>
                                <span class="font-medium">Mileage:</span>
                                <span class="ml-1">{{ number_format($car->mileage) }} km</span>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="mb-4">
                            <div class="text-2xl font-bold text-primary-600">${{ $car->price_per_day }}</div>
                            <div class="text-sm text-gray-600">per day</div>
                            @if($car->price_per_hour)
                            <div class="text-lg font-semibold text-primary-600">${{ $car->price_per_hour }}</div>
                            <div class="text-sm text-gray-600">per hour</div>
                            @endif
                        </div>

                        <!-- Location -->
                        <div class="mb-4 flex items-center text-sm text-gray-600">
                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ $car->location }}
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <a href="{{ route('owner.cars.show', $car) }}" 
                               class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-2 rounded text-sm font-medium text-center">
                                View
                            </a>
                            <a href="{{ route('owner.cars.edit', $car) }}" 
                               class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm font-medium text-center">
                                Edit
                            </a>
                            <form action="{{ route('owner.cars.toggle-status', $car) }}" method="POST" class="flex-1">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="w-full {{ $car->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white px-3 py-2 rounded text-sm font-medium">
                                    {{ $car->is_active ? 'Deactivate' : 'Activate' }}
                                </button>
                            </form>
                        </div>

                        <!-- Delete Button -->
                        <div class="mt-3">
                            <form action="{{ route('owner.cars.destroy', $car) }}" method="POST" 
                                  onsubmit="return confirm('Are you sure you want to delete this car? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm font-medium">
                                    Delete Car
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $cars->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No cars listed</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by adding your first car to the platform.</p>
                <div class="mt-6">
                    <a href="{{ route('owner.cars.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Your First Car
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
