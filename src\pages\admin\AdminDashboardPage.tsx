import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users, Car, UserRound, Calendar, CheckCircle, XCircle,
  AlertTriangle, BarChart2, DollarSign, Search, Filter,
  MapPin, Settings, Edit, Trash2, Eye, Check, X, Plus
} from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useCars } from '../../context/CarContext';
import { useDrivers } from '../../context/DriverContext';
import { useBookings } from '../../context/BookingContext';
import { useAuth } from '../../context/AuthContext';
import { useAdmin } from '../../context/AdminContext';
import Spinner from '../../components/ui/Spinner';
import { User, Car as CarType, Driver, Booking } from '../../types';
import { apiClient } from '../../config/api';
import Input from '../../components/ui/Input';

// Admin role check - check if user has admin role
const isAdmin = (user: User | null) => {
  return user?.role === 'admin';
};

const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cars, updateCar } = useCars();
  const { drivers, updateDriver } = useDrivers();
  const { bookings, updateBookingStatus } = useBookings();
  const {
    stats,
    users,
    cars: adminCars,
    drivers: adminDrivers,
    bookings: adminBookings,
    gpsRequests,
    isLoading: adminLoading,
    error: adminError,
    fetchDashboardStats,
    fetchUsers,
    fetchCars: fetchAdminCars,
    fetchDrivers: fetchAdminDrivers,
    fetchBookings: fetchAdminBookings,
    fetchGpsRequests,
    updateUserRole,
    verifyDriverLicense,
    updateGpsRequestStatus
  } = useAdmin();

  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'cars' | 'drivers' | 'bookings' | 'gps-requests' | 'settings'>('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Modal states
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showDriverModal, setShowDriverModal] = useState(false);
  const [showCarModal, setShowCarModal] = useState(false);
  const [showCreateUserModal, setShowCreateUserModal] = useState(false);
  const [showCreateDriverModal, setShowCreateDriverModal] = useState(false);
  const [showCreateCarModal, setShowCreateCarModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [selectedCar, setSelectedCar] = useState<CarType | null>(null);
  const [editMode, setEditMode] = useState(false);

  // Form states
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Create/Edit form states
  const [userForm, setUserForm] = useState({ name: '', email: '', role: 'client', password: '' });
  const [carForm, setCarForm] = useState({ make: '', model: '', year: '', pricePerHour: '', location: '', description: '', ownerId: '', images: [] as File[] });
  const [driverForm, setDriverForm] = useState({ name: '', age: '', experience: '', location: '', pricePerHour: '', specialties: '' });

  // Get users with owner role for car assignment
  const ownerUsers = users.filter(user => user.role === 'owner');

  // Fetch data when component mounts
  useEffect(() => {
    if (isAdmin(user)) {
      fetchDashboardStats();
      fetchUsers();
      fetchAdminCars();
      fetchAdminDrivers();
      fetchAdminBookings();
      fetchGpsRequests();
    }
  }, [user]);

  // Handler functions
  const handleVerifyDriver = async (driverId: string, status: 'verified' | 'rejected') => {
    try {
      setIsLoading(true);
      await verifyDriverLicense(driverId, status);
      // Refresh drivers data
      await fetchAdminDrivers();
    } catch (error) {
      console.error('Failed to verify driver:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateUserRole = async (userId: string, newRole: string) => {
    try {
      setIsLoading(true);
      await updateUserRole(userId, newRole);
      // Refresh users data
      await fetchUsers();
    } catch (error) {
      console.error('Failed to update user role:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    setPasswordError('');

    if (newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return;
    }

    try {
      setIsLoading(true);
      // Call API to change password
      await apiClient.post('/users/update-password', {
        current_password: currentPassword,
        new_password: newPassword,
        new_password_confirmation: confirmPassword
      });

      // Reset form and close modal
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordModal(false);
      alert('Password changed successfully!');
    } catch (error: any) {
      setPasswordError(error.response?.data?.message || 'Failed to change password');
    } finally {
      setIsLoading(false);
    }
  };

  // CRUD Operations
  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        setIsLoading(true);
        await apiClient.delete(`/admin/users/${userId}`);
        await fetchUsers();
      } catch (error) {
        console.error('Failed to delete user:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDeleteDriver = async (driverId: string) => {
    if (window.confirm('Are you sure you want to delete this driver?')) {
      try {
        setIsLoading(true);
        await apiClient.delete(`/admin/drivers/${driverId}`);
        await fetchAdminDrivers();
      } catch (error) {
        console.error('Failed to delete driver:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDeleteCar = async (carId: string) => {
    if (window.confirm('Are you sure you want to delete this car?')) {
      try {
        setIsLoading(true);
        await apiClient.delete(`/admin/cars/${carId}`);
        await fetchAdminCars();
      } catch (error) {
        console.error('Failed to delete car:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Create/Edit handlers
  const handleCreateUser = async () => {
    try {
      setIsLoading(true);
      await apiClient.post('/admin/users', userForm);
      await fetchUsers();
      setShowCreateUserModal(false);
      setUserForm({ name: '', email: '', role: 'client', password: '' });
    } catch (error) {
      console.error('Failed to create user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = async () => {
    if (!selectedUser) return;
    try {
      setIsLoading(true);
      await apiClient.put(`/admin/users/${selectedUser.id}`, userForm);
      await fetchUsers();
      setShowUserModal(false);
      setEditMode(false);
      setSelectedUser(null);
      setUserForm({ name: '', email: '', role: 'client', password: '' });
    } catch (error) {
      console.error('Failed to update user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCar = async () => {
    try {
      setIsLoading(true);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('make', carForm.make);
      formData.append('model', carForm.model);
      formData.append('year', carForm.year);
      formData.append('pricePerHour', carForm.pricePerHour);
      formData.append('location', carForm.location);
      formData.append('description', carForm.description);
      formData.append('ownerId', carForm.ownerId);

      // Add images
      carForm.images.forEach((image, index) => {
        formData.append(`images[${index}]`, image);
      });

      await apiClient.post('/admin/cars', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      await fetchAdminCars();
      setShowCreateCarModal(false);
      setCarForm({ make: '', model: '', year: '', pricePerHour: '', location: '', description: '', ownerId: '', images: [] });
    } catch (error) {
      console.error('Failed to create car:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditCar = async () => {
    if (!selectedCar) return;
    try {
      setIsLoading(true);
      await apiClient.put(`/admin/cars/${selectedCar.id}`, carForm);
      await fetchAdminCars();
      setShowCarModal(false);
      setEditMode(false);
      setSelectedCar(null);
      setCarForm({ make: '', model: '', year: '', pricePerHour: '', location: '', description: '' });
    } catch (error) {
      console.error('Failed to update car:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateDriver = async () => {
    try {
      setIsLoading(true);
      await apiClient.post('/admin/drivers', driverForm);
      await fetchAdminDrivers();
      setShowCreateDriverModal(false);
      setDriverForm({ name: '', age: '', experience: '', location: '', pricePerHour: '', specialties: '' });
    } catch (error) {
      console.error('Failed to create driver:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditDriver = async () => {
    if (!selectedDriver) return;
    try {
      setIsLoading(true);
      await apiClient.put(`/admin/drivers/${selectedDriver.id}`, driverForm);
      await fetchAdminDrivers();
      setShowDriverModal(false);
      setEditMode(false);
      setSelectedDriver(null);
      setDriverForm({ name: '', age: '', experience: '', location: '', pricePerHour: '', specialties: '' });
    } catch (error) {
      console.error('Failed to update driver:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Activation/Deactivation handlers
  const handleToggleCarStatus = async (carId: string, currentStatus: boolean) => {
    try {
      setIsLoading(true);
      await apiClient.post(`/admin/cars/${carId}/toggle-status`, {
        is_active: !currentStatus
      });
      await fetchAdminCars();
    } catch (error) {
      console.error('Failed to toggle car status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleDriverStatus = async (driverId: string, currentStatus: boolean) => {
    try {
      setIsLoading(true);
      await apiClient.post(`/admin/drivers/${driverId}/toggle-status`, {
        is_active: !currentStatus
      });
      await fetchAdminDrivers();
    } catch (error) {
      console.error('Failed to toggle driver status:', error);
    } finally {
      setIsLoading(false);
    }
  };



  // Stats for overview - use real data from backend
  const totalUsers = stats?.users_count || 0;
  const totalCars = stats?.cars_count || 0;
  const totalDrivers = stats?.drivers_count || 0;
  const totalBookings = stats?.bookings_count || 0;
  const pendingVerifications = stats?.pending_driver_verifications || 0;

  // Filtered lists based on search and filters
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredCars = cars.filter(car =>
    (car.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' ||
     (statusFilter === 'active' && car.isActive) ||
     (statusFilter === 'inactive' && !car.isActive))
  );

  const filteredDrivers = drivers.filter(driver =>
    (driver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     driver.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' ||
     (statusFilter === 'active' && driver.isAvailable) ||
     (statusFilter === 'inactive' && !driver.isAvailable) ||
     (statusFilter === 'pending' && driver.licenseVerificationStatus === 'pending'))
  );

  const filteredBookings = bookings.filter(booking =>
    (statusFilter === 'all' || booking.status === statusFilter)
  );

  const handleVerifyLicense = async (userId: string, type: 'user' | 'driver', id: string, status: 'verified' | 'rejected') => {
    setIsLoading(true);
    try {
      if (type === 'driver') {
        await verifyDriverLicense(id, status);
      } else {
        // For user license verification, we would need a separate API endpoint
        console.log('User license verification not implemented yet');
      }
    } catch (error) {
      console.error('Failed to update verification status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateBookingStatus = async (bookingId: string, status: Booking['status']) => {
    setIsLoading(true);
    try {
      await updateBookingStatus(bookingId, status);
    } catch (error) {
      console.error('Failed to update booking status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user || !isAdmin(user)) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Access Denied</h2>
            <p className="text-error-600">
              You must be logged in as an administrator to access this page.
            </p>
            <div className="mt-4 text-sm text-gray-600">
              <p>Current user: {user?.name || 'Not logged in'}</p>
              <p>Role: {user?.role || 'No role'}</p>
              <p className="mt-2">Please login with admin credentials:</p>
              <p>Email: <EMAIL></p>
              <p>Password: password</p>
            </div>
            <Button
              className="mt-4"
              onClick={() => navigate('/login')}
            >
              Log In
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
            <p className="text-gray-600">
              Manage users, cars, drivers, and bookings.
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex flex-wrap border-b border-gray-200 overflow-x-auto">
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'overview'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              <BarChart2 size={16} className="inline-block mr-1" />
              Overview
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'users'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('users')}
            >
              <Users size={16} className="inline-block mr-1" />
              Users
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'cars'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('cars')}
            >
              <Car size={16} className="inline-block mr-1" />
              Cars
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'drivers'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('drivers')}
            >
              <UserRound size={16} className="inline-block mr-1" />
              Drivers
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'bookings'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('bookings')}
            >
              <Calendar size={16} className="inline-block mr-1" />
              Bookings
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'gps-requests'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('gps-requests')}
            >
              <MapPin size={16} className="inline-block mr-1" />
              GPS Requests
            </button>
            <button
              className={`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${
                activeTab === 'settings'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('settings')}
            >
              <Settings size={16} className="inline-block mr-1" />
              Settings
            </button>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Platform Overview</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-blue-800">Total Users</h3>
                    <Users size={24} className="text-blue-500" />
                  </div>
                  <p className="text-3xl font-bold text-blue-600">{totalUsers}</p>
                  <div className="mt-2 text-sm text-blue-600">
                    <span className="font-medium">{users.filter(u => u.role === 'client').length}</span> Clients,
                    <span className="font-medium ml-1">{users.filter(u => u.role === 'owner').length}</span> Owners,
                    <span className="font-medium ml-1">{users.filter(u => u.role === 'driver').length}</span> Drivers
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-green-800">Total Cars</h3>
                    <Car size={24} className="text-green-500" />
                  </div>
                  <p className="text-3xl font-bold text-green-600">{totalCars}</p>
                  <div className="mt-2 text-sm text-green-600">
                    <span className="font-medium">{adminCars.filter(c => c.isActive).length}</span> Active,
                    <span className="font-medium ml-1">{adminCars.filter(c => !c.isActive).length}</span> Inactive
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-purple-800">Total Drivers</h3>
                    <UserRound size={24} className="text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold text-purple-600">{totalDrivers}</p>
                  <div className="mt-2 text-sm text-purple-600">
                    <span className="font-medium">{adminDrivers.filter(d => d.isAvailable).length}</span> Available,
                    <span className="font-medium ml-1">{adminDrivers.filter(d => !d.isAvailable).length}</span> Unavailable
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-yellow-800">Total Bookings</h3>
                    <Calendar size={24} className="text-yellow-500" />
                  </div>
                  <p className="text-3xl font-bold text-yellow-600">{totalBookings}</p>
                  <div className="mt-2 text-sm text-yellow-600">
                    <span className="font-medium">{stats?.completed_bookings || 0}</span> Completed,
                    <span className="font-medium ml-1">{stats?.pending_bookings || 0}</span> Pending
                  </div>
                </div>
              </div>

              <div className="bg-orange-50 rounded-lg p-6 mb-8">
                <div className="flex items-center mb-4">
                  <AlertTriangle size={24} className="text-orange-500 mr-2" />
                  <h3 className="text-lg font-medium text-orange-800">Pending Verifications</h3>
                </div>
                <p className="text-3xl font-bold text-orange-600 mb-2">{pendingVerifications}</p>
                <p className="text-sm text-orange-700">
                  {pendingVerifications} users are waiting for license verification. Review these to maintain platform safety.
                </p>
                <Button
                  variant="outline"
                  className="mt-4 border-orange-500 text-orange-700 hover:bg-orange-100"
                  onClick={() => setActiveTab('users')}
                >
                  Review Verifications
                </Button>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {(stats?.recent_bookings || []).slice(0, 5).map((booking, index) => (
                    <div key={index} className="flex items-start">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        booking.status === 'completed' ? 'bg-success-100' :
                        booking.status === 'pending' ? 'bg-yellow-100' :
                        booking.status === 'confirmed' ? 'bg-blue-100' : 'bg-error-100'
                      }`}>
                        <Calendar size={16} className={`${
                          booking.status === 'completed' ? 'text-success-600' :
                          booking.status === 'pending' ? 'text-yellow-600' :
                          booking.status === 'confirmed' ? 'text-blue-600' : 'text-error-600'
                        }`} />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          New {booking.itemType} booking ({booking.status})
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(booking.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Users Tab */}
          {activeTab === 'users' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center gap-4">
                  <h2 className="text-xl font-bold text-gray-900">Users Management</h2>
                  <div className="text-sm text-gray-600">
                    Total: {users.length}
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Button
                    onClick={() => setShowCreateUserModal(true)}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Users size={16} />
                    Create User
                  </Button>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search users..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Joined
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        License Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              {user.licenseImageUrl ? (
                                <img
                                  src={user.licenseImageUrl}
                                  alt={user.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              ) : (
                                <UserRound size={20} className="text-gray-500" />
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.role === 'client'
                              ? 'bg-blue-100 text-blue-800'
                              : user.role === 'owner'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-purple-100 text-purple-800'
                          }`}>
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {user.licenseVerificationStatus ? (
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              user.licenseVerificationStatus === 'verified'
                                ? 'bg-success-100 text-success-800'
                                : user.licenseVerificationStatus === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-error-100 text-error-800'
                            }`}>
                              {user.licenseVerificationStatus.charAt(0).toUpperCase() +
                               user.licenseVerificationStatus.slice(1)}
                            </span>
                          ) : (
                            <span className="text-gray-500 text-sm">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              className="text-blue-600 hover:text-blue-800"
                              onClick={() => {
                                setSelectedUser(user);
                                setShowUserModal(true);
                              }}
                              title="View Details"
                            >
                              <Eye size={16} />
                            </button>
                            <button
                              className="text-green-600 hover:text-green-800"
                              onClick={() => {
                                setSelectedUser(user);
                                setUserForm({
                                  name: user.name,
                                  email: user.email,
                                  role: user.role,
                                  password: ''
                                });
                                setEditMode(true);
                                setShowUserModal(true);
                              }}
                              title="Edit User"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800"
                              onClick={() => handleDeleteUser(user.id)}
                              disabled={isLoading}
                              title="Delete User"
                            >
                              <Trash2 size={16} />
                            </button>
                            <select
                              value={user.role}
                              onChange={(e) => handleUpdateUserRole(user.id, e.target.value)}
                              className="text-sm border border-gray-300 rounded px-2 py-1"
                              disabled={isLoading}
                            >
                              <option value="client">Client</option>
                              <option value="owner">Owner</option>
                              <option value="driver">Driver</option>
                              <option value="admin">Admin</option>
                            </select>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Cars Tab */}
          {activeTab === 'cars' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Cars Management</h2>
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-600">
                    Total Cars: {adminCars.length}
                  </div>
                  <Button
                    onClick={() => setShowCreateCarModal(true)}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Car size={16} />
                    Create Car
                  </Button>
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminCars.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car</th>
                        <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                        <th className="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Hour</th>
                        <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminCars.map((car) => (
                        <tr key={car.id}>
                          <td className="px-3 md:px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <Car className="h-5 w-5 text-gray-600" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {car.make} {car.model}
                                </div>
                                <div className="text-sm text-gray-500">{car.year}</div>
                                <div className="md:hidden text-xs text-gray-500 mt-1">
                                  Owner #{car.ownerId} • {car.location}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Owner #{car.ownerId}
                          </td>
                          <td className="hidden lg:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {car.location}
                          </td>
                          <td className="px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${car.pricePerHour}/hr
                          </td>
                          <td className="px-3 md:px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              car.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {car.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-3 md:px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => {
                                  setSelectedCar(car);
                                  setShowCarModal(true);
                                }}
                                title="View Details"
                              >
                                <Eye size={16} />
                              </button>
                              <button
                                className="text-green-600 hover:text-green-800"
                                onClick={() => {
                                  setSelectedCar(car);
                                  setCarForm({
                                    make: car.make,
                                    model: car.model,
                                    year: car.year.toString(),
                                    pricePerHour: car.pricePerHour.toString(),
                                    location: car.location,
                                    description: car.description || ''
                                  });
                                  setEditMode(true);
                                  setShowCarModal(true);
                                }}
                                title="Edit Car"
                              >
                                <Edit size={16} />
                              </button>
                              <button
                                className={`${car.isActive ? 'text-orange-600 hover:text-orange-800' : 'text-green-600 hover:text-green-800'}`}
                                onClick={() => handleToggleCarStatus(car.id, car.isActive)}
                                disabled={isLoading}
                                title={car.isActive ? 'Deactivate Car' : 'Activate Car'}
                              >
                                {car.isActive ? <XCircle size={16} /> : <CheckCircle size={16} />}
                              </button>
                              <button
                                className="text-red-600 hover:text-red-800"
                                onClick={() => handleDeleteCar(car.id)}
                                disabled={isLoading}
                                title="Delete Car"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Car size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No cars found</p>
                </div>
              )}
            </div>
          )}

          {/* Drivers Tab */}
          {activeTab === 'drivers' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Drivers Management</h2>
                <div className="text-sm text-gray-600">
                  Total Drivers: {adminDrivers.length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminDrivers.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Driver</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Experience</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Hour</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">License Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminDrivers.map((driver) => (
                        <tr key={driver.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <UserRound className="h-5 w-5 text-gray-600" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {driver.name}
                                </div>
                                <div className="text-sm text-gray-500">Age: {driver.age}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {driver.experience} years
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {driver.location}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${driver.pricePerHour}/hr
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              driver.licenseVerificationStatus === 'verified' ? 'bg-green-100 text-green-800' :
                              driver.licenseVerificationStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {driver.licenseVerificationStatus}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => {
                                  setSelectedDriver(driver);
                                  setShowDriverModal(true);
                                }}
                                title="View Details"
                              >
                                <Eye size={16} />
                              </button>
                              {driver.licenseVerificationStatus === 'pending' && (
                                <>
                                  <button
                                    className="text-green-600 hover:text-green-800"
                                    onClick={() => handleVerifyDriver(driver.id, 'verified')}
                                    disabled={isLoading}
                                    title="Approve Driver"
                                  >
                                    <CheckCircle size={16} />
                                  </button>
                                  <button
                                    className="text-red-600 hover:text-red-800"
                                    onClick={() => handleVerifyDriver(driver.id, 'rejected')}
                                    disabled={isLoading}
                                    title="Reject Driver"
                                  >
                                    <XCircle size={16} />
                                  </button>
                                </>
                              )}
                              <button
                                className={`${driver.isActive ? 'text-orange-600 hover:text-orange-800' : 'text-green-600 hover:text-green-800'}`}
                                onClick={() => handleToggleDriverStatus(driver.id, driver.isActive)}
                                disabled={isLoading}
                                title={driver.isActive ? 'Deactivate Driver' : 'Activate Driver'}
                              >
                                {driver.isActive ? <XCircle size={16} /> : <CheckCircle size={16} />}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <UserRound size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No drivers found</p>
                </div>
              )}
            </div>
          )}

          {/* Bookings Tab */}
          {activeTab === 'bookings' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Bookings Management</h2>
                <div className="text-sm text-gray-600">
                  Total Bookings: {adminBookings.length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : adminBookings.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminBookings.map((booking) => (
                        <tr key={booking.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{booking.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            User #{booking.userId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {booking.itemType} #{booking.itemId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(booking.startTime).toLocaleDateString()} - {new Date(booking.endTime).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${booking.totalPrice}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              booking.status === 'completed' ? 'bg-green-100 text-green-800' :
                              booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {booking.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => {
                                  setSelectedCar(car);
                                  setShowCarModal(true);
                                }}
                                title="View Details"
                              >
                                <Eye size={16} />
                              </button>
                              <button
                                className="text-green-600 hover:text-green-800"
                                onClick={() => {
                                  setSelectedCar(car);
                                  setEditMode(true);
                                  setShowCarModal(true);
                                }}
                                title="Edit Car"
                              >
                                <Edit size={16} />
                              </button>
                              <button
                                className="text-red-600 hover:text-red-800"
                                onClick={() => handleDeleteCar(car.id)}
                                disabled={isLoading}
                                title="Delete Car"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No bookings found</p>
                </div>
              )}
            </div>
          )}

          {/* GPS Requests Tab */}
          {activeTab === 'gps-requests' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">GPS Installation Requests</h2>
                <div className="text-sm text-gray-600">
                  Pending Requests: {gpsRequests.filter(req => req.status === 'pending').length}
                </div>
              </div>

              {adminLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : adminError ? (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                  <p className="text-error-600">{adminError}</p>
                </div>
              ) : gpsRequests.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {gpsRequests.map((request) => (
                        <tr key={request.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{request.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.user.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.car_make} {request.car_model} ({request.car_year})
                            <br />
                            <span className="text-xs text-gray-500">{request.license_plate}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.contact_phone}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              request.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : request.status === 'approved'
                                ? 'bg-blue-100 text-blue-800'
                                : request.status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(request.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              {request.status === 'pending' && (
                                <>
                                  <button
                                    className="text-green-600 hover:text-green-800"
                                    onClick={() => updateGpsRequestStatus(request.id, 'approved')}
                                    disabled={isLoading}
                                    title="Approve Request"
                                  >
                                    <Check size={16} />
                                  </button>
                                  <button
                                    className="text-red-600 hover:text-red-800"
                                    onClick={() => updateGpsRequestStatus(request.id, 'rejected')}
                                    disabled={isLoading}
                                    title="Reject Request"
                                  >
                                    <X size={16} />
                                  </button>
                                </>
                              )}
                              {request.status === 'approved' && (
                                <button
                                  className="text-blue-600 hover:text-blue-800"
                                  onClick={() => updateGpsRequestStatus(request.id, 'completed')}
                                  disabled={isLoading}
                                  title="Mark as Completed"
                                >
                                  <Check size={16} />
                                </button>
                              )}
                              <button
                                className="text-gray-600 hover:text-gray-800"
                                onClick={() => {
                                  alert(`Reason: ${request.reason}\n\nPreferred Date: ${request.preferred_installation_date || 'Not specified'}\n\nAdmin Notes: ${request.admin_notes || 'None'}`);
                                }}
                                title="View Details"
                              >
                                <Eye size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <MapPin size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No GPS installation requests found</p>
                  <p className="text-sm text-gray-500 mt-2">
                    GPS installation requests will appear here when users request GPS tracking for their vehicles.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Admin Settings</h2>
              </div>

              <div className="max-w-md">
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                  <Button
                    onClick={() => setShowPasswordModal(true)}
                    className="w-full"
                  >
                    Change Admin Password
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Password Change Modal */}
        {showPasswordModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Password
                  </label>
                  <Input
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder="Enter current password"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    New Password
                  </label>
                  <Input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter new password"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm New Password
                  </label>
                  <Input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                  />
                </div>

                {passwordError && (
                  <div className="text-red-600 text-sm">
                    {passwordError}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowPasswordModal(false);
                    setCurrentPassword('');
                    setNewPassword('');
                    setConfirmPassword('');
                    setPasswordError('');
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePasswordChange}
                  disabled={isLoading || !currentPassword || !newPassword || !confirmPassword}
                >
                  {isLoading ? 'Changing...' : 'Change Password'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* User Details Modal */}
        {showUserModal && selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">User Details</h3>

              <div className="space-y-3">
                <div><strong>Name:</strong> {selectedUser.name}</div>
                <div><strong>Email:</strong> {selectedUser.email}</div>
                <div><strong>Role:</strong> {selectedUser.role}</div>
                <div><strong>Created:</strong> {new Date(selectedUser.createdAt).toLocaleDateString()}</div>
                {selectedUser.licenseVerificationStatus && (
                  <div><strong>License Status:</strong> {selectedUser.licenseVerificationStatus}</div>
                )}
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowUserModal(false);
                    setSelectedUser(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Driver Details Modal */}
        {showDriverModal && selectedDriver && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Driver Details</h3>

              <div className="space-y-3">
                <div><strong>Name:</strong> {selectedDriver.name}</div>
                <div><strong>Age:</strong> {selectedDriver.age}</div>
                <div><strong>Experience:</strong> {selectedDriver.experience} years</div>
                <div><strong>Location:</strong> {selectedDriver.location}</div>
                <div><strong>Price/Hour:</strong> ${selectedDriver.pricePerHour}</div>
                <div><strong>License Status:</strong> {selectedDriver.licenseVerificationStatus}</div>
                <div><strong>Specialties:</strong> {selectedDriver.specialties.join(', ')}</div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                {selectedDriver.licenseVerificationStatus === 'pending' && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        handleVerifyDriver(selectedDriver.id, 'rejected');
                        setShowDriverModal(false);
                        setSelectedDriver(null);
                      }}
                      disabled={isLoading}
                    >
                      Reject
                    </Button>
                    <Button
                      onClick={() => {
                        handleVerifyDriver(selectedDriver.id, 'verified');
                        setShowDriverModal(false);
                        setSelectedDriver(null);
                      }}
                      disabled={isLoading}
                    >
                      Approve
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDriverModal(false);
                    setSelectedDriver(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Create User Modal */}
        {showCreateUserModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create New User</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <Input
                    value={userForm.name}
                    onChange={(e) => setUserForm({...userForm, name: e.target.value})}
                    placeholder="Enter user name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <Input
                    type="email"
                    value={userForm.email}
                    onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                    placeholder="Enter email address"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <select
                    value={userForm.role}
                    onChange={(e) => setUserForm({...userForm, role: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="client">Client</option>
                    <option value="owner">Owner</option>
                    <option value="driver">Driver</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                  <Input
                    type="password"
                    value={userForm.password}
                    onChange={(e) => setUserForm({...userForm, password: e.target.value})}
                    placeholder="Enter password"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCreateUserModal(false);
                    setUserForm({ name: '', email: '', role: 'client', password: '' });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateUser}
                  disabled={isLoading || !userForm.name || !userForm.email || !userForm.password}
                >
                  {isLoading ? 'Creating...' : 'Create User'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Edit User Modal */}
        {showUserModal && editMode && selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Edit User</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <Input
                    value={userForm.name}
                    onChange={(e) => setUserForm({...userForm, name: e.target.value})}
                    placeholder="Enter user name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <Input
                    type="email"
                    value={userForm.email}
                    onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                    placeholder="Enter email address"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <select
                    value={userForm.role}
                    onChange={(e) => setUserForm({...userForm, role: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="client">Client</option>
                    <option value="owner">Owner</option>
                    <option value="driver">Driver</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">New Password (optional)</label>
                  <Input
                    type="password"
                    value={userForm.password}
                    onChange={(e) => setUserForm({...userForm, password: e.target.value})}
                    placeholder="Leave blank to keep current password"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowUserModal(false);
                    setEditMode(false);
                    setSelectedUser(null);
                    setUserForm({ name: '', email: '', role: 'client', password: '' });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEditUser}
                  disabled={isLoading || !userForm.name || !userForm.email}
                >
                  {isLoading ? 'Updating...' : 'Update User'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Create Car Modal */}
        {showCreateCarModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Car</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Make</label>
                  <Input
                    value={carForm.make}
                    onChange={(e) => setCarForm({...carForm, make: e.target.value})}
                    placeholder="Enter car make (e.g., Toyota)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
                  <Input
                    value={carForm.model}
                    onChange={(e) => setCarForm({...carForm, model: e.target.value})}
                    placeholder="Enter car model (e.g., Camry)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
                  <Input
                    type="number"
                    value={carForm.year}
                    onChange={(e) => setCarForm({...carForm, year: e.target.value})}
                    placeholder="Enter year (e.g., 2022)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Price per Hour ($)</label>
                  <Input
                    type="number"
                    value={carForm.pricePerHour}
                    onChange={(e) => setCarForm({...carForm, pricePerHour: e.target.value})}
                    placeholder="Enter price per hour"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                  <Input
                    value={carForm.location}
                    onChange={(e) => setCarForm({...carForm, location: e.target.value})}
                    placeholder="Enter location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assign to Owner</label>
                  <select
                    value={carForm.ownerId}
                    onChange={(e) => setCarForm({...carForm, ownerId: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  >
                    <option value="">Select an owner</option>
                    {ownerUsers.map(owner => (
                      <option key={owner.id} value={owner.id}>
                        {owner.name} ({owner.email})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Car Images</label>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      setCarForm({...carForm, images: files});
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Select multiple images for the car</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={carForm.description}
                    onChange={(e) => setCarForm({...carForm, description: e.target.value})}
                    placeholder="Enter car description"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCreateCarModal(false);
                    setCarForm({ make: '', model: '', year: '', pricePerHour: '', location: '', description: '' });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateCar}
                  disabled={isLoading || !carForm.make || !carForm.model || !carForm.year || !carForm.pricePerHour || !carForm.ownerId}
                >
                  {isLoading ? 'Creating...' : 'Create Car'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Car Modal */}
        {showCarModal && editMode && selectedCar && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Car</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Make</label>
                  <Input
                    value={carForm.make}
                    onChange={(e) => setCarForm({...carForm, make: e.target.value})}
                    placeholder="Enter car make"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
                  <Input
                    value={carForm.model}
                    onChange={(e) => setCarForm({...carForm, model: e.target.value})}
                    placeholder="Enter car model"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
                  <Input
                    type="number"
                    value={carForm.year}
                    onChange={(e) => setCarForm({...carForm, year: e.target.value})}
                    placeholder="Enter year"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Price per Hour ($)</label>
                  <Input
                    type="number"
                    value={carForm.pricePerHour}
                    onChange={(e) => setCarForm({...carForm, pricePerHour: e.target.value})}
                    placeholder="Enter price per hour"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                  <Input
                    value={carForm.location}
                    onChange={(e) => setCarForm({...carForm, location: e.target.value})}
                    placeholder="Enter location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={carForm.description}
                    onChange={(e) => setCarForm({...carForm, description: e.target.value})}
                    placeholder="Enter car description"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCarModal(false);
                    setEditMode(false);
                    setSelectedCar(null);
                    setCarForm({ make: '', model: '', year: '', pricePerHour: '', location: '', description: '' });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEditCar}
                  disabled={isLoading || !carForm.make || !carForm.model || !carForm.year || !carForm.pricePerHour}
                >
                  {isLoading ? 'Updating...' : 'Update Car'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default AdminDashboardPage;
