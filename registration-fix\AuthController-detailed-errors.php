<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;
use App\Notifications\WelcomeUser;

class AuthController extends Controller
{
    /**
     * Register a new user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Password::defaults()],
            'role' => 'required|in:client,owner,driver',
            'phone_number' => 'nullable|string|max:20',
            'license_image_url' => 'nullable|string',
        ], [
            // Custom error messages
            'name.required' => 'Please enter your full name.',
            'name.string' => 'Name must be a valid text.',
            'name.max' => 'Name cannot be longer than 255 characters.',
            
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered. Please use a different email or try logging in.',
            'email.max' => 'Email address cannot be longer than 255 characters.',
            
            'password.required' => 'Please enter a password.',
            'password.confirmed' => 'Password confirmation does not match. Please make sure both passwords are identical.',
            'password.min' => 'Password must be at least 8 characters long.',
            
            'role.required' => 'Please select your account type (Client, Owner, or Driver).',
            'role.in' => 'Please select a valid account type: Client, Owner, or Driver.',
            
            'phone_number.string' => 'Phone number must be a valid text.',
            'phone_number.max' => 'Phone number cannot be longer than 20 characters.',
            
            'license_image_url.string' => 'License image must be a valid URL.',
        ]);

        if ($validator->fails()) {
            // Get the first error for each field
            $errors = $validator->errors();
            $detailedErrors = [];
            $mainMessage = 'Please fix the following issues:';
            
            foreach ($errors->toArray() as $field => $fieldErrors) {
                $detailedErrors[$field] = $fieldErrors[0]; // Get first error for each field
            }
            
            // Create a user-friendly main message based on the primary error
            if ($errors->has('email') && str_contains($errors->first('email'), 'already registered')) {
                $mainMessage = 'This email address is already registered.';
            } elseif ($errors->has('password')) {
                $mainMessage = 'There is an issue with your password.';
            } elseif ($errors->has('name')) {
                $mainMessage = 'Please check your name.';
            } elseif ($errors->has('role')) {
                $mainMessage = 'Please select a valid account type.';
            }
            
            return response()->json([
                'success' => false,
                'message' => $mainMessage,
                'errors' => $detailedErrors,
                'error_type' => 'validation_failed',
                'suggestions' => $this->getErrorSuggestions($errors)
            ], 422);
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'phone_number' => $request->phone_number,
                'license_image_url' => $request->license_image_url,
                'license_verification_status' => null,
                'email_verified_at' => now(), // Auto-verify users on registration
            ]);

            // Send welcome email - DISABLED to avoid SMTP issues
            // $user->notify(new WelcomeUser($user));

            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Registration successful! Welcome to Park & Rent.',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'phone_number' => $user->phone_number,
                ],
                'token' => $token,
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed due to a server error.',
                'error' => 'Unable to create your account at this time. Please try again later.',
                'error_type' => 'server_error'
            ], 500);
        }
    }

    /**
     * Get helpful suggestions based on validation errors
     */
    private function getErrorSuggestions($errors)
    {
        $suggestions = [];
        
        if ($errors->has('email') && str_contains($errors->first('email'), 'already registered')) {
            $suggestions[] = 'Try logging in instead of registering.';
            $suggestions[] = 'Use a different email address.';
            $suggestions[] = 'Contact support if you forgot your password.';
        }
        
        if ($errors->has('password')) {
            if (str_contains($errors->first('password'), 'confirmation')) {
                $suggestions[] = 'Make sure both password fields are identical.';
            } elseif (str_contains($errors->first('password'), '8 characters')) {
                $suggestions[] = 'Use a password with at least 8 characters.';
                $suggestions[] = 'Include letters, numbers, and special characters for security.';
            }
        }
        
        if ($errors->has('role')) {
            $suggestions[] = 'Choose "Client" if you want to rent cars.';
            $suggestions[] = 'Choose "Owner" if you want to rent out your cars.';
            $suggestions[] = 'Choose "Driver" if you want to offer driving services.';
        }
        
        return $suggestions;
    }

    /**
     * Login user and create token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ], [
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'password.required' => 'Please enter your password.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your login information.',
                'errors' => $validator->errors(),
                'error_type' => 'validation_failed'
            ], 422);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed',
                'error' => 'Invalid email or password. Please check your credentials and try again.',
                'error_type' => 'authentication_failed',
                'suggestions' => [
                    'Double-check your email address.',
                    'Make sure your password is correct.',
                    'Try resetting your password if you forgot it.',
                    'Contact support if you continue having issues.'
                ]
            ], 401);
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'phone_number' => $user->phone_number,
            ],
            'token' => $token,
        ]);
    }

    /**
     * Logout user (revoke the token).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Logout failed',
                'error' => 'Unable to logout at this time. Please try again.',
                'error_type' => 'server_error'
            ], 500);
        }
    }

    /**
     * Get the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        try {
            return response()->json([
                'success' => true,
                'user' => $request->user()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to retrieve user information',
                'error_type' => 'server_error'
            ], 500);
        }
    }

    /**
     * Update user password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'new_password' => 'required|min:8',
            'new_password_confirmation' => 'required|same:new_password',
        ], [
            'current_password.required' => 'Please enter your current password.',
            'new_password.required' => 'Please enter a new password.',
            'new_password.min' => 'New password must be at least 8 characters long.',
            'new_password_confirmation.required' => 'Please confirm your new password.',
            'new_password_confirmation.same' => 'New password confirmation does not match.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your password information.',
                'errors' => $validator->errors(),
                'error_type' => 'validation_failed'
            ], 422);
        }

        $user = $request->user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect',
                'error' => 'The current password you entered does not match our records.',
                'error_type' => 'authentication_failed',
                'suggestions' => [
                    'Double-check your current password.',
                    'Make sure Caps Lock is not on.',
                    'Contact support if you forgot your current password.'
                ]
            ], 400);
        }

        try {
            // Update password
            $user->password = Hash::make($request->new_password);
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Password updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Password update failed',
                'error' => 'Unable to update your password at this time. Please try again.',
                'error_type' => 'server_error'
            ], 500);
        }
    }
}
