import React, { useEffect } from 'react';
import MainLayout from '../components/layout/MainLayout';
import { useDrivers } from '../context/DriverContext';
import DriverCard from '../components/features/DriverCard';
import DriverFilters from '../components/features/DriverFilters';
import Spinner from '../components/ui/Spinner';
import Button from '../components/ui/Button';
import { UserRound, RefreshCw } from 'lucide-react';

const DriversListPage: React.FC = () => {
  const { filteredDrivers, isLoading, error, fetchDrivers, filterDrivers } = useDrivers();

  useEffect(() => {
    // Scroll to top
    window.scrollTo(0, 0);
  }, []);

  const handleRefresh = () => {
    fetchDrivers();
  };

  const handleSearch = (query: string) => {
    filterDrivers(query);
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-6 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Error Loading Drivers</h2>
            <p className="text-error-600 mb-4">{error}</p>
            <Button
              onClick={handleRefresh}
              disabled={isLoading}
              className="inline-flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Retrying...' : 'Try Again'}
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Hire a Driver</h1>
            <p className="text-gray-600">
              Find professional drivers to help you get around safely and comfortably.
            </p>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            className="inline-flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <DriverFilters onSearch={handleSearch} />

        {filteredDrivers.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDrivers.map((driver) => (
              <DriverCard key={driver.id} driver={driver} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
            <UserRound size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No drivers match your search</h3>
            <p className="text-gray-600">
              Try adjusting your filters or search term to find available drivers.
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default DriversListPage;
