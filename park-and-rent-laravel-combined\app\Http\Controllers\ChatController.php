<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\Message;
use App\Models\User;
use App\Models\Car;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChatController extends Controller
{
    /**
     * Display a listing of user's chats.
     */
    public function index()
    {
        $userId = auth()->id();
        
        $chats = Chat::where(function($query) use ($userId) {
            $query->where('user_id', $userId)
                  ->orWhere('recipient_id', $userId);
        })
        ->with(['user', 'recipient', 'relatedTo', 'messages' => function($query) {
            $query->latest()->limit(1);
        }])
        ->orderBy('last_message_at', 'desc')
        ->get();

        return view('chat.index', compact('chats'));
    }

    /**
     * Show a specific chat conversation.
     */
    public function show(Chat $chat)
    {
        $userId = auth()->id();
        
        // Check if user is part of this chat
        if ($chat->user_id !== $userId && $chat->recipient_id !== $userId) {
            abort(403, 'You are not authorized to view this chat.');
        }

        // Mark messages as read
        $chat->messages()
            ->where('sender_id', '!=', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        // Load chat with relationships
        $chat->load(['user', 'recipient', 'relatedTo', 'messages.sender']);

        // Get other participant
        $otherUser = $chat->user_id === $userId ? $chat->recipient : $chat->user;

        return view('chat.show', compact('chat', 'otherUser'));
    }

    /**
     * Start a new chat or get existing chat.
     */
    public function startChat(Request $request)
    {
        $request->validate([
            'recipient_id' => 'required|exists:users,id',
            'related_to_type' => 'nullable|string',
            'related_to_id' => 'nullable|integer',
            'message' => 'required|string|max:1000',
        ]);

        $userId = auth()->id();
        $recipientId = $request->recipient_id;

        // Don't allow chatting with yourself
        if ($userId === $recipientId) {
            return redirect()->back()->with('error', 'You cannot start a chat with yourself.');
        }

        // Check if chat already exists
        $existingChat = Chat::where(function($query) use ($userId, $recipientId) {
            $query->where('user_id', $userId)->where('recipient_id', $recipientId);
        })->orWhere(function($query) use ($userId, $recipientId) {
            $query->where('user_id', $recipientId)->where('recipient_id', $userId);
        })->first();

        if ($existingChat) {
            // Add message to existing chat
            $this->addMessage($existingChat, $request->message);
            return redirect()->route('chat.show', $existingChat);
        }

        // Create new chat
        $chat = Chat::create([
            'user_id' => $userId,
            'recipient_id' => $recipientId,
            'related_to_type' => $request->related_to_type,
            'related_to_id' => $request->related_to_id,
            'is_active' => true,
            'last_message_at' => now(),
        ]);

        // Add initial message
        $this->addMessage($chat, $request->message);

        return redirect()->route('chat.show', $chat);
    }

    /**
     * Send a message in a chat.
     */
    public function sendMessage(Request $request, Chat $chat)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $userId = auth()->id();

        // Check if user is part of this chat
        if ($chat->user_id !== $userId && $chat->recipient_id !== $userId) {
            abort(403, 'You are not authorized to send messages in this chat.');
        }

        $this->addMessage($chat, $request->message);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully'
            ]);
        }

        return redirect()->back();
    }

    /**
     * Get messages for a chat (AJAX).
     */
    public function getMessages(Chat $chat)
    {
        $userId = auth()->id();
        
        // Check if user is part of this chat
        if ($chat->user_id !== $userId && $chat->recipient_id !== $userId) {
            abort(403);
        }

        $messages = $chat->messages()
            ->with('sender')
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'messages' => $messages->map(function($message) use ($userId) {
                return [
                    'id' => $message->id,
                    'content' => $message->content,
                    'sender_name' => $message->sender->name,
                    'is_own' => $message->sender_id === $userId,
                    'created_at' => $message->created_at->format('H:i'),
                    'created_at_full' => $message->created_at->format('M d, Y H:i'),
                ];
            })
        ]);
    }

    /**
     * Mark chat messages as read.
     */
    public function markAsRead(Chat $chat)
    {
        $userId = auth()->id();
        
        // Check if user is part of this chat
        if ($chat->user_id !== $userId && $chat->recipient_id !== $userId) {
            abort(403);
        }

        $chat->messages()
            ->where('sender_id', '!=', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json(['success' => true]);
    }

    /**
     * Get unread message count.
     */
    public function getUnreadCount()
    {
        $userId = auth()->id();
        
        $unreadCount = Message::whereHas('chat', function($query) use ($userId) {
            $query->where('user_id', $userId)->orWhere('recipient_id', $userId);
        })
        ->where('sender_id', '!=', $userId)
        ->where('is_read', false)
        ->count();

        return response()->json(['unread_count' => $unreadCount]);
    }

    /**
     * Start chat with car owner.
     */
    public function startCarOwnerChat(Car $car)
    {
        $userId = auth()->id();
        $ownerId = $car->owner_id;

        if ($userId === $ownerId) {
            return redirect()->back()->with('error', 'You cannot chat with yourself.');
        }

        // Check if chat already exists
        $existingChat = Chat::where(function($query) use ($userId, $ownerId) {
            $query->where('user_id', $userId)->where('recipient_id', $ownerId);
        })->orWhere(function($query) use ($userId, $ownerId) {
            $query->where('user_id', $ownerId)->where('recipient_id', $userId);
        })->where('related_to_type', 'App\\Models\\Car')
        ->where('related_to_id', $car->id)
        ->first();

        if ($existingChat) {
            return redirect()->route('chat.show', $existingChat);
        }

        // Create new chat
        $chat = Chat::create([
            'user_id' => $userId,
            'recipient_id' => $ownerId,
            'related_to_type' => 'App\\Models\\Car',
            'related_to_id' => $car->id,
            'is_active' => true,
            'last_message_at' => now(),
        ]);

        return redirect()->route('chat.show', $chat);
    }

    /**
     * Start chat with driver.
     */
    public function startDriverChat(Driver $driver)
    {
        $userId = auth()->id();
        $driverId = $driver->user_id;

        if ($userId === $driverId) {
            return redirect()->back()->with('error', 'You cannot chat with yourself.');
        }

        // Check if chat already exists
        $existingChat = Chat::where(function($query) use ($userId, $driverId) {
            $query->where('user_id', $userId)->where('recipient_id', $driverId);
        })->orWhere(function($query) use ($userId, $driverId) {
            $query->where('user_id', $driverId)->where('recipient_id', $userId);
        })->where('related_to_type', 'App\\Models\\Driver')
        ->where('related_to_id', $driver->id)
        ->first();

        if ($existingChat) {
            return redirect()->route('chat.show', $existingChat);
        }

        // Create new chat
        $chat = Chat::create([
            'user_id' => $userId,
            'recipient_id' => $driverId,
            'related_to_type' => 'App\\Models\\Driver',
            'related_to_id' => $driver->id,
            'is_active' => true,
            'last_message_at' => now(),
        ]);

        return redirect()->route('chat.show', $chat);
    }

    /**
     * Helper method to add a message to a chat.
     */
    private function addMessage(Chat $chat, string $content)
    {
        $message = Message::create([
            'chat_id' => $chat->id,
            'sender_id' => auth()->id(),
            'content' => $content,
            'is_read' => false,
        ]);

        // Update chat's last message time
        $chat->update(['last_message_at' => now()]);

        return $message;
    }
}
