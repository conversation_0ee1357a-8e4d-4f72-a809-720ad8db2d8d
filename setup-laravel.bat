@echo off
echo ========================================
echo Laravel Backend Setup Script
echo ========================================

cd park-and-rent-api

echo.
echo Step 1: Installing Composer dependencies...
call composer install
if %errorlevel% neq 0 (
    echo ERROR: Composer install failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo .env file created from .env.example
) else (
    echo .env file already exists
)

echo.
echo Step 3: Generating application key...
call php artisan key:generate

echo.
echo Step 4: Creating database (SQLite for development)...
if not exist database\database.sqlite (
    echo. > database\database.sqlite
    echo SQLite database created
)

echo.
echo Step 5: Running migrations...
call php artisan migrate
if %errorlevel% neq 0 (
    echo WARNING: Migrations failed. Check your database configuration.
)

echo.
echo Step 6: Seeding database...
call php artisan db:seed
if %errorlevel% neq 0 (
    echo WARNING: Database seeding failed.
)

echo.
echo Step 7: Creating storage link...
call php artisan storage:link

echo.
echo Step 8: Clearing caches...
call php artisan config:clear
call php artisan cache:clear
call php artisan route:clear
call php artisan view:clear

cd ..

echo.
echo ========================================
echo Laravel Backend Setup Complete!
echo ========================================
echo.
echo You can now start the development server with:
echo cd park-and-rent-api
echo php artisan serve
echo.
echo The API will be available at: http://127.0.0.1:8000
echo.
pause
