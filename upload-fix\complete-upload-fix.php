<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

echo "=== COMPLETE UPLOAD FIX SETUP ===\n\n";

try {
    echo "🔧 STEP 1: CREATING PUBLIC DIRECTORIES...\n";
    
    $directories = [
        public_path('car-images'),
        public_path('driver-profiles'),
        public_path('uploads')
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ Created: {$dir}\n";
        } else {
            echo "✓ Exists: {$dir}\n";
        }
    }
    
    echo "\n🔧 STEP 2: COPYING ALL EXISTING IMAGES...\n";
    
    // Copy car images
    $storageCarImages = storage_path('app/public/car-images');
    $publicCarImages = public_path('car-images');
    
    if (is_dir($storageCarImages)) {
        $carImages = glob($storageCarImages . '/*');
        $carCopied = 0;
        
        foreach ($carImages as $image) {
            $filename = basename($image);
            $target = $publicCarImages . '/' . $filename;
            
            if (!file_exists($target)) {
                copy($image, $target);
                chmod($target, 0644);
                $carCopied++;
            }
        }
        
        echo "✅ Copied {$carCopied} car images\n";
    }
    
    // Copy driver images
    $storageDriverImages = storage_path('app/public/driver-profiles');
    $publicDriverImages = public_path('driver-profiles');
    
    if (is_dir($storageDriverImages)) {
        $driverImages = glob($storageDriverImages . '/*');
        $driverCopied = 0;
        
        foreach ($driverImages as $image) {
            $filename = basename($image);
            $target = $publicDriverImages . '/' . $filename;
            
            if (!file_exists($target)) {
                copy($image, $target);
                chmod($target, 0644);
                $driverCopied++;
            }
        }
        
        echo "✅ Copied {$driverCopied} driver images\n";
    }
    
    echo "\n🔧 STEP 3: UPDATING DATABASE URLS...\n";
    
    // Update car image URLs
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    $carUpdated = 0;
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        if (is_array($images)) {
            $newImages = [];
            $hasChanges = false;
            
            foreach ($images as $image) {
                $filename = basename($image);
                $newUrl = 'https://ebisera.com/api/public/car-images/' . $filename;
                
                if ($image !== $newUrl) {
                    $hasChanges = true;
                }
                
                $newImages[] = $newUrl;
            }
            
            if ($hasChanges) {
                DB::table('cars')
                    ->where('id', $car->id)
                    ->update(['images' => json_encode($newImages)]);
                $carUpdated++;
            }
        }
    }
    
    echo "✅ Updated {$carUpdated} car records\n";
    
    // Update driver image URLs
    $drivers = DB::table('drivers')->whereNotNull('profile_image')->where('profile_image', '!=', '')->get();
    $driverUpdated = 0;
    
    foreach ($drivers as $driver) {
        $filename = basename($driver->profile_image);
        $newUrl = 'https://ebisera.com/api/public/driver-profiles/' . $filename;
        
        if ($driver->profile_image !== $newUrl) {
            DB::table('drivers')
                ->where('id', $driver->id)
                ->update(['profile_image' => $newUrl]);
            $driverUpdated++;
        }
    }
    
    echo "✅ Updated {$driverUpdated} driver records\n";
    
    echo "\n🔧 STEP 4: CLEARING CACHES...\n";
    $kernel->call('config:clear');
    $kernel->call('cache:clear');
    $kernel->call('config:cache');
    echo "✅ Caches cleared and updated\n";
    
    echo "\n🧪 STEP 5: TESTING SPECIFIC IMAGES...\n";
    
    $testImages = [
        '1woQbesqNa0YDkRcueZ8ZeBrVehliHMZM6ufizmB.jpg',
        'Prc1UTR1ykcuKUwBX60TLyPRYoxkZXmgGtTTO3mp.jpg'
    ];
    
    foreach ($testImages as $filename) {
        $publicPath = public_path('car-images/' . $filename);
        $url = "https://ebisera.com/api/public/car-images/{$filename}";
        
        echo "Testing: {$filename}\n";
        echo "  File exists: " . (file_exists($publicPath) ? "✅ YES" : "❌ NO") . "\n";
        echo "  URL: {$url}\n\n";
    }
    
    echo "🎉 SETUP COMPLETE!\n\n";
    echo "📊 SUMMARY:\n";
    echo "Car images in public: " . count(glob(public_path('car-images') . '/*')) . "\n";
    echo "Driver images in public: " . count(glob(public_path('driver-profiles') . '/*')) . "\n";
    echo "Car records updated: {$carUpdated}\n";
    echo "Driver records updated: {$driverUpdated}\n";
    
    echo "\n✅ NEW UPLOADS WILL NOW WORK AUTOMATICALLY!\n";
    echo "✅ NO MORE MANUAL COPYING NEEDED!\n";
    echo "✅ ALL IMAGES ACCESSIBLE AT: https://ebisera.com/api/public/car-images/\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
