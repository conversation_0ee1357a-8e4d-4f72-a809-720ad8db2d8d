@echo off
setlocal enabledelayedexpansion

echo Starting deployment process...

:: Create production environment files
echo Creating production environment files...

:: Frontend .env.production
(
echo VITE_API_URL=https://ebisera.com/api
echo VITE_APP_NAME="Park & Rent"
echo VITE_APP_URL=https://ebisera.com
) > .env.production

:: Backend .env
(
echo APP_NAME="Park & Rent"
echo APP_ENV=production
echo APP_DEBUG=false
echo APP_URL=https://ebisera.com
echo.
echo LOG_CHANNEL=stack
echo LOG_STACK=single
echo LOG_DEPRECATIONS_CHANNEL=null
echo LOG_LEVEL=error
echo.
echo DB_CONNECTION=mysql
echo DB_HOST=localhost
echo DB_PORT=3306
echo DB_DATABASE=u555127771_parkrent
echo DB_USERNAME=u555127771_parkrent
echo DB_PASSWORD=YOUR_DATABASE_PASSWORD
echo.
echo SESSION_DRIVER=database
echo SESSION_LIFETIME=120
echo SESSION_ENCRYPT=false
echo SESSION_PATH=/
echo SESSION_DOMAIN=.ebisera.com
echo.
echo BROADCAST_CONNECTION=log
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=database
echo.
echo CACHE_STORE=database
echo.
echo MAIL_MAILER=smtp
echo MAIL_HOST=smtp.hostinger.com
echo MAIL_PORT=587
echo MAIL_USERNAME=<EMAIL>
echo MAIL_PASSWORD=YOUR_EMAIL_PASSWORD
echo MAIL_ENCRYPTION=tls
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="%%APP_NAME%%"
echo.
echo FRONTEND_URL=https://ebisera.com
echo SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com
) > park-and-rent-api\.env

:: Build frontend
echo Building frontend...
call npm install
call npm run build

:: Prepare Laravel backend
echo Preparing Laravel backend...
cd park-and-rent-api

:: Install dependencies
call composer install --optimize-autoloader --no-dev

:: Generate application key if not exists
if not exist .env (
    call php artisan key:generate
)

:: Optimize Laravel
call php artisan config:cache
call php artisan route:cache
call php artisan view:cache

:: Create storage link
call php artisan storage:link

cd ..

:: Create deployment package
echo Creating deployment package...
if not exist deployment mkdir deployment
xcopy /E /I /Y dist\* deployment\
xcopy /E /I /Y park-and-rent-api deployment\api

:: Create .htaccess for frontend
(
echo ^<IfModule mod_rewrite.c^>
echo     RewriteEngine On
echo     RewriteBase /
echo     RewriteRule ^index\.html$ - [L]
echo     RewriteCond %%{REQUEST_FILENAME} !-f
echo     RewriteCond %%{REQUEST_FILENAME} !-d
echo     RewriteCond %%{REQUEST_FILENAME} !-l
echo     RewriteRule . /index.html [L]
echo ^</IfModule^>
echo.
echo # Security headers
echo ^<IfModule mod_headers.c^>
echo     Header set X-Content-Type-Options "nosniff"
echo     Header set X-XSS-Protection "1; mode=block"
echo     Header set X-Frame-Options "SAMEORIGIN"
echo     Header set Referrer-Policy "strict-origin-when-cross-origin"
echo     Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
echo ^</IfModule^>
echo.
echo # Enable compression
echo ^<IfModule mod_deflate.c^>
echo     AddOutputFilterByType DEFLATE text/plain
echo     AddOutputFilterByType DEFLATE text/html
echo     AddOutputFilterByType DEFLATE text/xml
echo     AddOutputFilterByType DEFLATE text/css
echo     AddOutputFilterByType DEFLATE application/xml
echo     AddOutputFilterByType DEFLATE application/xhtml+xml
echo     AddOutputFilterByType DEFLATE application/rss+xml
echo     AddOutputFilterByType DEFLATE application/javascript
echo     AddOutputFilterByType DEFLATE application/x-javascript
echo ^</IfModule^>
echo.
echo # Cache control
echo ^<IfModule mod_expires.c^>
echo     ExpiresActive On
echo     ExpiresByType image/jpg "access plus 1 year"
echo     ExpiresByType image/jpeg "access plus 1 year"
echo     ExpiresByType image/gif "access plus 1 year"
echo     ExpiresByType image/png "access plus 1 year"
echo     ExpiresByType text/css "access plus 1 month"
echo     ExpiresByType application/javascript "access plus 1 month"
echo ^</IfModule^>
) > deployment\.htaccess

:: Create .htaccess for API
(
echo ^<IfModule mod_rewrite.c^>
echo     RewriteEngine On
echo     RewriteBase /api/
echo     RewriteCond %%{REQUEST_FILENAME} !-d
echo     RewriteCond %%{REQUEST_FILENAME} !-f
echo     RewriteRule ^ index.php [L]
echo ^</IfModule^>
echo.
echo # Security headers
echo ^<IfModule mod_headers.c^>
echo     Header set X-Content-Type-Options "nosniff"
echo     Header set X-XSS-Protection "1; mode=block"
echo     Header set X-Frame-Options "SAMEORIGIN"
echo     Header set Referrer-Policy "strict-origin-when-cross-origin"
echo ^</IfModule^>
echo.
echo # Protect sensitive files
echo ^<FilesMatch "^\.env|composer\.json|composer\.lock|package\.json|package-lock\.json"^>
echo     Order allow,deny
echo     Deny from all
echo ^</FilesMatch^>
) > deployment\api\.htaccess

echo Deployment package created successfully!
echo The deployment package is in the 'deployment' directory
echo Upload the contents of the 'deployment' directory to your hosting provider
echo IMPORTANT: Don't forget to update the database credentials in deployment/api/.env

pause 