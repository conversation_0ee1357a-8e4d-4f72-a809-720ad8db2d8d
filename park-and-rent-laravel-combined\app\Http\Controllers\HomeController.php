<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\Driver;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        try {
            // Get featured cars (active cars, limit to 3)
            $featuredCars = Car::where('is_active', true)
                ->with('owner')
                ->latest()
                ->take(3)
                ->get();

            // Get featured drivers (available drivers, limit to 3)
            $featuredDrivers = Driver::where('is_available', true)
                ->where('license_verification_status', 'verified')
                ->with('user')
                ->orderBy('rating', 'desc')
                ->take(3)
                ->get();

            return view('home', compact('featuredCars', 'featuredDrivers'));
        } catch (\Exception $e) {
            // For debugging
            return response()->json([
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
}
