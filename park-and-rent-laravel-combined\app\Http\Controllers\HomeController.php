<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\Driver;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Get featured cars (active cars, limit to 3)
        $featuredCars = Car::where('is_active', true)
            ->latest()
            ->take(3)
            ->get();

        // Get featured drivers (available drivers, limit to 3)
        $featuredDrivers = Driver::where('is_available', true)
            ->where('license_verification_status', 'verified')
            ->orderBy('rating', 'desc')
            ->take(3)
            ->get();

        return view('home', compact('featuredCars', 'featuredDrivers'));
    }
}
