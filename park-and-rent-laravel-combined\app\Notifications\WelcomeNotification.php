<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $roleSpecificContent = $this->getRoleSpecificContent();

        return (new MailMessage)
            ->subject('Welcome to Park & Rent!')
            ->greeting('Welcome to Park & Rent, ' . $notifiable->name . '!')
            ->line('Thank you for joining our platform. We\'re excited to have you as part of our community.')
            ->line($roleSpecificContent['intro'])
            ->line('**What you can do:**')
            ->lines($roleSpecificContent['features'])
            ->action('Get Started', route('dashboard'))
            ->line('**Need Help?**')
            ->line('• Email: <EMAIL>')
            ->line('• Phone: 0788613669')
            ->line('• Visit our platform for tutorials and guides')
            ->line('Welcome aboard, and happy renting!');
    }

    /**
     * Get role-specific content
     */
    private function getRoleSpecificContent(): array
    {
        switch ($this->user->role) {
            case 'owner':
                return [
                    'intro' => 'As a car owner, you can now start earning money by renting out your vehicles.',
                    'features' => [
                        '• List your cars with photos and detailed descriptions',
                        '• Set your own rental prices and availability',
                        '• Manage booking requests from customers',
                        '• Track your earnings and rental history',
                        '• Request GPS installation for enhanced security',
                        '• Communicate with renters through our messaging system'
                    ]
                ];

            case 'driver':
                return [
                    'intro' => 'As a driver, you can offer your services and connect with customers who need transportation.',
                    'features' => [
                        '• Create your professional driver profile',
                        '• Set your service rates and availability',
                        '• Showcase your experience and specialties',
                        '• Receive and manage service requests',
                        '• Build your reputation through customer reviews',
                        '• Communicate with customers through our platform'
                    ]
                ];

            case 'client':
                return [
                    'intro' => 'As a customer, you now have access to a wide range of cars and professional drivers.',
                    'features' => [
                        '• Browse and rent cars from verified owners',
                        '• Hire professional drivers for your transportation needs',
                        '• Book vehicles with flexible rental periods',
                        '• Track your bookings and rental history',
                        '• Rate and review your rental experiences',
                        '• Chat directly with car owners and drivers'
                    ]
                ];

            case 'admin':
                return [
                    'intro' => 'As an administrator, you have full access to manage the Park & Rent platform.',
                    'features' => [
                        '• Manage all users, cars, and drivers',
                        '• Monitor and moderate platform activity',
                        '• Handle GPS installation requests',
                        '• View comprehensive analytics and reports',
                        '• Manage system settings and configurations',
                        '• Provide customer support and assistance'
                    ]
                ];

            default:
                return [
                    'intro' => 'You now have access to the Park & Rent platform.',
                    'features' => [
                        '• Explore available cars and drivers',
                        '• Manage your account and preferences',
                        '• Contact support for assistance'
                    ]
                ];
        }
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'type' => 'welcome',
            'role' => $this->user->role,
            'message' => 'Welcome to Park & Rent!'
        ];
    }
}
