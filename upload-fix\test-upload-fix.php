<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING UPLOAD FIX ===\n\n";

try {
    echo "🧪 TESTING CONFIGURATION...\n";
    echo "Default filesystem disk: " . config('filesystems.default') . "\n";
    echo "Public direct URL: " . config('filesystems.disks.public_direct.url') . "\n";
    echo "App URL: " . config('app.url') . "\n";
    
    echo "\n🧪 TESTING URL GENERATION...\n";
    
    // Test URL generation for new uploads
    $testFiles = [
        'car-images/test-new-upload.jpg',
        'driver-profiles/test-driver.jpg'
    ];
    
    foreach ($testFiles as $file) {
        $url = Storage::disk('public_direct')->url($file);
        $expected = 'https://ebisera.com/api/public/' . $file;
        
        echo "File: {$file}\n";
        echo "Generated: {$url}\n";
        echo "Expected: {$expected}\n";
        echo "Match: " . ($url === $expected ? "✅ YES" : "❌ NO") . "\n\n";
    }
    
    echo "🧪 TESTING SPECIFIC PROBLEM IMAGES...\n";
    
    $problemImages = [
        '1woQbesqNa0YDkRcueZ8ZeBrVehliHMZM6ufizmB.jpg',
        'Prc1UTR1ykcuKUwBX60TLyPRYoxkZXmgGtTTO3mp.jpg'
    ];
    
    foreach ($problemImages as $filename) {
        $publicPath = public_path('car-images/' . $filename);
        $storagePath = storage_path('app/public/car-images/' . $filename);
        $url = "https://ebisera.com/api/public/car-images/{$filename}";
        
        echo "Image: {$filename}\n";
        echo "  In storage: " . (file_exists($storagePath) ? "✅ YES" : "❌ NO") . "\n";
        echo "  In public: " . (file_exists($publicPath) ? "✅ YES" : "❌ NO") . "\n";
        echo "  URL: {$url}\n";
        
        // Test HTTP access
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "  HTTP Status: {$httpCode} " . ($httpCode === 200 ? "✅ OK" : "❌ ERROR") . "\n\n";
    }
    
    echo "🧪 TESTING DATABASE URLS...\n";
    
    // Check car URLs in database
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    $correctUrls = 0;
    $totalCars = count($cars);
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        if (is_array($images) && !empty($images)) {
            $firstImage = $images[0];
            if (strpos($firstImage, 'https://ebisera.com/api/public/car-images/') === 0) {
                $correctUrls++;
            }
        }
    }
    
    echo "Car URLs: {$correctUrls}/{$totalCars} correct\n";
    
    // Check driver URLs
    $drivers = DB::table('drivers')->whereNotNull('profile_image')->where('profile_image', '!=', '')->get();
    $correctDriverUrls = 0;
    $totalDrivers = count($drivers);
    
    foreach ($drivers as $driver) {
        if (strpos($driver->profile_image, 'https://ebisera.com/api/public/driver-profiles/') === 0) {
            $correctDriverUrls++;
        }
    }
    
    echo "Driver URLs: {$correctDriverUrls}/{$totalDrivers} correct\n";
    
    echo "\n📊 SUMMARY:\n";
    echo "Total car images in public: " . count(glob(public_path('car-images') . '/*')) . "\n";
    echo "Total driver images in public: " . count(glob(public_path('driver-profiles') . '/*')) . "\n";
    echo "Filesystem config: " . (config('filesystems.default') === 'public_direct' ? "✅ CORRECT" : "❌ NEEDS FIX") . "\n";
    
    if ($correctUrls === $totalCars && $correctDriverUrls === $totalDrivers) {
        echo "\n🎉 ALL TESTS PASSED!\n";
        echo "✅ Upload system is working correctly\n";
        echo "✅ New uploads will be accessible immediately\n";
    } else {
        echo "\n⚠️ SOME ISSUES FOUND\n";
        echo "Run the complete-upload-fix.php script to resolve\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
