<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING DIRECT PUBLIC ACCESS ===\n\n";

try {
    echo "🧪 TESTING CONFIGURATION...\n";
    echo "Default disk: " . config('filesystems.default') . "\n";
    echo "Public direct URL: " . config('filesystems.disks.public_direct.url') . "\n";
    
    // Test URL generation
    echo "\n🧪 TESTING URL GENERATION...\n";
    $testFiles = [
        'car-images/test.jpg',
        'driver-profiles/test.jpg'
    ];
    
    foreach ($testFiles as $file) {
        $url = Storage::disk('public_direct')->url($file);
        echo "File: {$file}\n";
        echo "Generated URL: {$url}\n";
        echo "Expected: https://ebisera.com/api/public/{$file}\n";
        echo "Match: " . ($url === "https://ebisera.com/api/public/{$file}" ? "✅ YES" : "❌ NO") . "\n\n";
    }
    
    echo "🧪 TESTING FILE ACCESS...\n";
    
    // Test car images
    $carImages = glob(public_path('car-images') . '/*');
    echo "Car images in public: " . count($carImages) . "\n";
    
    foreach (array_slice($carImages, 0, 3) as $image) {
        $filename = basename($image);
        echo "  ✓ {$filename}\n";
    }
    
    // Test driver images
    $driverImages = glob(public_path('driver-profiles') . '/*');
    echo "\nDriver images in public: " . count($driverImages) . "\n";
    
    foreach (array_slice($driverImages, 0, 3) as $image) {
        $filename = basename($image);
        echo "  ✓ {$filename}\n";
    }
    
    echo "\n🧪 TESTING DATABASE URLS...\n";
    
    // Check car URLs
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    echo "Cars with images: " . count($cars) . "\n";
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        if (is_array($images) && !empty($images)) {
            $firstImage = $images[0];
            $isCorrect = strpos($firstImage, 'https://ebisera.com/api/public/car-images/') === 0;
            echo "Car {$car->id}: " . ($isCorrect ? "✅" : "❌") . " {$firstImage}\n";
        }
    }
    
    // Check driver URLs
    $drivers = DB::table('drivers')->whereNotNull('profile_image')->where('profile_image', '!=', '')->get();
    echo "\nDrivers with images: " . count($drivers) . "\n";
    
    foreach ($drivers as $driver) {
        $isCorrect = strpos($driver->profile_image, 'https://ebisera.com/api/public/driver-profiles/') === 0;
        echo "Driver {$driver->id}: " . ($isCorrect ? "✅" : "❌") . " {$driver->profile_image}\n";
    }
    
    echo "\n🎯 TEST COMPLETE!\n";
    echo "✅ New uploads will automatically be accessible\n";
    echo "✅ No need to move files manually\n";
    echo "✅ All images use direct public URLs\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
