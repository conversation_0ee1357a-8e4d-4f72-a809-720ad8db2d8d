<?php

echo "=== TESTING ALL POSSIBLE 422 SCENARIOS ===\n\n";

// Test different field name variations that frontend might send
$testCases = [
    [
        'name' => 'Standard correct format',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Frontend might send confirmPassword',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Frontend might send passwordConfirmation',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'passwordConfirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Missing password_confirmation entirely',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Empty name field',
        'data' => [
            'name' => '',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Missing role field',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]
    ],
    [
        'name' => 'Invalid email format',
        'data' => [
            'name' => 'Test User',
            'email' => 'invalid-email-format',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Password too short',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Password mismatch',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Invalid role value',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'admin'
        ]
    ],
    [
        'name' => 'Existing email (using <EMAIL>)',
        'data' => [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ]
];

foreach ($testCases as $index => $test) {
    echo "🧪 TEST " . ($index + 1) . ": {$test['name']}\n";
    echo "Data: " . json_encode($test['data'], JSON_PRETTY_PRINT) . "\n";
    
    // Make request
    $url = 'https://ebisera.com/api/public/api/register';
    $jsonData = json_encode($test['data']);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Status: {$httpCode}\n";
    
    if ($httpCode === 422) {
        echo "❌ VALIDATION FAILED\n";
        $responseData = json_decode($response, true);
        if (isset($responseData['errors'])) {
            echo "Validation Errors:\n";
            foreach ($responseData['errors'] as $field => $errors) {
                echo "  • {$field}: " . implode(', ', $errors) . "\n";
            }
        } else {
            echo "Response: {$response}\n";
        }
    } elseif ($httpCode === 201) {
        echo "✅ SUCCESS\n";
    } else {
        echo "⚠️ OTHER ERROR: {$response}\n";
    }
    
    echo str_repeat("=", 60) . "\n\n";
}

echo "📋 SUMMARY:\n";
echo "Look for the test case that matches your frontend's behavior.\n";
echo "The most common issues are:\n";
echo "1. Frontend sends 'confirmPassword' instead of 'password_confirmation'\n";
echo "2. Missing 'role' field\n";
echo "3. Email already exists in database\n";
echo "4. Password too short (minimum 8 characters)\n\n";

echo "🔍 NEXT: Check your browser DevTools and compare with the test cases above.\n";
