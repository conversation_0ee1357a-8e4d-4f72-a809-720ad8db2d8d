@echo off
echo ========================================
echo Park & Rent - Deployment Script
echo ========================================

echo.
echo Step 1: Building React Frontend...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Frontend build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Preparing Laravel Backend...
cd park-and-rent-api
call composer install --optimize-autoloader --no-dev
if %errorlevel% neq 0 (
    echo ERROR: Composer install failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Optimizing Laravel...
call php artisan config:cache
call php artisan route:cache
call php artisan view:cache
cd ..

echo.
echo Step 4: Creating deployment package...
if exist deploy rmdir /s /q deploy
mkdir deploy\public_html

echo Copying React build files...
xcopy /e /i /y dist\* deploy\public_html\

echo Copying Laravel API...
xcopy /e /i /y park-and-rent-api deploy\public_html\api\

echo.
echo Step 5: Creating .htaccess file...
echo RewriteEngine On > deploy\public_html\.htaccess
echo. >> deploy\public_html\.htaccess
echo # Handle API requests >> deploy\public_html\.htaccess
echo RewriteRule ^^api/(.*)$ api/public/index.php [L] >> deploy\public_html\.htaccess
echo. >> deploy\public_html\.htaccess
echo # Handle React Router (SPA) >> deploy\public_html\.htaccess
echo RewriteCond %%{REQUEST_FILENAME} !-f >> deploy\public_html\.htaccess
echo RewriteCond %%{REQUEST_FILENAME} !-d >> deploy\public_html\.htaccess
echo RewriteCond %%{REQUEST_URI} !^^/api/ >> deploy\public_html\.htaccess
echo RewriteRule . /index.html [L] >> deploy\public_html\.htaccess

echo.
echo ========================================
echo DEPLOYMENT PACKAGE READY!
echo ========================================
echo.
echo Location: .\deploy\public_html\
echo.
echo Next steps:
echo 1. Upload contents of 'deploy\public_html\' to your Hostinger public_html folder
echo 2. Configure your .env file in the api folder
echo 3. Run database migrations on the server
echo.
echo Your app will be available at: https://ebisera.com
echo Your API will be available at: https://ebisera.com/api
echo.
pause
