<?php

namespace App\Notifications;

use App\Models\TicketBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TicketBookingReceived extends Notification implements ShouldQueue
{
    use Queueable;

    public $ticketBooking;

    /**
     * Create a new notification instance.
     */
    public function __construct(TicketBooking $ticketBooking)
    {
        $this->ticketBooking = $ticketBooking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $ticketType = ucfirst($this->ticketBooking->ticket_type);
        $tripType = $this->ticketBooking->trip_type === 'round_trip' ? 'Round Trip' : 'One Way';
        $departureDate = $this->ticketBooking->departure_date->format('M j, Y');
        $returnDate = $this->ticketBooking->return_date ? $this->ticketBooking->return_date->format('M j, Y') : 'N/A';

        return (new MailMessage)
            ->subject('New Ticket Booking Request - Park & Rent')
            ->greeting('Hello Admin!')
            ->line('A new ticket booking request has been submitted.')
            ->line('**Booking Details:**')
            ->line('• Ticket Type: ' . $ticketType)
            ->line('• Trip Type: ' . $tripType)
            ->line('• Passenger: ' . $this->ticketBooking->passenger_name)
            ->line('• Email: ' . $this->ticketBooking->passenger_email)
            ->line('• Phone: ' . $this->ticketBooking->passenger_phone)
            ->line('• From: ' . $this->ticketBooking->departure_city . ', ' . $this->ticketBooking->departure_country)
            ->line('• To: ' . $this->ticketBooking->destination_city . ', ' . $this->ticketBooking->destination_country)
            ->line('• Departure Date: ' . $departureDate)
            ->line('• Return Date: ' . $returnDate)
            ->line('• Passengers: ' . $this->ticketBooking->passengers_count)
            ->line('• Class: ' . ucfirst($this->ticketBooking->class_type))
            ->when($this->ticketBooking->special_requests, function ($message) {
                return $message->line('• Special Requests: ' . $this->ticketBooking->special_requests);
            })
            ->line('• Booking ID: #' . $this->ticketBooking->id)
            ->line('• Customer: ' . $this->ticketBooking->user->name . ' (' . $this->ticketBooking->user->email . ')')
            ->action('View Booking Details', url('/admin/ticket-bookings/' . $this->ticketBooking->id))
            ->line('Please contact the customer to provide pricing and booking details.')
            ->line('Customer Contact: ' . $this->ticketBooking->passenger_phone)
            ->line('Thank you for managing Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_booking_id' => $this->ticketBooking->id,
            'customer_id' => $this->ticketBooking->user_id,
            'type' => 'ticket_booking_received',
            'title' => 'New Ticket Booking Request',
            'message' => 'New ' . $this->ticketBooking->ticket_type . ' booking request from ' . $this->ticketBooking->passenger_name . ' to ' . $this->ticketBooking->destination_country . '.',
            'action_url' => '/admin/ticket-bookings/' . $this->ticketBooking->id,
        ];
    }
}
