<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Car;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CarController extends Controller
{
    /**
     * Display a listing of the owner's cars.
     */
    public function index()
    {
        $cars = Car::where('owner_id', auth()->id())
            ->latest()
            ->paginate(10);

        return view('owner.cars.index', compact('cars'));
    }

    /**
     * Show the form for creating a new car.
     */
    public function create()
    {
        return view('owner.cars.create');
    }

    /**
     * Store a newly created car in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:cars',
            'transmission' => 'required|in:automatic,manual',
            'fuel_type' => 'required|in:petrol,diesel,hybrid,electric',
            'seats' => 'required|integer|min:1|max:50',
            'mileage' => 'required|integer|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'price_per_hour' => 'nullable|numeric|min:0',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        $carData = $request->except(['images']);
        $carData['owner_id'] = auth()->id();
        $carData['is_active'] = $request->has('is_active');

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('cars', 'public');
                $imagePaths[] = $path;
            }
            $carData['images'] = $imagePaths;
        }

        Car::create($carData);

        return redirect()->route('owner.cars.index')
            ->with('success', 'Car added successfully!');
    }

    /**
     * Display the specified car.
     */
    public function show(Car $car)
    {
        // Ensure owner can only view their own cars
        if ($car->owner_id !== auth()->id()) {
            abort(403);
        }

        $car->load('owner');
        return view('owner.cars.show', compact('car'));
    }

    /**
     * Show the form for editing the specified car.
     */
    public function edit(Car $car)
    {
        // Ensure owner can only edit their own cars
        if ($car->owner_id !== auth()->id()) {
            abort(403);
        }

        return view('owner.cars.edit', compact('car'));
    }

    /**
     * Update the specified car in storage.
     */
    public function update(Request $request, Car $car)
    {
        // Ensure owner can only update their own cars
        if ($car->owner_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'required|string|max:255',
            'license_plate' => 'required|string|max:255|unique:cars,license_plate,' . $car->id,
            'transmission' => 'required|in:automatic,manual',
            'fuel_type' => 'required|in:petrol,diesel,hybrid,electric',
            'seats' => 'required|integer|min:1|max:50',
            'mileage' => 'required|integer|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'price_per_hour' => 'nullable|numeric|min:0',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'new_images' => 'nullable|array|max:5',
            'new_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'remove_images' => 'nullable|array',
            'remove_images.*' => 'string',
            'is_active' => 'boolean',
        ]);

        $carData = $request->except(['new_images', 'remove_images']);
        $carData['is_active'] = $request->has('is_active');

        // Handle existing images
        $currentImages = $car->images ?? [];

        // Remove selected images
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (in_array($imageToRemove, $currentImages)) {
                    Storage::disk('public')->delete($imageToRemove);
                    $currentImages = array_filter($currentImages, function($img) use ($imageToRemove) {
                        return $img !== $imageToRemove;
                    });
                }
            }
        }

        // Add new images
        if ($request->hasFile('new_images')) {
            foreach ($request->file('new_images') as $image) {
                $path = $image->store('cars', 'public');
                $currentImages[] = $path;
            }
        }

        $carData['images'] = array_values($currentImages);

        $car->update($carData);

        return redirect()->route('owner.cars.index')
            ->with('success', 'Car updated successfully!');
    }

    /**
     * Remove the specified car from storage.
     */
    public function destroy(Car $car)
    {
        // Ensure owner can only delete their own cars
        if ($car->owner_id !== auth()->id()) {
            abort(403);
        }

        // Delete associated images
        if ($car->images) {
            foreach ($car->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $car->delete();

        return redirect()->route('owner.cars.index')
            ->with('success', 'Car deleted successfully!');
    }

    /**
     * Toggle car active status
     */
    public function toggleStatus(Car $car)
    {
        // Ensure owner can only toggle their own cars
        if ($car->owner_id !== auth()->id()) {
            abort(403);
        }

        $car->update(['is_active' => !$car->is_active]);

        $status = $car->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Car {$status} successfully!");
    }
}
