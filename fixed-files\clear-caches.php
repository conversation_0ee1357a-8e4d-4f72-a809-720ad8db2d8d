<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

echo "=== CLEARING ALL LARAVEL CACHES ===\n\n";

try {
    // Clear configuration cache
    echo "Clearing config cache...\n";
    $kernel->call('config:clear');
    echo "✅ Config cache cleared\n\n";
    
    // Clear application cache
    echo "Clearing application cache...\n";
    $kernel->call('cache:clear');
    echo "✅ Application cache cleared\n\n";
    
    // Clear route cache
    echo "Clearing route cache...\n";
    $kernel->call('route:clear');
    echo "✅ Route cache cleared\n\n";
    
    // Clear view cache
    echo "Clearing view cache...\n";
    $kernel->call('view:clear');
    echo "✅ View cache cleared\n\n";
    
    // Cache config with new settings
    echo "Caching new configuration...\n";
    $kernel->call('config:cache');
    echo "✅ Configuration cached\n\n";
    
    echo "🎉 All caches cleared and configuration updated!\n";
    echo "📋 Next steps:\n";
    echo "1. Upload the fixed files to replace existing ones\n";
    echo "2. Run the database fix script\n";
    echo "3. Test image URLs\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
