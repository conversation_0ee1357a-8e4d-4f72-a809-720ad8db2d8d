========================================
  COMPLETE DEPLOYMENT INSTRUCTIONS
========================================

UPLOAD ALL FILES FROM 'deployment\complete-upload\' TO YOUR HOSTINGER public_html/

Final structure on server:
public_html/
├── index.html              (React frontend)
├── .htaccess               (React routing)
├── assets/                 (React assets)
├── api/                    (Laravel API public files)
│   ├── index.php
│   ├── .htaccess
│   └── (other Laravel public files)
├── app/                    (Laravel application)
├── bootstrap/              (Laravel bootstrap)
├── config/                 (Laravel config)
├── database/               (Laravel database)
├── resources/              (Laravel resources)
├── routes/                 (Laravel routes)
├── storage/                (Laravel storage)
├── vendor/                 (Laravel dependencies)
├── .env                    (Laravel environment)
├── artisan                 (Laravel CLI)
├── composer.json
└── composer.lock

========================================
  POST-UPLOAD CONFIGURATION
========================================

1. UPDATE .env FILE:
   - DB_DATABASE=park_and_rent
   - DB_USERNAME=(your database username)
   - DB_PASSWORD=(your database password)
   - Generate APP_KEY: php artisan key:generate

2. SET PERMISSIONS:
   - storage/ folder: 755 or 775
   - bootstrap/cache/ folder: 755 or 775

3. INSTALL DEPENDENCIES (if vendor not uploaded):
   composer install --no-dev --optimize-autoloader

4. RUN DATABASE SETUP:
   php artisan migrate
   php artisan db:seed

5. CLEAR CACHES:
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache

========================================
  TESTING
========================================

1. Test Frontend: https://ebisera.com
   Should load Park & Rent React application

2. Test API Health: https://ebisera.com/api/health
   Should return: {"status":"ok","message":"Park & Rent API is running"}

3. Test API Cars: https://ebisera.com/api/api/cars
   Should return cars data or empty array

========================================
  TROUBLESHOOTING
========================================

If API returns 500 error:
- Check Laravel logs in storage/logs/
- Verify database connection in .env
- Ensure proper file permissions

If React app shows API errors:
- Check browser console for CORS errors
- Verify API endpoints are accessible
- Check network tab in browser dev tools

========================================
