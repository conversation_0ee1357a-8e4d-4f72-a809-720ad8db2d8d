<!DOCTYPE html>
<html>
<head>
    <title>Create PWA Icons</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    
    <script>
        // Create 192x192 icon
        const canvas192 = document.getElementById('canvas192');
        const ctx192 = canvas192.getContext('2d');
        
        // Simple car icon for 192x192
        ctx192.fillStyle = '#4CAF50';
        ctx192.fillRect(0, 0, 192, 192);
        ctx192.fillStyle = '#fff';
        ctx192.font = '48px Arial';
        ctx192.textAlign = 'center';
        ctx192.fillText('P&R', 96, 110);
        
        // Create 512x512 icon
        const canvas512 = document.getElementById('canvas512');
        const ctx512 = canvas512.getContext('2d');
        
        // Simple car icon for 512x512
        ctx512.fillStyle = '#4CAF50';
        ctx512.fillRect(0, 0, 512, 512);
        ctx512.fillStyle = '#fff';
        ctx512.font = '128px Arial';
        ctx512.textAlign = 'center';
        ctx512.fillText('P&R', 256, 290);
        
        // Download function
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Auto-download after 1 second
        setTimeout(() => {
            downloadCanvas(canvas192, 'pwa-192x192.png');
            downloadCanvas(canvas512, 'pwa-512x512.png');
        }, 1000);
    </script>
    
    <p>Icons will download automatically. Upload them to your public_html/ directory.</p>
</body>
</html>
