# 🚀 Park & Rent Complete Deployment Guide

## 📋 Overview
This guide will help you deploy the complete Park & Rent system with:
- **React Frontend** (main website)
- **Laravel API Backend** (park-and-rent-api)

## 🛠️ Quick Deployment

### Option 1: Complete Deployment (Recommended)
```bash
# Run this single command to deploy everything
deploy-complete.bat
```

### Option 2: Deploy Separately
```bash
# Deploy API only
deploy-api.bat

# Deploy Frontend only  
deploy-frontend.bat
```

## 📁 What Gets Deployed

### Frontend (React)
- Built React application → `public_html/` (root)
- Includes routing, assets, and optimized code
- Configured to connect to API at `https://ebisera.com/api/api`

### Backend (Laravel API)
- Laravel application files → `public_html/` (root level)
- Laravel public files → `public_html/api/` (API endpoint)
- Database migrations, models, controllers
- Email notifications, authentication, CORS configured

## 🌐 Expected URLs After Deployment

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | `https://ebisera.com` | Main React application |
| **API Health** | `https://ebisera.com/api/health` | API status check |
| **API Cars** | `https://ebisera.com/api/api/cars` | Cars endpoint |
| **API Auth** | `https://ebisera.com/api/api/login` | Authentication |

## 📂 Server File Structure

After deployment, your `public_html/` should look like:

```
public_html/
├── index.html              (React app entry point)
├── .htaccess               (React routing rules)
├── assets/                 (React built assets)
│   ├── index-[hash].js
│   ├── index-[hash].css
│   └── ...
├── api/                    (Laravel API public files)
│   ├── index.php           (Laravel entry point)
│   ├── .htaccess           (API routing + CORS)
│   └── ...
├── app/                    (Laravel application)
├── bootstrap/              (Laravel bootstrap)
├── config/                 (Laravel config)
├── database/               (Laravel database)
├── resources/              (Laravel resources)
├── routes/                 (Laravel routes)
├── storage/                (Laravel storage)
├── vendor/                 (Laravel dependencies)
├── .env                    (Laravel environment)
├── artisan                 (Laravel CLI)
├── composer.json
└── composer.lock
```

## ⚙️ Post-Deployment Configuration

### 1. Database Setup
Update `.env` file with your database credentials:
```env
DB_DATABASE=park_and_rent
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
```

### 2. Generate Application Key
```bash
php artisan key:generate
```

### 3. Run Migrations
```bash
php artisan migrate
php artisan db:seed
```

### 4. Set Permissions
```bash
chmod 755 storage/
chmod 755 bootstrap/cache/
```

### 5. Cache Configuration
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🧪 Testing Your Deployment

### Frontend Test
1. Visit `https://ebisera.com`
2. Should load the Park & Rent React application
3. Check browser console for any errors

### API Tests
1. **Health Check**: `https://ebisera.com/api/health`
   ```json
   {
     "status": "ok",
     "message": "Park & Rent API is running",
     "timestamp": "2024-12-15T10:30:00.000000Z",
     "version": "1.0.0"
   }
   ```

2. **Cars Endpoint**: `https://ebisera.com/api/api/cars`
   - Should return cars data or empty array
   - No authentication required

3. **Authentication**: `https://ebisera.com/api/api/login`
   - POST request with email/password
   - Should return user data and token

## 🔧 Troubleshooting

### API Returns 404 Error
- Check if Laravel files are uploaded correctly
- Verify `.htaccess` file in `api/` folder
- Check server error logs

### API Returns 500 Error
- Check Laravel logs in `storage/logs/`
- Verify database connection in `.env`
- Ensure proper file permissions

### React App Shows API Errors
- Check browser console for CORS errors
- Verify API endpoints are accessible
- Check network tab in browser dev tools

### CORS Issues
- Verify `.htaccess` in `api/` folder has CORS headers
- Check Laravel CORS configuration
- Ensure API URLs are correct

## 📞 Support

If you encounter issues:
1. Check the deployment logs
2. Verify file permissions
3. Test API endpoints individually
4. Check browser console for frontend errors

## 🎯 Key Features Deployed

### Frontend Features
- ✅ Car browsing and search
- ✅ Driver listings
- ✅ User authentication
- ✅ Booking system
- ✅ Real-time chat
- ✅ Owner/Admin dashboards

### Backend Features
- ✅ RESTful API endpoints
- ✅ User authentication (Sanctum)
- ✅ Database models and migrations
- ✅ Email notifications
- ✅ File uploads
- ✅ CORS configuration
- ✅ Security headers

## 🚀 Ready to Deploy!

Run `deploy-complete.bat` and follow the upload instructions to get your Park & Rent system live!
