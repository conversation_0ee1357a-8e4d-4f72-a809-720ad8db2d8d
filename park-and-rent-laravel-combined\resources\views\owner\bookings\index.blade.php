@extends('layouts.app')

@section('title', 'Booking Requests')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Booking Requests</h1>
            <p class="mt-2 text-gray-600">Manage booking requests for your cars</p>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        {{ session('success') }}
                    </p>
                </div>
            </div>
        </div>
        @endif

        @if($bookings->count() > 0)
            <div class="space-y-6">
                @foreach($bookings as $booking)
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- Booking Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">
                                            Booking #{{ $booking->id }}
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            Requested on {{ $booking->created_at->format('M d, Y \a\t g:i A') }}
                                        </p>
                                    </div>
                                    <div>
                                        @if($booking->status === 'pending')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Pending Review
                                            </span>
                                        @elseif($booking->status === 'confirmed')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Confirmed
                                            </span>
                                        @elseif($booking->status === 'cancelled')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Cancelled
                                            </span>
                                        @elseif($booking->status === 'completed')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Completed
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Customer & Car Info -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Customer Info -->
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Customer Information</h4>
                                        <div class="space-y-2">
                                            <div>
                                                <span class="text-sm font-medium text-gray-500">Name:</span>
                                                <span class="text-sm text-gray-900 ml-2">{{ $booking->user->name ?? 'Unknown' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-medium text-gray-500">Email:</span>
                                                <span class="text-sm text-gray-900 ml-2">{{ $booking->user->email ?? 'N/A' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-medium text-gray-500">Contact:</span>
                                                <span class="text-sm text-gray-900 ml-2">0788613669</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Car Info -->
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Car Information</h4>
                                        @if($booking->bookable)
                                            <div class="flex items-center space-x-3">
                                                @if($booking->bookable->images && count($booking->bookable->images) > 0)
                                                    <img class="h-16 w-16 rounded-lg object-cover" src="{{ asset('storage/' . $booking->bookable->images[0]) }}" alt="Car">
                                                @else
                                                    <div class="h-16 w-16 rounded-lg bg-gray-300 flex items-center justify-center">
                                                        <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                                        </svg>
                                                    </div>
                                                @endif
                                                <div>
                                                    <h5 class="font-medium text-gray-900">{{ $booking->bookable->make }} {{ $booking->bookable->model }}</h5>
                                                    <p class="text-sm text-gray-600">{{ $booking->bookable->year }} • {{ $booking->bookable->color }}</p>
                                                    <p class="text-sm text-gray-500">{{ $booking->bookable->license_plate }}</p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Booking Details -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <h4 class="font-medium text-gray-900 mb-3">Booking Details</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-500">Start Date:</span>
                                            <div class="text-gray-900">{{ \Carbon\Carbon::parse($booking->start_date)->format('M d, Y') }}</div>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-500">End Date:</span>
                                            <div class="text-gray-900">{{ \Carbon\Carbon::parse($booking->end_date)->format('M d, Y') }}</div>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-500">Duration:</span>
                                            <div class="text-gray-900">
                                                {{ \Carbon\Carbon::parse($booking->start_date)->diffInDays(\Carbon\Carbon::parse($booking->end_date)) + 1 }} days
                                            </div>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-500">Total Amount:</span>
                                            <div class="text-lg font-bold text-primary-600">${{ number_format($booking->total_amount, 2) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pickup/Destination -->
                                @if($booking->pickup_location || $booking->destination)
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <h4 class="font-medium text-gray-900 mb-3">Location Details</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        @if($booking->pickup_location)
                                        <div>
                                            <span class="font-medium text-gray-500">Pickup Location:</span>
                                            <div class="text-gray-900">{{ $booking->pickup_location }}</div>
                                        </div>
                                        @endif
                                        @if($booking->destination)
                                        <div>
                                            <span class="font-medium text-gray-500">Destination:</span>
                                            <div class="text-gray-900">{{ $booking->destination }}</div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif

                                <!-- Customer Message -->
                                @if($booking->message)
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <h4 class="font-medium text-gray-900 mb-2">Customer Message</h4>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <p class="text-sm text-gray-700">{{ $booking->message }}</p>
                                    </div>
                                </div>
                                @endif

                                <!-- Actions -->
                                @if($booking->status === 'pending')
                                <div class="mt-6 pt-6 border-t border-gray-200 flex space-x-3">
                                    <form action="{{ route('owner.bookings.confirm', $booking) }}" method="POST" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                            Confirm Booking
                                        </button>
                                    </form>
                                    
                                    <form action="{{ route('owner.bookings.reject', $booking) }}" method="POST" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to reject this booking?')"
                                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                            Reject Booking
                                        </button>
                                    </form>
                                </div>
                                @elseif($booking->status === 'confirmed')
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="bg-green-50 border border-green-200 rounded-md p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-green-800">
                                                    Booking Confirmed
                                                </h3>
                                                <div class="mt-2 text-sm text-green-700">
                                                    <p>The customer has been notified. Contact details: 0788613669</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $bookings->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No booking requests</h3>
                <p class="mt-1 text-sm text-gray-500">Booking requests will appear here when customers book your cars.</p>
                <div class="mt-6">
                    <a href="{{ route('owner.cars.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Manage Your Cars
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
