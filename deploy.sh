#!/bin/bash

echo "========================================"
echo "Park & Rent - Deployment Script"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Build React Frontend
print_status "Step 1: Building React Frontend..."
npm run build
if [ $? -ne 0 ]; then
    print_error "Frontend build failed!"
    exit 1
fi

# Step 2: Prepare <PERSON>end
print_status "Step 2: Preparing Laravel Backend..."
cd park-and-rent-api

# Install composer dependencies
composer install --optimize-autoloader --no-dev
if [ $? -ne 0 ]; then
    print_error "Composer install failed!"
    exit 1
fi

# Step 3: Optimize Laravel
print_status "Step 3: Optimizing Laravel..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
cd ..

# Step 4: Create deployment package
print_status "Step 4: Creating deployment package..."
rm -rf deploy
mkdir -p deploy/public_html

# Copy React build files
print_status "Copying React build files..."
cp -r dist/* deploy/public_html/

# Copy Laravel API
print_status "Copying Laravel API..."
cp -r park-and-rent-api deploy/public_html/api

# Step 5: Create .htaccess file
print_status "Step 5: Creating .htaccess file..."
cat > deploy/public_html/.htaccess << 'EOF'
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# CORS Headers for API
<IfModule mod_headers.c>
    SetEnvIf Origin "^https?://(www\.)?(ebisera\.com|localhost:5173)$" CORS_ORIGIN=$0
    Header always set Access-Control-Allow-Origin %{CORS_ORIGIN}e env=CORS_ORIGIN
    Header always set Access-Control-Allow-Headers "origin, x-requested-with, content-type, authorization, accept"
    Header always set Access-Control-Allow-Methods "PUT, GET, POST, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Handle API requests
RewriteRule ^api/(.*)$ api/public/index.php [L]

# Handle React Router (SPA)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes
EOF

# Step 6: Create production .env template
print_status "Step 6: Creating production .env template..."
cat > deploy/public_html/api/.env.production << 'EOF'
APP_NAME="Park & Rent"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://ebisera.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration (Update with your Hostinger details)
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.ebisera.com

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# CORS Configuration
FRONTEND_URL=https://ebisera.com
SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com

VITE_APP_NAME="${APP_NAME}"
EOF

# Step 7: Create deployment instructions
print_status "Step 7: Creating deployment instructions..."
cat > deploy/DEPLOYMENT_INSTRUCTIONS.md << 'EOF'
# Deployment Instructions

## Files to Upload

Upload the contents of `public_html/` to your Hostinger public_html directory.

## Server Configuration

1. **Configure Environment**
   ```bash
   cd api/
   cp .env.production .env
   # Edit .env with your actual database credentials
   ```

2. **Generate Application Key**
   ```bash
   php artisan key:generate
   ```

3. **Run Database Migrations**
   ```bash
   php artisan migrate --force
   php artisan db:seed --force
   ```

4. **Set Permissions**
   ```bash
   chmod -R 755 .
   chmod -R 775 api/storage/
   chmod -R 775 api/bootstrap/cache/
   ```

5. **Create Storage Link**
   ```bash
   cd api/
   php artisan storage:link
   ```

## Testing

- Frontend: https://ebisera.com
- API Health: https://ebisera.com/api/up
- Cars API: https://ebisera.com/api/cars

## Troubleshooting

- Check Laravel logs: `api/storage/logs/laravel.log`
- Verify database connection
- Ensure proper file permissions
- Check .htaccess syntax
EOF

echo
echo "========================================"
print_status "DEPLOYMENT PACKAGE READY!"
echo "========================================"
echo
print_status "Location: ./deploy/public_html/"
echo
print_warning "Next steps:"
echo "1. Upload contents of 'deploy/public_html/' to your Hostinger public_html folder"
echo "2. Configure your .env file in the api folder (template provided)"
echo "3. Run database migrations on the server"
echo "4. Set proper file permissions"
echo
print_status "Your app will be available at: https://ebisera.com"
print_status "Your API will be available at: https://ebisera.com/api"
echo
print_status "See deploy/DEPLOYMENT_INSTRUCTIONS.md for detailed server setup"
