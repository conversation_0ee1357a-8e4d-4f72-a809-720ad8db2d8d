<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

echo "=== COMPLETE PARK & RENT IMAGE SETUP ===\n\n";

try {
    echo "🔧 STEP 1: CLEARING CACHES...\n";
    $kernel->call('config:clear');
    $kernel->call('cache:clear');
    $kernel->call('route:clear');
    $kernel->call('view:clear');
    echo "✅ All caches cleared\n\n";
    
    echo "🔧 STEP 2: CREATING STORAGE DIRECTORIES...\n";
    $directories = [
        storage_path('app/public/car-images'),
        storage_path('app/public/driver-profiles'),
        public_path('storage')
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ Created: {$dir}\n";
        } else {
            echo "✓ Exists: {$dir}\n";
        }
    }
    
    echo "\n🔧 STEP 3: CHECKING STORAGE LINK...\n";
    $storageLink = public_path('storage');
    $storageTarget = storage_path('app/public');
    
    if (is_link($storageLink)) {
        echo "✅ Storage link exists\n";
        echo "Target: " . readlink($storageLink) . "\n";
    } else {
        echo "❌ Storage link missing - creating...\n";
        if (file_exists($storageLink)) {
            unlink($storageLink);
        }
        symlink($storageTarget, $storageLink);
        echo "✅ Storage link created\n";
    }
    
    echo "\n🔧 STEP 4: SETTING PERMISSIONS...\n";
    chmod(storage_path('app/public'), 0755);
    chmod(storage_path('app/public/car-images'), 0755);
    chmod(storage_path('app/public/driver-profiles'), 0755);
    chmod(public_path('storage'), 0755);
    echo "✅ Permissions set\n";
    
    echo "\n🔧 STEP 5: TESTING CONFIGURATION...\n";
    echo "APP_URL: " . config('app.url') . "\n";
    echo "Storage URL: " . config('filesystems.disks.public.url') . "\n";
    
    $testUrl = Storage::disk('public')->url('car-images/test.jpg');
    echo "Generated URL: {$testUrl}\n";
    
    if ($testUrl === 'https://ebisera.com/api/public/storage/car-images/test.jpg') {
        echo "✅ URL generation is correct\n";
    } else {
        echo "❌ URL generation needs fixing\n";
    }
    
    echo "\n🔧 STEP 6: CACHING NEW CONFIGURATION...\n";
    $kernel->call('config:cache');
    echo "✅ Configuration cached\n";
    
    echo "\n🎉 SETUP COMPLETE!\n";
    echo "📋 Next steps:\n";
    echo "1. Run: php copy-missing-images.php\n";
    echo "2. Run: php fix-all-images.php\n";
    echo "3. Run: php fix-driver-model.php\n";
    echo "4. Test your application\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
