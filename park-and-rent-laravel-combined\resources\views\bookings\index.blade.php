@extends('layouts.app')

@section('title', 'My Bookings')

@section('content')
<div class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Bookings</h1>
            <p class="mt-2 text-gray-600">View and manage your car rentals and driver bookings</p>
        </div>

        @if($bookings->count() > 0)
            <div class="space-y-6">
                @foreach($bookings as $booking)
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- Booking Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">
                                            Booking #{{ $booking->id }}
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            Booked on {{ $booking->created_at->format('M d, Y') }}
                                        </p>
                                    </div>
                                    <div>
                                        @if($booking->status === 'pending')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        @elseif($booking->status === 'confirmed')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Confirmed
                                            </span>
                                        @elseif($booking->status === 'cancelled')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Cancelled
                                            </span>
                                        @elseif($booking->status === 'completed')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Completed
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Booking Details -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Item Info -->
                                    <div>
                                        @if($booking->bookable_type === 'App\\Models\\Car' && $booking->bookable)
                                            <div class="flex items-center space-x-3">
                                                @if($booking->bookable->images && count($booking->bookable->images) > 0)
                                                    <img class="h-16 w-16 rounded-lg object-cover" src="{{ asset('storage/' . $booking->bookable->images[0]) }}" alt="Car">
                                                @else
                                                    <div class="h-16 w-16 rounded-lg bg-gray-300 flex items-center justify-center">
                                                        <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                                        </svg>
                                                    </div>
                                                @endif
                                                <div>
                                                    <h4 class="font-medium text-gray-900">{{ $booking->bookable->make }} {{ $booking->bookable->model }}</h4>
                                                    <p class="text-sm text-gray-600">{{ $booking->bookable->year }} • Car Rental</p>
                                                    <p class="text-sm text-gray-500">{{ $booking->bookable->location }}</p>
                                                </div>
                                            </div>
                                        @elseif($booking->bookable_type === 'App\\Models\\Driver' && $booking->bookable)
                                            <div class="flex items-center space-x-3">
                                                @if($booking->bookable->profile_image)
                                                    <img class="h-16 w-16 rounded-full object-cover" src="{{ asset('storage/' . $booking->bookable->profile_image) }}" alt="Driver">
                                                @else
                                                    <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                        </svg>
                                                    </div>
                                                @endif
                                                <div>
                                                    <h4 class="font-medium text-gray-900">{{ $booking->bookable->name }}</h4>
                                                    <p class="text-sm text-gray-600">Driver Service</p>
                                                    <p class="text-sm text-gray-500">{{ $booking->bookable->location }}</p>
                                                </div>
                                            </div>
                                        @else
                                            <div class="text-gray-500">
                                                <p>Item no longer available</p>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Booking Info -->
                                    <div class="space-y-2">
                                        <div>
                                            <span class="text-sm font-medium text-gray-500">Date:</span>
                                            <span class="text-sm text-gray-900 ml-2">
                                                {{ \Carbon\Carbon::parse($booking->start_date)->format('M d, Y') }}
                                                @if($booking->end_date && $booking->end_date !== $booking->start_date)
                                                    - {{ \Carbon\Carbon::parse($booking->end_date)->format('M d, Y') }}
                                                @endif
                                            </span>
                                        </div>
                                        
                                        @if($booking->start_time)
                                        <div>
                                            <span class="text-sm font-medium text-gray-500">Time:</span>
                                            <span class="text-sm text-gray-900 ml-2">{{ \Carbon\Carbon::parse($booking->start_time)->format('g:i A') }}</span>
                                        </div>
                                        @endif

                                        @if($booking->service_type)
                                        <div>
                                            <span class="text-sm font-medium text-gray-500">Service:</span>
                                            <span class="text-sm text-gray-900 ml-2">{{ ucfirst(str_replace('_', ' ', $booking->service_type)) }}</span>
                                        </div>
                                        @endif

                                        @if($booking->duration)
                                        <div>
                                            <span class="text-sm font-medium text-gray-500">Duration:</span>
                                            <span class="text-sm text-gray-900 ml-2">{{ $booking->duration }} hours</span>
                                        </div>
                                        @endif

                                        <div>
                                            <span class="text-sm font-medium text-gray-500">Total:</span>
                                            <span class="text-lg font-bold text-primary-600 ml-2">${{ number_format($booking->total_amount, 2) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Locations -->
                                @if($booking->pickup_location || $booking->destination)
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        @if($booking->pickup_location)
                                        <div>
                                            <span class="font-medium text-gray-500">Pickup:</span>
                                            <span class="text-gray-900 ml-2">{{ $booking->pickup_location }}</span>
                                        </div>
                                        @endif
                                        @if($booking->destination)
                                        <div>
                                            <span class="font-medium text-gray-500">Destination:</span>
                                            <span class="text-gray-900 ml-2">{{ $booking->destination }}</span>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif

                                <!-- Actions -->
                                <div class="mt-4 pt-4 border-t border-gray-200 flex justify-between items-center">
                                    <div>
                                        @if($booking->status === 'confirmed')
                                            <span class="text-sm text-green-600 font-medium">
                                                ✓ Contact: 0788613669
                                            </span>
                                        @elseif($booking->status === 'pending')
                                            <span class="text-sm text-yellow-600 font-medium">
                                                ⏳ Waiting for confirmation
                                            </span>
                                        @endif
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <a href="{{ route('bookings.show', $booking) }}" 
                                           class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm font-medium">
                                            View Details
                                        </a>
                                        
                                        @if($booking->status === 'pending')
                                            <form action="{{ route('bookings.destroy', $booking) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" onclick="return confirm('Are you sure you want to cancel this booking?')" 
                                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium">
                                                    Cancel
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $bookings->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
                <p class="mt-1 text-sm text-gray-500">Start by browsing available cars or drivers.</p>
                <div class="mt-6 flex justify-center space-x-4">
                    <a href="{{ route('cars.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Browse Cars
                    </a>
                    <a href="{{ route('drivers.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Find Drivers
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
