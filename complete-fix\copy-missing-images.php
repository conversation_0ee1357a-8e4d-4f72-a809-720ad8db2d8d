<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== COPYING MISSING IMAGES TO STORAGE ===\n\n";

try {
    // Create storage directories
    $carImagesDir = storage_path('app/public/car-images');
    $driverProfilesDir = storage_path('app/public/driver-profiles');
    
    if (!is_dir($carImagesDir)) {
        mkdir($carImagesDir, 0755, true);
        echo "✅ Created car-images directory\n";
    }
    
    if (!is_dir($driverProfilesDir)) {
        mkdir($driverProfilesDir, 0755, true);
        echo "✅ Created driver-profiles directory\n";
    }
    
    // Find all image files in the project
    echo "🔍 SEARCHING FOR IMAGE FILES...\n";
    
    $searchPaths = [
        public_path(),
        storage_path(),
        base_path('public'),
        base_path('storage')
    ];
    
    $foundImages = [];
    
    foreach ($searchPaths as $searchPath) {
        if (is_dir($searchPath)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($searchPath, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $extension = strtolower($file->getExtension());
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        $filename = $file->getFilename();
                        $foundImages[$filename] = $file->getPathname();
                    }
                }
            }
        }
    }
    
    echo "Found " . count($foundImages) . " image files\n\n";
    
    // Get all image filenames from database
    echo "📋 CHECKING DATABASE FOR REQUIRED IMAGES...\n";
    
    $requiredImages = [];
    
    // Car images
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        if (is_array($images)) {
            foreach ($images as $image) {
                $filename = basename($image);
                if (!empty($filename) && $filename !== 'test.jpg') {
                    $requiredImages['car-images'][] = $filename;
                }
            }
        }
    }
    
    // Driver images
    $drivers = DB::table('drivers')->whereNotNull('profile_image_url')->get();
    foreach ($drivers as $driver) {
        $filename = basename($driver->profile_image_url);
        if (!empty($filename) && $filename !== 'test.jpg') {
            $requiredImages['driver-profiles'][] = $filename;
        }
    }
    
    // Copy missing images
    echo "📁 COPYING MISSING IMAGES...\n";
    
    $copiedCount = 0;
    
    // Copy car images
    if (isset($requiredImages['car-images'])) {
        foreach ($requiredImages['car-images'] as $filename) {
            $targetPath = $carImagesDir . '/' . $filename;
            
            if (!file_exists($targetPath)) {
                if (isset($foundImages[$filename])) {
                    copy($foundImages[$filename], $targetPath);
                    echo "✅ Copied car image: {$filename}\n";
                    $copiedCount++;
                } else {
                    echo "❌ Missing car image: {$filename}\n";
                }
            } else {
                echo "✓ Car image exists: {$filename}\n";
            }
        }
    }
    
    // Copy driver images
    if (isset($requiredImages['driver-profiles'])) {
        foreach ($requiredImages['driver-profiles'] as $filename) {
            $targetPath = $driverProfilesDir . '/' . $filename;
            
            if (!file_exists($targetPath)) {
                if (isset($foundImages[$filename])) {
                    copy($foundImages[$filename], $targetPath);
                    echo "✅ Copied driver image: {$filename}\n";
                    $copiedCount++;
                } else {
                    echo "❌ Missing driver image: {$filename}\n";
                }
            } else {
                echo "✓ Driver image exists: {$filename}\n";
            }
        }
    }
    
    // Set permissions
    chmod($carImagesDir, 0755);
    chmod($driverProfilesDir, 0755);
    
    if (is_dir($carImagesDir)) {
        $carImages = glob($carImagesDir . '/*');
        foreach ($carImages as $image) {
            chmod($image, 0644);
        }
    }
    
    if (is_dir($driverProfilesDir)) {
        $driverImages = glob($driverProfilesDir . '/*');
        foreach ($driverImages as $image) {
            chmod($image, 0644);
        }
    }
    
    echo "\n🎉 COPYING COMPLETE!\n";
    echo "Copied {$copiedCount} images\n";
    echo "Car images: " . count(glob($carImagesDir . '/*')) . " files\n";
    echo "Driver images: " . count(glob($driverProfilesDir . '/*')) . " files\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
