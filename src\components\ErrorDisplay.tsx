import React from 'react';

interface ErrorDisplayProps {
  error: string | null;
  suggestions?: string[];
  className?: string;
}

interface SuccessDisplayProps {
  message: string | null;
  className?: string;
}

// Component to display detailed error messages with suggestions
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  suggestions = [], 
  className = '' 
}) => {
  if (!error) return null;
  
  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-red-400" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <div className="text-red-800 font-semibold text-sm">
            {error}
          </div>
          
          {suggestions.length > 0 && (
            <div className="mt-2 text-red-700 text-sm">
              <div className="font-medium mb-1">Suggestions:</div>
              <ul className="list-disc list-inside space-y-1">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="text-xs">{suggestion}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Component to display success messages
export const SuccessDisplay: React.FC<SuccessDisplayProps> = ({ 
  message, 
  className = '' 
}) => {
  if (!message) return null;
  
  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 mb-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-green-400" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        <div className="ml-3">
          <div className="text-green-800 font-semibold text-sm">
            {message}
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook to manage error and success states
export const useNotifications = () => {
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);
  const [suggestions, setSuggestions] = React.useState<string[]>([]);
  
  const clearNotifications = () => {
    setError(null);
    setSuccess(null);
    setSuggestions([]);
  };
  
  const showError = (message: string, suggestionList: string[] = []) => {
    setError(message);
    setSuggestions(suggestionList);
    setSuccess(null);
  };
  
  const showSuccess = (message: string) => {
    setSuccess(message);
    setError(null);
    setSuggestions([]);
  };
  
  return {
    error,
    success,
    suggestions,
    showError,
    showSuccess,
    clearNotifications,
    setError,
    setSuccess,
    setSuggestions
  };
};
