<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DriverController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Driver::with('user')->where('is_available', true);

        // Apply filters if provided
        if ($request->has('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->has('min_price')) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        if ($request->has('min_experience')) {
            $query->where('experience', '>=', $request->min_experience);
        }

        if ($request->has('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        if ($request->has('specialties')) {
            $specialties = explode(',', $request->specialties);
            foreach ($specialties as $specialty) {
                $query->whereJsonContains('specialties', trim($specialty));
            }
        }

        $drivers = $query->get();

        return response()->json($drivers);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user is a driver
        if (auth()->user()->role !== 'driver' && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Only users with driver role can register as drivers'], 403);
        }

        // Check if driver profile already exists
        $existingDriver = Driver::where('user_id', auth()->id())->first();
        if ($existingDriver) {
            return response()->json(['message' => 'Driver profile already exists'], 422);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:18|max:100',
            'experience' => 'required|integer|min:0',
            'profile_image' => 'sometimes|file|image|mimes:jpeg,png,jpg,gif|max:2048',
            'license_number' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'price_per_hour' => 'required|numeric|min:0',
            'specialties' => 'required|string', // JSON string from frontend
            'availability_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle profile image upload
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $filename = Str::random(40) . '.' . $request->file('profile_image')->getClientOriginalExtension();
            $request->file('profile_image')->storeAs('driver-profiles', $filename, 'public_direct');
            $profileImagePath = url('/driver-profiles/' . $filename);
        }

        // Parse specialties JSON
        $specialties = json_decode($request->specialties, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return response()->json(['errors' => ['specialties' => ['Invalid specialties format']]], 422);
        }

        $driver = Driver::create([
            'user_id' => auth()->id(),
            'name' => $request->name,
            'age' => $request->age,
            'experience' => $request->experience,
            'profile_image' => $profileImagePath,
            'license_number' => $request->license_number,
            'license_verification_status' => 'pending',
            'location' => $request->location,
            'price_per_hour' => $request->price_per_hour,
            'rating' => 0,
            'reviews' => 0,
            'specialties' => $specialties,
            'availability_notes' => $request->availability_notes,
            'is_available' => true,
        ]);

        return response()->json([
            'message' => 'Driver profile created successfully',
            'driver' => $driver
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $driver = Driver::with('user')->findOrFail($id);

        return response()->json($driver);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $driver = Driver::findOrFail($id);

        // Check if user is the owner of the driver profile or an admin
        if (auth()->id() != $driver->user_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'age' => 'sometimes|integer|min:18|max:100',
            'experience' => 'sometimes|integer|min:0',
            'profile_image' => 'sometimes|file|image|mimes:jpeg,png,jpg,gif|max:2048',
            'license_number' => 'sometimes|string|max:255',
            'location' => 'sometimes|string|max:255',
            'price_per_hour' => 'sometimes|numeric|min:0',
            'specialties' => 'sometimes|string', // JSON string from frontend
            'availability_notes' => 'sometimes|nullable|string',
            'is_available' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Prepare data for update
        $updateData = $request->except(['profile_image', 'specialties']);

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $filename = Str::random(40) . '.' . $request->file('profile_image')->getClientOriginalExtension();
            $request->file('profile_image')->storeAs('driver-profiles', $filename, 'public_direct');
            $updateData['profile_image'] = url('/driver-profiles/' . $filename);
        }

        // Handle specialties if provided
        if ($request->has('specialties')) {
            $specialties = json_decode($request->specialties, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $updateData['specialties'] = $specialties;
            }
        }

        $driver->update($updateData);

        return response()->json([
            'message' => 'Driver profile updated successfully',
            'driver' => $driver
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $driver = Driver::findOrFail($id);

        // Check if user is the owner of the driver profile or an admin
        if (auth()->id() != $driver->user_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $driver->delete();

        return response()->json([
            'message' => 'Driver profile deleted successfully'
        ]);
    }

    /**
     * Get the driver profile of the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function myProfile()
    {
        $driver = Driver::where('user_id', auth()->id())->first();

        if (!$driver) {
            return response()->json(['message' => 'Driver profile not found'], 404);
        }

        return response()->json($driver);
    }

    /**
     * Toggle the availability status of a driver.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleAvailability(string $id)
    {
        $driver = Driver::findOrFail($id);

        // Check if user is the owner of the driver profile or an admin
        if (auth()->id() != $driver->user_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $driver->update([
            'is_available' => !$driver->is_available
        ]);

        return response()->json([
            'message' => 'Driver availability updated successfully',
            'is_available' => $driver->is_available
        ]);
    }

    /**
     * Verify a driver's license (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyLicense(Request $request, string $id)
    {
        // Check if user is an admin
        if (auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,verified,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $driver = Driver::findOrFail($id);
        $driver->update([
            'license_verification_status' => $request->status
        ]);

        // Also update the user's license verification status
        $user = User::find($driver->user_id);
        $user->update([
            'license_verification_status' => $request->status
        ]);

        return response()->json([
            'message' => 'Driver license verification status updated successfully',
            'status' => $request->status
        ]);
    }
}
