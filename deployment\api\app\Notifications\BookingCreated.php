<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingCreated extends Notification implements ShouldQueue
{
    use Queueable;

    public $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $itemType = $this->booking->item_type === 'car' ? 'Car Rental' : 'Driver Service';
        $startDate = $this->booking->start_time->format('M j, Y \a\t g:i A');
        $endDate = $this->booking->end_time->format('M j, Y \a\t g:i A');

        return (new MailMessage)
            ->subject('New Booking Request - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have received a new booking request.')
            ->line('**Booking Details:**')
            ->line('Type: ' . $itemType)
            ->line('Booking ID: #' . $this->booking->id)
            ->line('Customer: ' . $this->booking->user->name)
            ->line('Customer Email: ' . $this->booking->user->email)
            ->line('Customer Phone: ' . ($this->booking->user->phone_number ?: 'Not provided'))
            ->line('Start: ' . $startDate)
            ->line('End: ' . $endDate)
            ->line('Total Price: RWF ' . number_format($this->booking->total_price))
            ->line('Status: ' . ucfirst($this->booking->status))
            ->action('View Booking Details', url('/bookings/' . $this->booking->id))
            ->line('Please review and respond to this booking request promptly.')
            ->line('If you have any questions, please contact <NAME_EMAIL> or 0788613669.')
            ->line('Thank you for using Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'customer_id' => $this->booking->user_id,
            'type' => 'booking_created',
            'title' => 'New Booking Request',
            'message' => 'You have received a new ' . ($this->booking->item_type === 'car' ? 'car rental' : 'driver service') . ' booking request from ' . $this->booking->user->name . '.',
            'action_url' => '/bookings/' . $this->booking->id,
        ];
    }
}
