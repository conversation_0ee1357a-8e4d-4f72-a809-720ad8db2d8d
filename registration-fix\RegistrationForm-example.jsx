import React, { useState } from 'react';
import { ErrorDisplay, SuccessDisplay, useRegistration } from './ErrorHandler';

const RegistrationForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'client',
    phone_number: ''
  });
  
  const { register, error, success, suggestions, loading } = useRegistration();
  
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const result = await register(formData);
    
    if (result.success) {
      // Registration successful
      console.log('User registered:', result.data.user);
      console.log('Token:', result.data.token);
      
      // Redirect or update UI
      // window.location.href = '/dashboard';
    }
  };
  
  return (
    <div className="registration-form">
      <h2>Register for Park & Rent</h2>
      
      {/* Error Display */}
      <ErrorDisplay error={error} suggestions={suggestions} />
      
      {/* Success Display */}
      <SuccessDisplay message={success} />
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="name">Full Name *</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            placeholder="Enter your full name"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="email">Email Address *</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            placeholder="Enter your email address"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="password">Password *</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            required
            placeholder="Enter a password (minimum 8 characters)"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="password_confirmation">Confirm Password *</label>
          <input
            type="password"
            id="password_confirmation"
            name="password_confirmation"
            value={formData.password_confirmation}
            onChange={handleChange}
            required
            placeholder="Confirm your password"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="role">Account Type *</label>
          <select
            id="role"
            name="role"
            value={formData.role}
            onChange={handleChange}
            required
          >
            <option value="client">Client (Rent Cars)</option>
            <option value="owner">Owner (Rent Out Cars)</option>
            <option value="driver">Driver (Offer Driving Services)</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="phone_number">Phone Number</label>
          <input
            type="tel"
            id="phone_number"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleChange}
            placeholder="Enter your phone number (optional)"
          />
        </div>
        
        <button 
          type="submit" 
          disabled={loading}
          className="submit-button"
        >
          {loading ? 'Creating Account...' : 'Register'}
        </button>
      </form>
      
      <div className="login-link">
        Already have an account? <a href="/login">Login here</a>
      </div>
    </div>
  );
};

export default RegistrationForm;
