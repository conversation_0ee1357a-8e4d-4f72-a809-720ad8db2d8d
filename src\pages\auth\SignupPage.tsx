import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Car, AlertCircle, UserRound } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import { useAuth } from '../../context/AuthContext';
import { ErrorDisplay } from '../../components/ErrorDisplay';

const SignupPage: React.FC = () => {
  const { register, isLoading } = useAuth();
  const navigate = useNavigate();

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [userType, setUserType] = useState<'client' | 'owner' | 'driver'>('client');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Clear any stale auth data when signup page loads to prevent infinite loops
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const user = localStorage.getItem('user');

    if (token || user) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if ((userType === 'owner' || userType === 'driver') && !phoneNumber) {
      setError('Phone number is required for car owners and drivers');
      return;
    }

    try {
      await register(
        {
          name,
          email,
          role: userType,
          phoneNumber: (userType === 'owner' || userType === 'driver') ? phoneNumber : undefined,
        },
        password
      );

      if (userType === 'client') {
        navigate('/account/verification');
      } else if (userType === 'owner') {
        navigate('/owner/dashboard');
      } else if (userType === 'driver') {
        navigate('/driver/register');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during registration');
    }
  };

  return (
    <MainLayout>
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <div className="flex justify-center space-x-2">
              <Car className="h-12 w-12 text-primary-600" />
              <UserRound className="h-12 w-12 text-primary-600" />
            </div>
            <h1 className="mt-4 text-3xl font-bold text-gray-900">Create your account</h1>
            <p className="mt-2 text-gray-600">
              Join Park & Rent to rent cars, list your car, or register as a driver.
            </p>
          </div>

          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <ErrorDisplay error={error} />

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="mb-4">
                <span className="block text-sm font-medium text-gray-700 mb-1">
                  I want to:
                </span>
                <div className="flex flex-wrap gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="userType"
                      value="client"
                      checked={userType === 'client'}
                      onChange={() => setUserType('client')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 block text-sm text-gray-700">
                      Rent a car
                    </span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="userType"
                      value="owner"
                      checked={userType === 'owner'}
                      onChange={() => setUserType('owner')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 block text-sm text-gray-700">
                      List my car
                    </span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="userType"
                      value="driver"
                      checked={userType === 'driver'}
                      onChange={() => setUserType('driver')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 block text-sm text-gray-700">
                      Register as driver
                    </span>
                  </label>
                </div>
              </div>

              <Input
                id="name"
                label="Full Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />

              <Input
                id="email"
                type="email"
                label="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />

              {(userType === 'owner' || userType === 'driver') && (
                <Input
                  id="phone"
                  label="Phone Number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  required
                />
              )}

              <Input
                id="password"
                type="password"
                label="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />

              <Input
                id="confirmPassword"
                type="password"
                label="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />

              <div className="flex items-center">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
                  I agree to the{' '}
                  <Link to="/terms" className="font-medium text-primary-600 hover:text-primary-500">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link to="/privacy" className="font-medium text-primary-600 hover:text-primary-500">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <Button
                type="submit"
                fullWidth
                disabled={isLoading}
              >
                {isLoading ? 'Creating account...' : 'Sign up'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link to="/login" className="font-medium text-primary-600 hover:text-primary-500">
                  Log in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SignupPage;