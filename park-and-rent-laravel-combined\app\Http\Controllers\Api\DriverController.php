<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DriverController extends Controller
{
    /**
     * Display a listing of drivers.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Driver::where('is_available', true)->with('user');

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('location', 'like', "%{$search}%")
                      ->orWhere('bio', 'like', "%{$search}%");
                });
            }

            // Filter by location
            if ($request->has('location') && $request->location) {
                $query->where('location', 'like', "%{$request->location}%");
            }

            // Filter by price range
            if ($request->has('min_price') && $request->min_price) {
                $query->where('price_per_hour', '>=', $request->min_price);
            }

            if ($request->has('max_price') && $request->max_price) {
                $query->where('price_per_hour', '<=', $request->max_price);
            }

            // Sort options
            $sortBy = $request->get('sort_by', 'rating');
            switch ($sortBy) {
                case 'price_low':
                    $query->orderBy('price_per_hour', 'asc');
                    break;
                case 'price_high':
                    $query->orderBy('price_per_hour', 'desc');
                    break;
                case 'experience':
                    $query->orderBy('experience', 'desc');
                    break;
                case 'rating':
                default:
                    $query->orderBy('rating', 'desc');
                    break;
            }

            $drivers = $query->paginate($request->get('per_page', 12));

            return response()->json([
                'success' => true,
                'data' => $drivers->items(),
                'pagination' => [
                    'current_page' => $drivers->currentPage(),
                    'last_page' => $drivers->lastPage(),
                    'per_page' => $drivers->perPage(),
                    'total' => $drivers->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch drivers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified driver.
     */
    public function show(Driver $driver): JsonResponse
    {
        try {
            $driver->load('user');
            
            return response()->json([
                'success' => true,
                'data' => $driver
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch driver details',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
