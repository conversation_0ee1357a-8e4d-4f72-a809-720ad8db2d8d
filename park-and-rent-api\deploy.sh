#!/bin/bash

# Park & Rent API - Deployment Script
# This script helps deploy the updated API to production

echo "🚀 Park & Rent API Deployment Script"
echo "======================================"

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Please run this script from the Laravel project root directory"
    exit 1
fi

echo "📋 Pre-deployment checklist:"
echo "1. ✅ Updated AuthController with detailed error messages"
echo "2. ✅ Updated filesystem configuration for direct public storage"
echo "3. ✅ Updated .env configuration"
echo "4. ✅ Updated .htaccess with comprehensive security and CORS"
echo "5. ✅ Updated CarController and DriverController for direct storage"
echo ""

# Create necessary directories
echo "📁 Creating upload directories..."
mkdir -p public/car-images
mkdir -p public/driver-profiles
echo "✅ Upload directories created"

# Set proper permissions (if on Linux/Unix)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🔒 Setting directory permissions..."
    chmod 755 public/car-images
    chmod 755 public/driver-profiles
    chmod 644 public/.htaccess
    echo "✅ Permissions set"
fi

# Clear Laravel caches
echo "🧹 Clearing Laravel caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear
echo "✅ Caches cleared"

# Optimize for production
echo "⚡ Optimizing for production..."
php artisan config:cache
php artisan route:cache
echo "✅ Optimization complete"

echo ""
echo "🎉 Local preparation complete!"
echo ""
echo "📤 Next steps for production deployment:"
echo "1. Upload the entire project to your hosting server"
echo "2. Update your .env file with production database credentials"
echo "3. Run 'composer install --no-dev --optimize-autoloader' on the server"
echo "4. Run 'php artisan migrate' to update the database"
echo "5. Create the storage symlink: ln -s ../../../storage/app/public public/storage"
echo "6. Test the API endpoints"
echo ""
echo "🔗 Important URLs to test:"
echo "- API Base: https://ebisera.com/api/"
echo "- Cars: https://ebisera.com/api/cars"
echo "- Register: https://ebisera.com/api/register"
echo "- Login: https://ebisera.com/api/login"
echo ""
echo "✨ Deployment preparation complete!"
