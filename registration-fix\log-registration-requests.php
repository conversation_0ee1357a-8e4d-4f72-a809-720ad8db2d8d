<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CREATING REQUEST LOGGER FOR REGISTRATION ===\n\n";

try {
    // Create a middleware to log registration requests
    $middlewarePath = app_path('Http/Middleware/LogRegistrationRequests.php');
    
    $middlewareContent = '<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LogRegistrationRequests
{
    public function handle(Request $request, Closure $next)
    {
        // Only log registration requests
        if ($request->is("api/register")) {
            Log::info("Registration Request", [
                "method" => $request->method(),
                "url" => $request->fullUrl(),
                "headers" => $request->headers->all(),
                "body" => $request->all(),
                "ip" => $request->ip(),
                "user_agent" => $request->userAgent()
            ]);
        }
        
        $response = $next($request);
        
        // Log response for registration
        if ($request->is("api/register")) {
            Log::info("Registration Response", [
                "status" => $response->getStatusCode(),
                "content" => $response->getContent()
            ]);
        }
        
        return $response;
    }
}';
    
    // Create middleware directory if it doesn\'t exist
    $middlewareDir = dirname($middlewarePath);
    if (!is_dir($middlewareDir)) {
        mkdir($middlewareDir, 0755, true);
    }
    
    file_put_contents($middlewarePath, $middlewareContent);
    echo "✅ Created LogRegistrationRequests middleware\n";
    
    // Create a temporary route to test specific validation
    $testRoutePath = 'routes/test-registration.php';
    
    $testRouteContent = '<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

Route::post("/api/test-validation", function (Request $request) {
    // Same validation as AuthController
    $validator = Validator::make($request->all(), [
        "name" => "required|string|max:255",
        "email" => "required|string|email|max:255|unique:users",
        "password" => ["required", "confirmed", \Illuminate\Validation\Rules\Password::defaults()],
        "role" => "required|in:client,owner,driver",
        "phone_number" => "nullable|string|max:20",
        "license_image_url" => "nullable|string",
    ]);

    if ($validator->fails()) {
        return response()->json([
            "message" => "Validation failed",
            "errors" => $validator->errors(),
            "received_data" => $request->all(),
            "validation_rules" => [
                "name" => "required|string|max:255",
                "email" => "required|string|email|max:255|unique:users",
                "password" => "required|confirmed|Password::defaults()",
                "role" => "required|in:client,owner,driver",
                "phone_number" => "nullable|string|max:20",
                "license_image_url" => "nullable|string",
            ]
        ], 422);
    }

    return response()->json([
        "message" => "Validation passed",
        "received_data" => $request->all()
    ]);
});';
    
    file_put_contents($testRoutePath, $testRouteContent);
    echo "✅ Created test validation route: /api/test-validation\n";
    
    echo "\n📋 NEXT STEPS:\n";
    echo "1. Run: php debug-frontend-request.php\n";
    echo "2. Test frontend registration and check browser DevTools\n";
    echo "3. Test validation endpoint: POST /api/test-validation\n";
    echo "4. Check Laravel logs: tail -f storage/logs/laravel.log\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
