<?php

namespace Database\Seeders;

use App\Models\Driver;
use App\Models\User;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the driver user
        $driverUser = User::where('email', '<EMAIL>')->first();

        if (!$driverUser) {
            return;
        }

        // Create driver profile
        Driver::create([
            'user_id' => $driverUser->id,
            'name' => 'John Driver',
            'age' => 35,
            'experience' => 10,
            'profile_image' => 'https://example.com/driver1.jpg',
            'license_number' => '**********',
            'license_verification_status' => 'verified',
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 10000, // RWF
            'rating' => 4.8,
            'reviews' => 24,
            'specialties' => [
                'City Driving',
                'Long Distance',
                'Tour Guide',
                'English Speaking',
            ],
            'availability_notes' => 'Available on weekdays and weekends',
            'is_available' => true,
        ]);

        // Create additional drivers
        $additionalDrivers = [
            [
                'name' => 'Alice Driver',
                'age' => 28,
                'experience' => 5,
                'profile_image' => 'https://example.com/driver2.jpg',
                'license_number' => '**********',
                'license_verification_status' => 'verified',
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 8000, // RWF
                'rating' => 4.5,
                'reviews' => 15,
                'specialties' => [
                    'City Driving',
                    'Airport Transfers',
                    'French Speaking',
                ],
                'availability_notes' => 'Available on weekdays only',
                'is_available' => true,
            ],
            [
                'name' => 'Robert Driver',
                'age' => 42,
                'experience' => 15,
                'profile_image' => 'https://example.com/driver3.jpg',
                'license_number' => '**********',
                'license_verification_status' => 'verified',
                'location' => 'Musanze, Rwanda',
                'price_per_hour' => 12000, // RWF
                'rating' => 4.9,
                'reviews' => 32,
                'specialties' => [
                    'Off-Road Driving',
                    'Safari Tours',
                    'Mountain Regions',
                    'English Speaking',
                    'Kinyarwanda Speaking',
                ],
                'availability_notes' => 'Available for multi-day trips',
                'is_available' => true,
            ],
        ];

        // Create additional driver users and profiles
        foreach ($additionalDrivers as $index => $driverData) {
            $user = User::create([
                'name' => $driverData['name'],
                'email' => 'driver' . ($index + 2) . '@parkandrent.com',
                'password' => bcrypt('password'),
                'role' => 'driver',
                'email_verified_at' => now(),
                'license_image_url' => 'https://example.com/license' . ($index + 2) . '.jpg',
                'license_verification_status' => 'verified',
            ]);

            $driverData['user_id'] = $user->id;
            Driver::create($driverData);
        }
    }
}
