import axios from 'axios';

// API Configuration
// For production on Hostinger, the Laravel API is deployed at domain root
// For development, use local Laravel server
export const API_BASE_URL = import.meta.env.VITE_API_URL || (import.meta.env.PROD ? 'https://ebisera.com/api' : 'http://127.0.0.1:8000/api');

// Create axios instance with default configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000, // 15 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: false, // Don't send cookies for CORS
});

// Add request logging for debugging (simplified)
apiClient.interceptors.request.use(
  (config) => {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('API Request:', config.method?.toUpperCase(), (config.baseURL || '') + (config.url || ''));
    }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('API Response:', response.status, `${Array.isArray(response.data) ? response.data.length + ' items' : 'data'}`);
    }
    return response;
  },
  (error) => {
    // Enhanced error logging
    console.error('API Error Details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method,
      baseURL: error.config?.baseURL,
      data: error.response?.data
    });

    // Handle network errors
    if (!error.response) {
      console.error('Network Error: Unable to connect to API server');
      console.error('Check if the API server is running and accessible at:', API_BASE_URL);
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Clear auth data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');

      // Only redirect to login if:
      // 1. Not already on login/signup pages
      // 2. Not a public API endpoint (cars, drivers can be viewed without auth)
      const isAuthPage = window.location.pathname.includes('/login') || window.location.pathname.includes('/signup');
      const isPublicEndpoint = error.config?.url?.includes('/cars') || error.config?.url?.includes('/drivers');

      if (!isAuthPage && !isPublicEndpoint) {
        window.location.href = '/login';
      }
    }

    // Handle 404 errors
    if (error.response?.status === 404) {
      console.error('API Endpoint not found:', error.config?.url);
    }

    // Handle 500 errors
    if (error.response?.status === 500) {
      console.error('Server Error: Please check the Laravel application logs');
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  REGISTER: '/register',
  LOGIN: '/login',
  LOGOUT: '/logout',
  USER: '/user',

  // User endpoints
  USERS: '/users',
  UPDATE_PASSWORD: '/users/update-password',

  // Car endpoints
  CARS: '/cars',
  MY_CARS: '/my-cars',

  // Driver endpoints
  DRIVERS: '/drivers',
  MY_DRIVER_PROFILE: '/my-driver-profile',

  // Booking endpoints
  BOOKINGS: '/bookings',
  MY_BOOKINGS: '/my-bookings',

  // Chat endpoints
  CHATS: '/chats',
  MY_CHATS: '/my-chats',
  UNREAD_COUNT: '/unread-messages-count',

  // Message endpoints
  MESSAGES: '/messages',
  MARK_AS_READ: '/messages/mark-as-read',

  // Owner Dashboard endpoints
  OWNER: {
    DASHBOARD: '/owner/dashboard',
    MESSAGES: '/owner/messages',
    CHATS: '/owner/chats',
    BOOKINGS: '/owner/bookings',
  },

  // Admin endpoints
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    CARS: '/admin/cars',
    DRIVERS: '/admin/drivers',
    BOOKINGS: '/admin/bookings',
    REVENUE: '/admin/revenue',
  },
};

// API Health Check Function
export const checkApiHealth = async () => {
  try {
    console.log('Checking API health at:', API_BASE_URL);
    const response = await apiClient.get('/health');
    console.log('API Health Check Success:', response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error('API Health Check Failed:', error);
    return { success: false, error };
  }
};

// Test API Connection (for debugging)
export const testApiConnection = async () => {
  try {
    console.log('Testing API connection to:', API_BASE_URL);

    // Try to fetch cars (public endpoint)
    const response = await apiClient.get('/cars');
    console.log('API Connection Test Success - Cars endpoint working');
    return { success: true, endpoint: '/cars', data: response.data };
  } catch (error) {
    console.error('API Connection Test Failed:', error);

    // Try alternative endpoint
    try {
      const healthResponse = await fetch(`${API_BASE_URL}/health`);
      if (healthResponse.ok) {
        console.log('Health endpoint accessible via fetch');
        return { success: true, endpoint: '/health', method: 'fetch' };
      }
    } catch (fetchError) {
      console.error('Fetch also failed:', fetchError);
    }

    return { success: false, error };
  }
};

