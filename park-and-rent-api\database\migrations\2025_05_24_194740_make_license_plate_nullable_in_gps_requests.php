<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gps_installation_requests', function (Blueprint $table) {
            $table->string('license_plate')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gps_installation_requests', function (Blueprint $table) {
            $table->string('license_plate')->nullable(false)->change();
        });
    }
};
