import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Car, UserRound, Calendar, Clock, DollarSign, MapPin, Star, Edit, AlertCircle, XCircle } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useCars } from '../../context/CarContext';
import { useDrivers } from '../../context/DriverContext';
import { apiClient, API_ENDPOINTS } from '../../config/api';
import { useAuth } from '../../context/AuthContext';
import Spinner from '../../components/ui/Spinner';
import { Booking } from '../../types';

const ClientDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { cars } = useCars();
  const { drivers } = useDrivers();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [chats, setChats] = useState<any[]>([]);
  const [selectedChat, setSelectedChat] = useState<any>(null);
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingBookings, setIsLoadingBookings] = useState(true);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [activeTab, setActiveTab] = useState<'bookings' | 'messages' | 'profile'>('bookings');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch user bookings from API
  const fetchUserBookings = async () => {
    if (!isAuthenticated || !user) {
      setIsLoadingBookings(false);
      return;
    }

    try {
      setIsLoadingBookings(true);
      const response = await apiClient.get(API_ENDPOINTS.MY_BOOKINGS);

      // Convert backend format to frontend format
      const frontendBookings = (response.data || []).map((booking: any) => ({
        id: booking.id,
        userId: booking.user_id,
        itemType: booking.item_type,
        itemId: booking.item_id,
        startTime: booking.start_time,
        endTime: booking.end_time,
        totalPrice: parseFloat(booking.total_price) || 0,
        status: booking.status,
        notes: booking.notes,
        createdAt: booking.created_at,
      }));

      setBookings(frontendBookings);
    } catch (error) {
      console.error('Failed to fetch user bookings:', error);
    } finally {
      setIsLoadingBookings(false);
    }
  };

  // Fetch user chats from API
  const fetchUserChats = async () => {
    if (!isAuthenticated || !user) {
      setIsLoadingChats(false);
      return;
    }

    try {
      setIsLoadingChats(true);
      const response = await apiClient.get(API_ENDPOINTS.MY_CHATS);
      setChats(response.data || []);
    } catch (error) {
      console.error('Failed to fetch user chats:', error);
    } finally {
      setIsLoadingChats(false);
    }
  };

  // Cancel booking function
  const cancelBooking = async (bookingId: string) => {
    try {
      await apiClient.patch(`${API_ENDPOINTS.BOOKINGS}/${bookingId}`, { status: 'cancelled' });
      // Update local state
      setBookings(prev => prev.map(booking =>
        booking.id === bookingId ? { ...booking, status: 'cancelled' } : booking
      ));
    } catch (error) {
      console.error('Failed to cancel booking:', error);
      throw error;
    }
  };

  // Use bookings directly (no filtering needed since API returns user's bookings)
  const clientBookings = bookings;

  // Fetch bookings when component mounts or authentication changes
  useEffect(() => {
    // Only fetch if authenticated and not loading
    if (isAuthenticated && !isLoading) {
      fetchUserBookings();
    }
  }, [isAuthenticated, isLoading]);

  // Fetch chats when messages tab is active
  useEffect(() => {
    if (activeTab === 'messages') {
      fetchUserChats();
      fetchUnreadCount();
    }
  }, [activeTab, isAuthenticated]);

  // Fetch unread count when component mounts
  useEffect(() => {
    fetchUnreadCount();
  }, [isAuthenticated]);

  const handleCancelBooking = async (bookingId: string) => {
    setIsLoading(true);
    try {
      await cancelBooking(bookingId);
    } catch (error) {
      console.error('Failed to cancel booking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch unread message count
  const fetchUnreadCount = async () => {
    if (!isAuthenticated || !user) return;

    try {
      const response = await apiClient.get(API_ENDPOINTS.UNREAD_COUNT);
      setUnreadCount(response.data.count || 0);
    } catch (error) {
      console.error('Failed to fetch unread count:', error);
    }
  };

  // Fetch messages for a specific chat
  const fetchChatMessages = async (chatId: number) => {
    try {
      setIsLoadingMessages(true);
      const response = await apiClient.get(`${API_ENDPOINTS.CHATS}/${chatId}`);
      setChatMessages(response.data.messages || []);
      // Scroll to bottom after messages load
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (error) {
      console.error('Failed to fetch chat messages:', error);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  // Send a new message
  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedChat) return;

    try {
      setIsSendingMessage(true);
      const response = await apiClient.post(API_ENDPOINTS.MESSAGES, {
        chat_id: selectedChat.id,
        content: newMessage.trim()
      });

      // Add the new message to the chat
      setChatMessages(prev => [...prev, response.data]);
      setNewMessage('');

      // Scroll to bottom after sending message
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

      // Refresh the chat list to update last message and unread count
      fetchUserChats();
      fetchUnreadCount();
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSendingMessage(false);
    }
  };

  // Handle chat selection
  const handleChatSelect = (chat: any) => {
    setSelectedChat(chat);
    fetchChatMessages(chat.id);
    // Refresh unread count after opening chat (messages will be marked as read)
    setTimeout(() => {
      fetchUnreadCount();
    }, 500);
  };

  // Helper function to get item details
  const getItemDetails = (booking: Booking) => {
    if (booking.itemType === 'car') {
      const car = cars.find(c => c.id === String(booking.itemId));
      return car ? {
        name: `${car.year} ${car.make} ${car.model}`,
        image: car.images[0],
        location: car.location,
        price: car.pricePerHour
      } : null;
    } else {
      const driver = drivers.find(d => d.id === String(booking.itemId));
      return driver ? {
        name: driver.name,
        image: driver.profileImage,
        location: driver.location,
        price: driver.pricePerHour
      } : null;
    }
  };

  if (!user || user.role !== 'client') {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Access Denied</h2>
            <p className="text-error-600">
              You must be logged in as a client to access this page.
            </p>
            <Button
              className="mt-4"
              onClick={() => navigate('/login')}
            >
              Log In
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Account</h1>
            <p className="text-gray-600">
              Manage your bookings and account information.
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex space-x-3">
            <Link to="/cars">
              <Button variant="outline">
                <Car size={18} className="mr-2" />
                Browse Cars
              </Button>
            </Link>
            <Link to="/drivers">
              <Button variant="outline">
                <UserRound size={18} className="mr-2" />
                Hire Drivers
              </Button>
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="flex border-b border-gray-200">
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'bookings'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('bookings')}
            >
              My Bookings
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none relative ${
                activeTab === 'messages'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('messages')}
            >
              Messages
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'profile'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </div>

          {activeTab === 'bookings' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Your Bookings</h2>

              {isLoadingBookings ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading your bookings...</p>
                </div>
              ) : clientBookings.length === 0 ? (
                <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings yet</h3>
                  <p className="text-gray-600 mb-6">
                    You haven't made any bookings yet. Browse our cars and drivers to get started.
                  </p>
                  <div className="flex flex-col sm:flex-row justify-center gap-3">
                    <Link to="/cars">
                      <Button>
                        Browse Cars
                      </Button>
                    </Link>
                    <Link to="/drivers">
                      <Button variant="outline">
                        Hire Drivers
                      </Button>
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-6">
                  {clientBookings.map((booking) => {
                    const itemDetails = getItemDetails(booking);
                    const startDate = new Date(booking.startTime);
                    const endDate = new Date(booking.endTime);
                    const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);

                    if (!itemDetails) return null;

                    return (
                      <div key={booking.id} className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="flex flex-col md:flex-row">
                          <div className="md:w-1/4 h-48 md:h-auto">
                            <img
                              src={itemDetails.image}
                              alt={itemDetails.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="p-6 md:w-3/4">
                            <div className="flex flex-col md:flex-row justify-between mb-4">
                              <div>
                                <h3 className="text-lg font-bold text-gray-900 mb-1">
                                  {booking.itemType === 'car' ? 'Car Rental: ' : 'Driver Hire: '}
                                  {itemDetails.name}
                                </h3>
                                <div className="flex items-center text-gray-600 mb-2">
                                  <MapPin size={16} className="mr-1" />
                                  <span className="text-sm">{itemDetails.location}</span>
                                </div>
                              </div>
                              <div className="mt-2 md:mt-0">
                                <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  booking.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : booking.status === 'confirmed'
                                    ? 'bg-blue-100 text-blue-800'
                                    : booking.status === 'completed'
                                    ? 'bg-success-100 text-success-800'
                                    : 'bg-error-100 text-error-800'
                                }`}>
                                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                              <div>
                                <div className="text-sm text-gray-500 mb-1">Date</div>
                                <div className="font-medium">
                                  {startDate.toLocaleDateString()}
                                </div>
                              </div>
                              <div>
                                <div className="text-sm text-gray-500 mb-1">Time</div>
                                <div className="font-medium">
                                  {startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -
                                  {endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </div>
                              </div>
                              <div>
                                <div className="text-sm text-gray-500 mb-1">Duration</div>
                                <div className="font-medium">
                                  {durationHours} {durationHours === 1 ? 'hour' : 'hours'}
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <div className="font-bold text-lg text-primary-600">
                                ${booking.totalPrice.toFixed(2)}
                              </div>

                              {(booking.status === 'pending' || booking.status === 'confirmed') && (
                                <Button
                                  variant="outline"
                                  className="text-error-600 border-error-600 hover:bg-error-50"
                                  onClick={() => handleCancelBooking(booking.id)}
                                  disabled={isLoading}
                                >
                                  {isLoading ? <Spinner size="sm" className="mr-2" /> : <XCircle size={16} className="mr-2" />}
                                  Cancel Booking
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="p-6">
              {!selectedChat ? (
                // Chat List View
                <>
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Your Messages</h2>

                  {isLoadingChats ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-gray-600 mt-4">Loading messages...</p>
                    </div>
                  ) : chats.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                      <p className="text-gray-600 mb-4">
                        Start a conversation by messaging a car owner when you're interested in booking their vehicle.
                      </p>
                      <Link to="/cars">
                        <Button>
                          Browse Cars
                        </Button>
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {chats.map((chat: any) => {
                        const otherUser = chat.user?.id === user?.id ? chat.recipient : chat.user;
                        const lastMessage = chat.messages?.[0];

                        return (
                          <div
                            key={chat.id}
                            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                            onClick={() => handleChatSelect(chat)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center mb-2">
                                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-gray-600 font-semibold">
                                      {otherUser?.name?.charAt(0)?.toUpperCase() || '?'}
                                    </span>
                                  </div>
                                  <div>
                                    <h3 className="font-medium text-gray-900">{otherUser?.name || 'Unknown User'}</h3>
                                    <p className="text-sm text-gray-500">{otherUser?.email}</p>
                                  </div>
                                </div>
                                {lastMessage && (
                                  <div className="ml-13">
                                    <p className="text-gray-700 text-sm mb-1">{lastMessage.content}</p>
                                    <p className="text-xs text-gray-500">
                                      {new Date(lastMessage.created_at).toLocaleDateString()} at {new Date(lastMessage.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                    </p>
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="text-xs text-gray-400">
                                  Chat #{String(chat.id).slice(0, 8)}
                                </div>
                                {chat.unread_count > 0 && (
                                  <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    {chat.unread_count > 99 ? '99+' : chat.unread_count}
                                  </span>
                                )}
                              </div>
                            </div>
                            {chat.related_to_type && (
                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <p className="text-xs text-gray-500">
                                  Related to: {chat.related_to_type === 'car' ? 'Car Booking' : 'Driver Booking'}
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </>
              ) : (
                // Chat Detail View
                <>
                  <div className="flex items-center mb-4">
                    <button
                      onClick={() => setSelectedChat(null)}
                      className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">
                        {selectedChat.user?.id === user?.id ? selectedChat.recipient?.name : selectedChat.user?.name}
                      </h2>
                      <p className="text-sm text-gray-500">
                        {selectedChat.user?.id === user?.id ? selectedChat.recipient?.email : selectedChat.user?.email}
                      </p>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto mb-4" style={{ scrollBehavior: 'smooth' }}>
                    {isLoadingMessages ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-600 mt-2">Loading messages...</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {chatMessages.map((message: any) => {
                          const isCurrentUser = String(message.sender_id) === String(user?.id);
                          const senderName = isCurrentUser
                            ? 'You'
                            : (selectedChat.user?.id === user?.id ? selectedChat.recipient?.name : selectedChat.user?.name);

                          return (
                            <div
                              key={message.id}
                              className={`flex ${
                                isCurrentUser ? 'justify-end' : 'justify-start'
                              }`}
                            >
                              <div className={`max-w-xs lg:max-w-md ${
                                isCurrentUser ? 'order-2' : 'order-1'
                              }`}>
                                {/* Sender Label */}
                                <div className={`text-xs mb-1 ${
                                  isCurrentUser ? 'text-right text-blue-600' : 'text-left text-gray-600'
                                }`}>
                                  {senderName} {isCurrentUser ? '(Client)' : '(Owner)'}
                                </div>

                                {/* Message Bubble */}
                                <div
                                  className={`px-4 py-3 rounded-lg shadow-sm ${
                                    isCurrentUser
                                      ? 'bg-blue-600 text-white rounded-br-sm'
                                      : 'bg-green-100 text-gray-900 border border-green-200 rounded-bl-sm'
                                  }`}
                                >
                                  <p className="text-sm leading-relaxed">{message.content}</p>
                                  <p className={`text-xs mt-2 ${
                                    isCurrentUser ? 'text-blue-100' : 'text-green-600'
                                  }`}>
                                    {new Date(message.created_at).toLocaleDateString()} at {new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                  </p>
                                </div>
                              </div>

                              {/* Avatar */}
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
                                isCurrentUser
                                  ? 'bg-blue-100 text-blue-600 ml-2 order-3'
                                  : 'bg-green-100 text-green-600 mr-2 order-0'
                              }`}>
                                {isCurrentUser ? 'C' : 'O'}
                              </div>
                            </div>
                          );
                        })}
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </div>

                  {/* Message Input */}
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      placeholder="Type your message..."
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={isSendingMessage}
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || isSendingMessage}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSendingMessage ? 'Sending...' : 'Send'}
                    </button>
                  </div>
                </>
              )}
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="p-6">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/3 mb-6 md:mb-0 md:pr-6">
                  <div className="bg-gray-100 rounded-lg p-6 text-center">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden mb-4 bg-gray-300 flex items-center justify-center">
                      {user.licenseImageUrl ? (
                        <img
                          src={user.licenseImageUrl}
                          alt={user.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <UserRound size={64} className="text-gray-400" />
                      )}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{user.name}</h3>
                    <p className="text-gray-500 mb-4">{user.email}</p>
                    <Link to="/account/edit-profile">
                      <Button variant="outline" className="w-full flex items-center justify-center">
                        <Edit size={16} className="mr-2" />
                        Edit Profile
                      </Button>
                    </Link>
                  </div>
                </div>

                <div className="md:w-2/3">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Account Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">Account Type</div>
                      <div className="font-medium">Client</div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">Member Since</div>
                      <div className="font-medium">
                        {new Date(user.createdAt).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">Phone Number</div>
                      <div className="font-medium">
                        {user.phoneNumber || 'Not provided'}
                        {user.phoneNumber && user.isPhoneVerified && (
                          <span className="ml-2 text-xs bg-success-100 text-success-800 px-2 py-0.5 rounded-full">
                            Verified
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 mb-1">License Verification</div>
                      <div className="font-medium">
                        {user.licenseVerificationStatus ? (
                          <span className={`${
                            user.licenseVerificationStatus === 'verified'
                              ? 'text-success-600'
                              : user.licenseVerificationStatus === 'pending'
                              ? 'text-yellow-600'
                              : 'text-error-600'
                          }`}>
                            {user.licenseVerificationStatus.charAt(0).toUpperCase() +
                             user.licenseVerificationStatus.slice(1)}
                          </span>
                        ) : (
                          'Not submitted'
                        )}
                      </div>
                    </div>
                  </div>

                  {!user.licenseVerificationStatus && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                      <div className="flex">
                        <AlertCircle size={20} className="text-yellow-600 mr-2 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-yellow-800 mb-1">Verify Your License</h4>
                          <p className="text-yellow-700 text-sm mb-3">
                            To rent cars, you need to verify your driver's license. This helps ensure safety and trust in our community.
                          </p>
                          <Link to="/account/verification">
                            <Button size="sm">
                              Verify License
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">Payment Methods</h4>
                    <p className="text-gray-600 mb-3">
                      Park & Rent doesn't handle payments directly. All payments are arranged between you and the car owner or driver.
                    </p>
                    <p className="text-sm text-gray-500">
                      We recommend discussing payment methods and terms before confirming any booking.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default ClientDashboardPage;
