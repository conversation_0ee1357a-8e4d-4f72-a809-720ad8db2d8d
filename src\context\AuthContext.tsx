import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '../types';
import axios from 'axios';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<User>, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is logged in from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (err) {
        console.error('Error parsing stored user:', err);
        localStorage.removeItem('user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Make the API request
      const response = await apiClient.post(API_ENDPOINTS.LOGIN, { email, password });

      // If successful, the API will return the user data and token
      if (response.data && response.data.user) {
        // Transform the API response to match our User type
        const loggedInUser: User = {
          id: response.data.user.id.toString(),
          email: response.data.user.email,
          name: response.data.user.name,
          role: response.data.user.role,
          phoneNumber: response.data.user.phone_number,
          isPhoneVerified: response.data.user.is_phone_verified,
          licenseImageUrl: response.data.user.license_image_url,
          licenseVerificationStatus: response.data.user.license_verification_status,
          createdAt: response.data.user.created_at,
        };

        // Store the token for future authenticated requests
        localStorage.setItem('auth_token', response.data.token);

        // Update the state with the user data
        setUser(loggedInUser);
        localStorage.setItem('user', JSON.stringify(loggedInUser));
      } else {
        throw new Error('Login failed: Invalid response from server');
      }
    } catch (err) {
      // Handle API errors with detailed error messages
      if (axios.isAxiosError(err) && err.response) {
        const responseData = err.response.data;

        if (responseData && responseData.message) {
          // Use the main user-friendly message from the API
          setError(responseData.message);
        } else if (responseData && responseData.errors) {
          // Fallback to field errors if no main message
          const errorMessages = Object.values(responseData.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else {
          setError(`Login failed: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'An error occurred during login');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: Partial<User>, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare the data for the API
      const registrationData = {
        name: userData.name,
        email: userData.email,
        password: password,
        password_confirmation: password, // Laravel requires password confirmation
        role: userData.role || 'client',
        phone_number: userData.phoneNumber,
        license_image_url: userData.licenseImageUrl,
      };

      // Make the API request
      const response = await apiClient.post(API_ENDPOINTS.REGISTER, registrationData);

      // If successful, the API will return the user data and token
      if (response.data && response.data.user) {
        // Transform the API response to match our User type
        const newUser: User = {
          id: response.data.user.id.toString(),
          email: response.data.user.email,
          name: response.data.user.name,
          role: response.data.user.role,
          phoneNumber: response.data.user.phone_number,
          isPhoneVerified: response.data.user.is_phone_verified,
          licenseImageUrl: response.data.user.license_image_url,
          licenseVerificationStatus: response.data.user.license_verification_status,
          createdAt: response.data.user.created_at,
        };

        // Store the token for future authenticated requests
        localStorage.setItem('auth_token', response.data.token);

        // Update the state with the user data
        setUser(newUser);
        localStorage.setItem('user', JSON.stringify(newUser));
      } else {
        throw new Error('Registration failed: Invalid response from server');
      }
    } catch (err) {
      // Handle API errors
      console.error('Registration error:', err);
      if (axios.isAxiosError(err) && err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        // Handle detailed error responses from updated API
        const responseData = err.response.data;

        if (responseData && responseData.message) {
          // Use the main user-friendly message from the API
          setError(responseData.message);
        } else if (responseData && responseData.errors) {
          // Fallback to field errors if no main message
          const errorMessages = Object.values(responseData.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else {
          setError(`Registration failed: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'An error occurred during registration');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Get the auth token
      const token = localStorage.getItem('auth_token');

      if (token) {
        // Make the API request (apiClient will automatically add the auth header)
        await apiClient.post(API_ENDPOINTS.LOGOUT);
      }
    } catch (err) {
      console.error('Error during logout:', err);
    } finally {
      // Clear user data and token regardless of API success
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('No user is logged in');
      }

      // Transform userData to match API expectations
      const apiUserData = {
        name: userData.name,
        email: userData.email,
        phone_number: userData.phoneNumber,
        license_image_url: userData.licenseImageUrl,
      };

      // Make the API request (apiClient will automatically add the auth header)
      const response = await apiClient.put(`${API_ENDPOINTS.USERS}/${user.id}`, apiUserData);

      // If successful, the API will return the updated user data
      if (response.data && response.data.user) {
        // Transform the API response to match our User type
        const updatedUser: User = {
          ...user,
          name: response.data.user.name || user.name,
          email: response.data.user.email || user.email,
          phoneNumber: response.data.user.phone_number,
          licenseImageUrl: response.data.user.license_image_url,
        };

        // Update the state with the user data
        setUser(updatedUser);
        localStorage.setItem('user', JSON.stringify(updatedUser));
      } else {
        throw new Error('Update failed: Invalid response from server');
      }
    } catch (err) {
      // Handle API errors with detailed error messages
      if (axios.isAxiosError(err) && err.response) {
        const responseData = err.response.data;

        if (responseData && responseData.message) {
          // Use the main user-friendly message from the API
          setError(responseData.message);
        } else if (responseData && responseData.errors) {
          // Fallback to field errors if no main message
          const errorMessages = Object.values(responseData.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else {
          setError(`Update failed: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'An error occurred while updating user data');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    register,
    logout,
    updateUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};