<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Park & Rent</title>
    <script type="module" crossorigin src="/assets/index-3tZepKQW.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-UTipGiiB.js">
    <link rel="modulepreload" crossorigin href="/assets/router-De09AHy8.js">
    <link rel="stylesheet" crossorigin href="/assets/index-Q0eq-75S.css">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body>
    <div id="root"></div>

    <!-- API Configuration Override -->
    <script>
      // Override API configuration for production
      window.API_BASE_URL = 'https://ebisera.com/api/public/api';
      window.API_URL = 'https://ebisera.com/api/public/api';
      window.VITE_API_URL = 'https://ebisera.com/api/public/api';

      // Override any localhost references
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string') {
          // Replace localhost URLs with production URLs
          url = url.replace('http://localhost:3000/api', 'https://ebisera.com/api/public/api');
          url = url.replace('http://localhost:8000/api', 'https://ebisera.com/api/public/api');
          url = url.replace('http://127.0.0.1:8000/api', 'https://ebisera.com/api/public/api');
          url = url.replace('/api/api/', '/api/public/api/');
        }
        return originalFetch.call(this, url, options);
      };

      console.log('API Configuration Override Applied:', window.API_BASE_URL);
    </script>

  </body>
</html>