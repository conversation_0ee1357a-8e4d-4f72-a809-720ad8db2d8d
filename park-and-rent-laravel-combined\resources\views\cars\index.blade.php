@extends('layouts.app')

@section('title', 'Browse Cars')

@section('content')
<div class="bg-white">
    <!-- Header -->
    <div class="bg-primary-600">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold text-white sm:text-5xl">
                    Browse Cars
                </h1>
                <p class="mt-4 text-xl text-primary-100">
                    Find the perfect car for your next trip
                </p>
            </div>
        </div>
    </div>

    <!-- Advanced Search & Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow rounded-lg">
            <!-- Filter Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Search & Filter Cars</h3>
                    <button type="button" id="toggleAdvancedFilters" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        Advanced Filters
                    </button>
                </div>
            </div>

            <form method="GET" action="{{ route('cars.index') }}" class="p-6">
                <!-- Basic Search -->
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               placeholder="Make, model, or location..."
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                        <select name="location" id="location" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            <option value="">All Locations</option>
                            @foreach($filterOptions['locations'] as $location)
                                <option value="{{ $location }}" {{ request('location') == $location ? 'selected' : '' }}>{{ $location }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="available_from" class="block text-sm font-medium text-gray-700">Available From</label>
                        <input type="date" name="available_from" id="available_from" value="{{ request('available_from') }}"
                               min="{{ date('Y-m-d') }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="available_to" class="block text-sm font-medium text-gray-700">Available To</label>
                        <input type="date" name="available_to" id="available_to" value="{{ request('available_to') }}"
                               min="{{ date('Y-m-d') }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                    </div>
                </div>

                <!-- Advanced Filters (Hidden by default) -->
                <div id="advancedFilters" class="hidden border-t border-gray-200 pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <!-- Car Make -->
                        <div>
                            <label for="make" class="block text-sm font-medium text-gray-700">Make</label>
                            <select name="make" id="make" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">All Makes</option>
                                @foreach($filterOptions['makes'] as $make)
                                    <option value="{{ $make }}" {{ request('make') == $make ? 'selected' : '' }}>{{ $make }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Transmission -->
                        <div>
                            <label for="transmission" class="block text-sm font-medium text-gray-700">Transmission</label>
                            <select name="transmission" id="transmission" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">All Transmissions</option>
                                @foreach($filterOptions['transmissions'] as $transmission)
                                    <option value="{{ $transmission }}" {{ request('transmission') == $transmission ? 'selected' : '' }}>{{ ucfirst($transmission) }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Fuel Type -->
                        <div>
                            <label for="fuel_type" class="block text-sm font-medium text-gray-700">Fuel Type</label>
                            <select name="fuel_type" id="fuel_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">All Fuel Types</option>
                                @foreach($filterOptions['fuel_types'] as $fuelType)
                                    <option value="{{ $fuelType }}" {{ request('fuel_type') == $fuelType ? 'selected' : '' }}>{{ ucfirst($fuelType) }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Minimum Seats -->
                        <div>
                            <label for="seats" class="block text-sm font-medium text-gray-700">Minimum Seats</label>
                            <select name="seats" id="seats" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="">Any</option>
                                @foreach($filterOptions['seat_options'] as $seatCount)
                                    <option value="{{ $seatCount }}" {{ request('seats') == $seatCount ? 'selected' : '' }}>{{ $seatCount }}+ seats</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="min_price" class="block text-sm font-medium text-gray-700">Min Price/Day (${{ $filterOptions['price_range']['min'] }} - ${{ $filterOptions['price_range']['max'] }})</label>
                            <input type="range" name="min_price" id="min_price"
                                   min="{{ $filterOptions['price_range']['min'] }}"
                                   max="{{ $filterOptions['price_range']['max'] }}"
                                   value="{{ request('min_price', $filterOptions['price_range']['min']) }}"
                                   class="mt-1 block w-full">
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>${{ $filterOptions['price_range']['min'] }}</span>
                                <span id="minPriceValue">${{ request('min_price', $filterOptions['price_range']['min']) }}</span>
                            </div>
                        </div>
                        <div>
                            <label for="max_price" class="block text-sm font-medium text-gray-700">Max Price/Day</label>
                            <input type="range" name="max_price" id="max_price"
                                   min="{{ $filterOptions['price_range']['min'] }}"
                                   max="{{ $filterOptions['price_range']['max'] }}"
                                   value="{{ request('max_price', $filterOptions['price_range']['max']) }}"
                                   class="mt-1 block w-full">
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span id="maxPriceValue">${{ request('max_price', $filterOptions['price_range']['max']) }}</span>
                                <span>${{ $filterOptions['price_range']['max'] }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Year Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="min_year" class="block text-sm font-medium text-gray-700">Min Year</label>
                            <input type="number" name="min_year" id="min_year"
                                   min="{{ $filterOptions['year_range']['min'] }}"
                                   max="{{ $filterOptions['year_range']['max'] }}"
                                   value="{{ request('min_year') }}"
                                   placeholder="{{ $filterOptions['year_range']['min'] }}"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="max_year" class="block text-sm font-medium text-gray-700">Max Year</label>
                            <input type="number" name="max_year" id="max_year"
                                   min="{{ $filterOptions['year_range']['min'] }}"
                                   max="{{ $filterOptions['year_range']['max'] }}"
                                   value="{{ request('max_year') }}"
                                   placeholder="{{ $filterOptions['year_range']['max'] }}"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Features</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                            @foreach($filterOptions['features'] as $feature)
                                <label class="flex items-center">
                                    <input type="checkbox" name="features[]" value="{{ $feature }}"
                                           {{ in_array($feature, request('features', [])) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">{{ $feature }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="has_gps" value="1"
                                       {{ request('has_gps') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">GPS Tracking Available</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Sort and Actions -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700">Sort by</label>
                            <select name="sort_by" id="sort_by" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                                <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                <option value="year_new" {{ request('sort_by') == 'year_new' ? 'selected' : '' }}>Year: Newest First</option>
                                <option value="year_old" {{ request('sort_by') == 'year_old' ? 'selected' : '' }}>Year: Oldest First</option>
                            </select>
                        </div>
                    </div>
                <div class="flex space-x-2">
                    <a href="{{ route('cars.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
                        Clear
                    </a>
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900">
                {{ $cars->total() }} {{ Str::plural('car', $cars->total()) }} found
            </h2>
        </div>

        @if($cars->count() > 0)
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                @foreach($cars as $car)
                <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                    <div class="h-48 bg-gray-200">
                        @if($car->images && count($car->images) > 0)
                            <img class="h-full w-full object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                        @else
                            <div class="h-full w-full flex items-center justify-center bg-gray-300">
                                <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ $car->make }} {{ $car->model }}</h3>
                            <span class="text-sm text-gray-500">{{ $car->year }}</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-600 flex items-center">
                            <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ $car->location }}
                        </p>
                        <p class="mt-2 text-sm text-gray-600">{{ Str::limit($car->description, 80) }}</p>

                        @if($car->features && count($car->features) > 0)
                        <div class="mt-3 flex flex-wrap gap-1">
                            @foreach(array_slice($car->features, 0, 3) as $feature)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                {{ $feature }}
                            </span>
                            @endforeach
                            @if(count($car->features) > 3)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                +{{ count($car->features) - 3 }} more
                            </span>
                            @endif
                        </div>
                        @endif

                        <div class="mt-4 flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-primary-600">${{ $car->price_per_hour }}</span>
                                <span class="text-sm text-gray-500">/hour</span>
                            </div>
                            <a href="{{ route('cars.show', $car) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $cars->withQueryString()->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No cars found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
                <div class="mt-6">
                    <a href="{{ route('cars.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        View All Cars
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
