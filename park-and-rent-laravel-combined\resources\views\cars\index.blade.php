@extends('layouts.app')

@section('title', 'Browse Cars')

@section('content')
<div class="bg-white">
    <!-- Header -->
    <div class="bg-primary-600">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold text-white sm:text-5xl">
                    Browse Cars
                </h1>
                <p class="mt-4 text-xl text-primary-100">
                    Find the perfect car for your next trip
                </p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form method="GET" action="{{ route('cars.index') }}" class="bg-gray-50 p-6 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="Make, model, or location..." 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" name="location" id="location" value="{{ request('location') }}" 
                           placeholder="Enter location..." 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="min_price" class="block text-sm font-medium text-gray-700">Min Price/Hour</label>
                    <input type="number" name="min_price" id="min_price" value="{{ request('min_price') }}" 
                           placeholder="$0" min="0" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="max_price" class="block text-sm font-medium text-gray-700">Max Price/Hour</label>
                    <input type="number" name="max_price" id="max_price" value="{{ request('max_price') }}" 
                           placeholder="$1000" min="0" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
            </div>
            <div class="mt-4 flex justify-between items-center">
                <div>
                    <label for="sort_by" class="block text-sm font-medium text-gray-700">Sort by</label>
                    <select name="sort_by" id="sort_by" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                        <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="year_new" {{ request('sort_by') == 'year_new' ? 'selected' : '' }}>Year: Newest First</option>
                        <option value="year_old" {{ request('sort_by') == 'year_old' ? 'selected' : '' }}>Year: Oldest First</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <a href="{{ route('cars.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
                        Clear
                    </a>
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900">
                {{ $cars->total() }} {{ Str::plural('car', $cars->total()) }} found
            </h2>
        </div>

        @if($cars->count() > 0)
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                @foreach($cars as $car)
                <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                    <div class="h-48 bg-gray-200">
                        @if($car->images && count($car->images) > 0)
                            <img class="h-full w-full object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                        @else
                            <div class="h-full w-full flex items-center justify-center bg-gray-300">
                                <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ $car->make }} {{ $car->model }}</h3>
                            <span class="text-sm text-gray-500">{{ $car->year }}</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-600 flex items-center">
                            <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ $car->location }}
                        </p>
                        <p class="mt-2 text-sm text-gray-600">{{ Str::limit($car->description, 80) }}</p>
                        
                        @if($car->features && count($car->features) > 0)
                        <div class="mt-3 flex flex-wrap gap-1">
                            @foreach(array_slice($car->features, 0, 3) as $feature)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                {{ $feature }}
                            </span>
                            @endforeach
                            @if(count($car->features) > 3)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                +{{ count($car->features) - 3 }} more
                            </span>
                            @endif
                        </div>
                        @endif

                        <div class="mt-4 flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-primary-600">${{ $car->price_per_hour }}</span>
                                <span class="text-sm text-gray-500">/hour</span>
                            </div>
                            <a href="{{ route('cars.show', $car) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $cars->withQueryString()->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No cars found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
                <div class="mt-6">
                    <a href="{{ route('cars.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        View All Cars
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
