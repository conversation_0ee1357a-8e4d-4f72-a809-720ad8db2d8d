import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Car, Plus, Edit, Trash2, MapPin, Clock, DollarSign, ToggleLeft as Toggle, AlertCircle, MessageCircle, Calendar, TrendingUp, Users } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useCars } from '../../context/CarContext';
import { useAuth } from '../../context/AuthContext';
import { apiClient, API_ENDPOINTS } from '../../config/api';

interface DashboardStats {
  total_cars: number;
  active_cars: number;
  total_bookings: number;
  pending_bookings: number;
  confirmed_bookings: number;
  total_revenue: number;
  unread_messages: number;
}

interface Chat {
  id: string;
  user: { id: string; name: string; email: string };
  recipient: { id: string; name: string; email: string };
  messages: Array<{
    id: string;
    content: string;
    sender_id: string;
    created_at: string;
  }>;
  last_message_at: string;
}

const OwnerDashboardPage: React.FC = () => {
  const { ownerCars, fetchOwnerCars, updateCar, deleteCar } = useCars();
  const { user, isAuthenticated } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [carToDelete, setCarToDelete] = useState<string | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentChats, setRecentChats] = useState<Chat[]>([]);
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    if (!isAuthenticated) {
      console.log('User not authenticated, skipping dashboard data fetch');
      setIsLoadingStats(false);
      return;
    }

    try {
      setIsLoadingStats(true);
      console.log('Fetching dashboard data...');
      const response = await apiClient.get(API_ENDPOINTS.OWNER.DASHBOARD);
      console.log('Dashboard API response:', response.data);

      if (response.data.data) {
        setDashboardStats(response.data.data.stats);
        setRecentChats(response.data.data.recent_chats || []);
        console.log('Dashboard stats set:', response.data.data.stats);
      } else {
        console.error('No data in dashboard response:', response.data);
      }
    } catch (error: any) {
      console.error('Failed to fetch dashboard data:', error);
      if (error.response) {
        console.error('Error response:', error.response.status, error.response.data);
      }
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Fetch owner cars when component mounts
  useEffect(() => {
    if (isAuthenticated) {
      fetchOwnerCars();
      fetchDashboardData();
    }
  }, [isAuthenticated]);

  const handleToggleActive = async (carId: string, currentStatus: boolean) => {
    await updateCar(carId, { isActive: !currentStatus });
  };

  const confirmDelete = (carId: string) => {
    setCarToDelete(carId);
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    if (carToDelete) {
      await deleteCar(carToDelete);
      setShowDeleteModal(false);
      setCarToDelete(null);
    }
  };

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <AlertCircle size={64} className="mx-auto text-red-500 mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
            <p className="text-gray-600 mb-6">
              You need to be logged in as an owner to access this dashboard.
            </p>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Test credentials: <br />
                <strong>Email:</strong> <EMAIL> <br />
                <strong>Password:</strong> password
              </p>
              <Link to="/login">
                <Button className="mx-auto">
                  Go to Login
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Owner Dashboard</h1>
            <p className="text-gray-600">
              Manage your car listings and track rental requests.
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex gap-3">
            <Link to="/owner/messages">
              <Button variant="secondary" className="flex items-center">
                <MessageCircle size={18} className="mr-2" />
                Messages
                {dashboardStats && dashboardStats.unread_messages > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {dashboardStats.unread_messages}
                  </span>
                )}
              </Button>
            </Link>
            <Link to="/owner/add-car">
              <Button className="flex items-center">
                <Plus size={18} className="mr-2" />
                Add New Car
              </Button>
            </Link>
          </div>
        </div>

        {/* API Status Alert */}
        {isLoadingStats && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
              <div className="flex-1">
                <p className="text-blue-800 font-medium">
                  Loading dashboard data...
                </p>
                <p className="text-blue-600 text-sm mt-1">
                  If this takes too long, make sure the Laravel API server is running on port 8000.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Dashboard Stats */}
        {!isLoadingStats && dashboardStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="p-6 cursor-pointer hover:shadow-lg transition-shadow" onClick={() => {
              const carsSection = document.querySelector('[data-section="cars"]');
              if (carsSection) {
                carsSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}>
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Car className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-500">Total Cars</h3>
                  <p className="text-2xl font-bold text-gray-900">{dashboardStats.total_cars}</p>
                </div>
              </div>
            </Card>

            <Link to="/owner/bookings" className="block">
              <Card className="p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-full">
                    <Calendar className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-500">Total Bookings</h3>
                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.total_bookings}</p>
                  </div>
                </div>
              </Card>
            </Link>

            <Link to="/owner/bookings" className="block">
              <Card className="p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-500">Pending Bookings</h3>
                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.pending_bookings}</p>
                  </div>
                </div>
              </Card>
            </Link>

            <Link to="/owner/messages" className="block">
              <Card className="p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-full">
                    <MessageCircle className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-500">Unread Messages</h3>
                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.unread_messages}</p>
                  </div>
                </div>
              </Card>
            </Link>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md p-6 mb-8" data-section="cars">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Your Listed Cars</h2>

          {ownerCars.length === 0 ? (
            <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
              <Car size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No cars listed yet</h3>
              <p className="text-gray-600 mb-6">
                Start earning by adding your first car listing.
              </p>
              <Link to="/owner/add-car">
                <Button>
                  Add Your Car
                </Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Car
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {ownerCars.map((car) => (
                    <tr key={car.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-16 flex-shrink-0 bg-gray-200 rounded overflow-hidden">
                            {car.images.length > 0 && (
                              <img
                                src={car.images[0]}
                                alt={`${car.make} ${car.model}`}
                                className="h-full w-full object-cover"
                              />
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {car.year} {car.make} {car.model}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-500">
                          <MapPin size={16} className="mr-1" />
                          {car.location}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm font-medium text-gray-900">
                          <DollarSign size={16} className="text-primary-600" />
                          {car.pricePerHour}/hour
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleToggleActive(car.id, car.isActive)}
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            car.isActive
                              ? 'bg-success-100 text-success-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          <Toggle size={14} className={`mr-1 ${car.isActive ? 'text-success-600' : 'text-gray-500'}`} />
                          {car.isActive ? 'Active' : 'Inactive'}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link to={`/owner/edit-car/${car.id}`}>
                            <button className="text-primary-600 hover:text-primary-800">
                              <Edit size={18} />
                            </button>
                          </Link>
                          <button
                            className="text-error-600 hover:text-error-800"
                            onClick={() => confirmDelete(car.id)}
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Quick Access Messages Section - Always visible */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <MessageCircle size={20} className="mr-2 text-blue-500" />
              Messages
            </h2>
            <Link to="/owner/messages">
              <Button variant="secondary" size="sm" className="flex items-center">
                <MessageCircle size={16} className="mr-2" />
                Open Messages
                {dashboardStats && dashboardStats.unread_messages > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {dashboardStats.unread_messages}
                  </span>
                )}
              </Button>
            </Link>
          </div>

          <div className="text-center py-6">
            <div className="bg-blue-50 rounded-lg p-6">
              <MessageCircle size={48} className="mx-auto text-blue-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Message Center</h3>
              <p className="text-gray-600 mb-4">
                Communicate with customers interested in your cars. Manage all your conversations in one place.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link to="/owner/messages">
                  <Button className="flex items-center">
                    <MessageCircle size={16} className="mr-2" />
                    View All Messages
                  </Button>
                </Link>
                <Button variant="secondary" className="flex items-center">
                  <Users size={16} className="mr-2" />
                  Customer Support
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Messages Section (API dependent) */}
        {!isLoadingStats && recentChats.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Recent Messages</h2>
              <Link to="/owner/messages">
                <Button variant="secondary" size="sm">
                  View All Messages
                </Button>
              </Link>
            </div>

            {recentChats.length > 0 ? (
              <div className="space-y-4">
                {recentChats.slice(0, 5).map((chat) => {
                const otherUser = chat.user.id === user?.id ? chat.recipient : chat.user;
                const lastMessage = chat.messages[0];
                const isUnread = lastMessage && lastMessage.sender_id !== user?.id;

                return (
                  <div key={chat.id} className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-gray-600" />
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className={`text-sm font-medium ${
                            isUnread ? 'text-gray-900 font-semibold' : 'text-gray-700'
                          }`}>
                            {otherUser.name}
                          </h4>
                          <p className={`text-sm ${
                            isUnread ? 'text-gray-900' : 'text-gray-500'
                          } truncate max-w-xs`}>
                            {lastMessage ? lastMessage.content : 'No messages yet'}
                          </p>
                        </div>
                        <div className="text-xs text-gray-400">
                          {lastMessage && new Date(lastMessage.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    {isUnread && (
                      <div className="flex-shrink-0">
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      </div>
                    )}
                  </div>
                );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <MessageCircle size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                <p className="text-gray-600">
                  Messages from customers interested in your cars will appear here.
                </p>
              </div>
            )}
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Apply for GPS Installation
          </h2>
          <p className="text-gray-600 mb-6">
            We offer GPS installation services for car owners who want to track their vehicles.
            Apply below to request this service.
          </p>
          <Link to="/owner/gps-request">
            <Button variant="secondary">
              Request GPS Installation
            </Button>
          </Link>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-error-100 sm:mx-0 sm:h-10 sm:w-10">
                    <AlertCircle className="h-6 w-6 text-error-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Delete Car Listing
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this car listing? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-error-600 text-base font-medium text-white hover:bg-error-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-error-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
};

export default OwnerDashboardPage;