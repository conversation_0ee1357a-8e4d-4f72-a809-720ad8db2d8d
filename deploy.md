# Park & Rent - Combined Frontend & Backend Deployment Guide

## Current Architecture
- **Frontend**: React + TypeScript + Vite (root directory)
- **Backend**: Laravel 12 API (park-and-rent-api directory)
- **Target**: Single domain deployment on Hostinger

## Deployment Strategy: Single Domain Approach

### Step 1: Prepare Frontend for Production

1. **Update API Configuration**
   - The frontend is already configured to use `/api/api` for production
   - Ensure VITE_API_URL is not set in production (uses default)

2. **Build React Frontend**
   ```bash
   npm run build
   ```
   This creates a `dist` folder with optimized static files.

### Step 2: Prepare Laravel Backend

1. **Navigate to Laravel directory**
   ```bash
   cd park-and-rent-api
   ```

2. **Install dependencies**
   ```bash
   composer install --optimize-autoloader --no-dev
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Set production environment variables in .env**
   ```
   APP_ENV=production
   APP_DEBUG=false
   APP_URL=https://ebisera.com
   
   # Database (MySQL on Hostinger)
   DB_CONNECTION=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   
   # CORS
   FRONTEND_URL=https://ebisera.com
   ```

5. **Run migrations**
   ```bash
   php artisan migrate --force
   php artisan db:seed --force
   ```

6. **Optimize Laravel**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

### Step 3: Hostinger Deployment Structure

```
public_html/
├── index.html (React app entry point)
├── assets/ (React build assets)
├── api/
│   ├── public/
│   │   └── index.php (Laravel entry point)
│   ├── app/
│   ├── config/
│   ├── database/
│   ├── routes/
│   ├── storage/
│   ├── vendor/
│   └── .env
└── .htaccess (Root rewrite rules)
```

### Step 4: Upload Files

1. **Upload React build files**
   - Copy contents of `dist/` to `public_html/`

2. **Upload Laravel API**
   - Copy entire `park-and-rent-api/` to `public_html/api/`
   - Move `park-and-rent-api/public/*` to `public_html/api/public/`

### Step 5: Configure .htaccess

Create root `.htaccess` in `public_html/`:
```apache
RewriteEngine On

# Handle API requests
RewriteRule ^api/(.*)$ api/public/index.php [L]

# Handle React Router (SPA)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]
```

### Step 6: Set Permissions

```bash
chmod -R 755 public_html/
chmod -R 775 public_html/api/storage/
chmod -R 775 public_html/api/bootstrap/cache/
```

## Alternative: Automated Deployment Script

Create a deployment script to automate the process:

```bash
#!/bin/bash

# Build frontend
echo "Building React frontend..."
npm run build

# Prepare Laravel
echo "Preparing Laravel backend..."
cd park-and-rent-api
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
cd ..

# Create deployment package
echo "Creating deployment package..."
mkdir -p deploy/public_html
cp -r dist/* deploy/public_html/
cp -r park-and-rent-api deploy/public_html/api
mv deploy/public_html/api/public/* deploy/public_html/api/public/

echo "Deployment package ready in ./deploy/"
```

## Benefits of This Approach

1. **No CORS Issues**: Same domain for frontend and backend
2. **Simple Routing**: Clean URLs for both app and API
3. **Easy Maintenance**: Single deployment process
4. **SEO Friendly**: React app served from root
5. **Cost Effective**: Single hosting account

## Testing

1. **Frontend**: https://ebisera.com
2. **API**: https://ebisera.com/api/cars
3. **Admin**: https://ebisera.com/admin

## Troubleshooting

- Check Laravel logs: `api/storage/logs/laravel.log`
- Verify database connection
- Ensure proper file permissions
- Check .htaccess syntax
