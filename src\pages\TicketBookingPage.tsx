import React from 'react';
import { Plane, Bus, MapPin, Clock, Shield, Headphones } from 'lucide-react';
import MainLayout from '../components/layout/MainLayout';
import TicketBookingForm from '../components/TicketBookingForm';

const TicketBookingPage: React.FC = () => {
  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Travel to East Africa
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100">
                Book flights and bus tickets to East African countries
              </p>
              <div className="flex justify-center space-x-8 text-sm">
                <div className="flex items-center">
                  <Plane className="h-5 w-5 mr-2" />
                  Flight Bookings
                </div>
                <div className="flex items-center">
                  <Bus className="h-5 w-5 mr-2" />
                  Bus Tickets
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  8 Countries
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-12 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Travel Service?
              </h2>
              <p className="text-lg text-gray-600">
                We make traveling to East Africa simple and affordable
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Quick Response</h3>
                <p className="text-gray-600">
                  Get pricing and availability within 24 hours of your booking request
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Secure & Reliable</h3>
                <p className="text-gray-600">
                  Safe and trusted travel arrangements with verified partners
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Headphones className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
                <p className="text-gray-600">
                  Our team is available to assist you throughout your journey
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Destinations Section */}
        <div className="py-12 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Popular Destinations
              </h2>
              <p className="text-lg text-gray-600">
                Explore beautiful East African countries
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { country: 'Uganda', city: 'Kampala', flag: '🇺🇬' },
                { country: 'Kenya', city: 'Nairobi', flag: '🇰🇪' },
                { country: 'Tanzania', city: 'Dar es Salaam', flag: '🇹🇿' },
                { country: 'Burundi', city: 'Bujumbura', flag: '🇧🇮' },
                { country: 'South Sudan', city: 'Juba', flag: '🇸🇸' },
                { country: 'Ethiopia', city: 'Addis Ababa', flag: '🇪🇹' },
                { country: 'Somalia', city: 'Mogadishu', flag: '🇸🇴' },
                { country: 'Djibouti', city: 'Djibouti City', flag: '🇩🇯' },
              ].map((destination) => (
                <div key={destination.country} className="bg-white rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow">
                  <div className="text-center">
                    <div className="text-3xl mb-2">{destination.flag}</div>
                    <h3 className="font-semibold text-gray-900">{destination.country}</h3>
                    <p className="text-sm text-gray-600">{destination.city}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Booking Form Section */}
        <div className="py-12 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <TicketBookingForm />
          </div>
        </div>

        {/* Contact Information */}
        <div className="py-12 bg-gray-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Need Help?</h2>
              <p className="text-gray-300 mb-6">
                Our travel experts are here to assist you
              </p>
              <div className="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8">
                <div className="flex items-center">
                  <Headphones className="h-5 w-5 mr-2" />
                  <span>0788613669</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TicketBookingPage;
