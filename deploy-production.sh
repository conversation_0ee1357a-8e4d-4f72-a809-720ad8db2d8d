#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Starting deployment process...${NC}"

# Create production environment files
echo -e "${GREEN}Creating production environment files...${NC}"

# Frontend .env.production
cat > .env.production << EOL
VITE_API_URL=https://ebisera.com/api
VITE_APP_NAME="Park & Rent"
VITE_APP_URL=https://ebisera.com
EOL

# Backend .env
cat > park-and-rent-api/.env << EOL
APP_NAME="Park & Rent"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://ebisera.com

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=u555127771_parkrent
DB_USERNAME=u555127771_parkrent
DB_PASSWORD=YOUR_DATABASE_PASSWORD

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.ebisera.com

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=YOUR_EMAIL_PASSWORD
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="\${APP_NAME}"

FRONTEND_URL=https://ebisera.com
SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com
EOL

# Build frontend
echo -e "${GREEN}Building frontend...${NC}"
npm install
npm run build

# Prepare Laravel backend
echo -e "${GREEN}Preparing Laravel backend...${NC}"
cd park-and-rent-api

# Install dependencies
composer install --optimize-autoloader --no-dev

# Generate application key if not exists
if [ ! -f .env ]; then
    php artisan key:generate
fi

# Optimize Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link

# Set permissions
chmod -R 755 .
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/

cd ..

# Create deployment package
echo -e "${GREEN}Creating deployment package...${NC}"
mkdir -p deployment
cp -r dist/* deployment/
cp -r park-and-rent-api deployment/api

# Create .htaccess for frontend
cat > deployment/.htaccess << EOL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-l
    RewriteRule . /index.html [L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
EOL

# Create .htaccess for API
cat > deployment/api/.htaccess << EOL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /api/
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Protect sensitive files
<FilesMatch "^\.env|composer\.json|composer\.lock|package\.json|package-lock\.json">
    Order allow,deny
    Deny from all
</FilesMatch>
EOL

echo -e "${GREEN}Deployment package created successfully!${NC}"
echo -e "${GREEN}The deployment package is in the 'deployment' directory${NC}"
echo -e "${GREEN}Upload the contents of the 'deployment' directory to your hosting provider${NC}"
echo -e "${RED}IMPORTANT: Don't forget to update the database credentials in deployment/api/.env${NC}" 