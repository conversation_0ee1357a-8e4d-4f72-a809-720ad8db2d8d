<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== FIXING REGISTRATION ISSUES ===\n\n";

try {
    echo "🔍 ANALYZING USERS TABLE...\n";
    
    // Get table structure
    $columns = DB::select("SHOW COLUMNS FROM users");
    $requiredFields = [];
    $optionalFields = [];
    
    foreach ($columns as $column) {
        if ($column->Null === 'NO' && $column->Default === null && $column->Extra !== 'auto_increment') {
            $requiredFields[] = $column->Field;
        } else {
            $optionalFields[] = $column->Field;
        }
    }
    
    echo "Required fields:\n";
    foreach ($requiredFields as $field) {
        echo "  ✓ {$field}\n";
    }
    
    echo "\nOptional fields:\n";
    foreach ($optionalFields as $field) {
        echo "  - {$field}\n";
    }
    
    echo "\n🔧 TESTING REGISTRATION DATA...\n";
    
    // Test different registration scenarios
    $testCases = [
        [
            'name' => 'Basic Registration',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123'
            ]
        ],
        [
            'name' => 'Registration with Role',
            'data' => [
                'name' => 'Test User 2',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Short Password Test',
            'data' => [
                'name' => 'Test User 3',
                'email' => '<EMAIL>',
                'password' => '123',
                'password_confirmation' => '123',
                'role' => 'client'
            ]
        ]
    ];
    
    foreach ($testCases as $test) {
        echo "\nTesting: {$test['name']}\n";
        
        // Check if all required fields are present
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (!isset($test['data'][$field]) && !in_array($field, ['email_verified_at', 'created_at', 'updated_at'])) {
                $missingFields[] = $field;
            }
        }
        
        if (empty($missingFields)) {
            echo "  ✅ All required fields present\n";
        } else {
            echo "  ❌ Missing fields: " . implode(', ', $missingFields) . "\n";
        }
        
        // Check email uniqueness
        $existingUser = DB::table('users')->where('email', $test['data']['email'])->first();
        if ($existingUser) {
            echo "  ⚠️ Email already exists\n";
        } else {
            echo "  ✅ Email is unique\n";
        }
        
        // Check password length
        if (isset($test['data']['password'])) {
            $passwordLength = strlen($test['data']['password']);
            if ($passwordLength >= 8) {
                echo "  ✅ Password length OK ({$passwordLength} chars)\n";
            } else {
                echo "  ❌ Password too short ({$passwordLength} chars, minimum 8)\n";
            }
        }
        
        // Check password confirmation
        if (isset($test['data']['password']) && isset($test['data']['password_confirmation'])) {
            if ($test['data']['password'] === $test['data']['password_confirmation']) {
                echo "  ✅ Password confirmation matches\n";
            } else {
                echo "  ❌ Password confirmation doesn't match\n";
            }
        }
    }
    
    echo "\n🔧 CREATING TEST USERS...\n";
    
    // Create test users for different scenarios
    $testUsers = [
        [
            'name' => 'Registration Test Client',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'client'
        ],
        [
            'name' => 'Registration Test Owner',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'owner'
        ]
    ];
    
    foreach ($testUsers as $userData) {
        $email = $userData['email'];
        
        // Delete if exists
        DB::table('users')->where('email', $email)->delete();
        
        $insertData = [
            'name' => $userData['name'],
            'email' => $email,
            'password' => Hash::make($userData['password']),
            'role' => $userData['role'],
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ];
        
        DB::table('users')->insert($insertData);
        echo "✅ Created test user: {$email} (password: {$userData['password']})\n";
    }
    
    echo "\n📋 REGISTRATION REQUIREMENTS SUMMARY:\n";
    echo "Required fields for registration:\n";
    echo "  - name (string, required)\n";
    echo "  - email (string, unique, required)\n";
    echo "  - password (string, min 8 chars, required)\n";
    echo "  - password_confirmation (string, must match password)\n";
    echo "Optional fields:\n";
    echo "  - role (string, defaults to 'client')\n";
    
    echo "\n🧪 CURL TEST COMMAND:\n";
    echo "curl -X POST https://ebisera.com/api/public/api/register \\\n";
    echo "  -H 'Content-Type: application/json' \\\n";
    echo "  -H 'Accept: application/json' \\\n";
    echo "  -d '{\n";
    echo "    \"name\": \"New User\",\n";
    echo "    \"email\": \"<EMAIL>\",\n";
    echo "    \"password\": \"password123\",\n";
    echo "    \"password_confirmation\": \"password123\",\n";
    echo "    \"role\": \"client\"\n";
    echo "  }'\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
