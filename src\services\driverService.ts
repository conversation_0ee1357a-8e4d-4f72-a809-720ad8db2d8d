import { apiClient } from '../config/api';
import { Driver, DriverFilters } from '../types';

export interface DriverResponse {
  success: boolean;
  data: Driver[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface DriverDetailResponse {
  success: boolean;
  data: Driver;
}

export const driverService = {
  // Get all drivers with filters
  getDrivers: async (filters?: DriverFilters): Promise<DriverResponse> => {
    const params = new URLSearchParams();
    
    if (filters?.search) params.append('search', filters.search);
    if (filters?.location) params.append('location', filters.location);
    if (filters?.min_price) params.append('min_price', filters.min_price.toString());
    if (filters?.max_price) params.append('max_price', filters.max_price.toString());
    if (filters?.min_experience) params.append('min_experience', filters.min_experience.toString());
    if (filters?.min_rating) params.append('min_rating', filters.min_rating.toString());
    if (filters?.sort_by) params.append('sort_by', filters.sort_by);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.per_page) params.append('per_page', filters.per_page.toString());
    
    if (filters?.specialties && filters.specialties.length > 0) {
      filters.specialties.forEach(specialty => {
        params.append('specialties[]', specialty);
      });
    }

    if (filters?.languages && filters.languages.length > 0) {
      filters.languages.forEach(language => {
        params.append('languages[]', language);
      });
    }

    const response = await apiClient.get(`/v1/drivers?${params.toString()}`);
    return response.data;
  },

  // Get driver by ID
  getDriver: async (id: string): Promise<DriverDetailResponse> => {
    const response = await apiClient.get(`/v1/drivers/${id}`);
    return response.data;
  },

  // Get my driver profile (authenticated)
  getMyProfile: async (): Promise<DriverDetailResponse> => {
    const response = await apiClient.get('/v1/my-driver-profile');
    return response.data;
  },

  // Update my driver profile (authenticated)
  updateMyProfile: async (profileData: Partial<Driver>): Promise<DriverDetailResponse> => {
    const response = await apiClient.post('/v1/my-driver-profile', profileData);
    return response.data;
  }
};
