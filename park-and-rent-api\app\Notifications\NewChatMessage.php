<?php

namespace App\Notifications;

use App\Models\Message;
use App\Models\Chat;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewChatMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public $message;
    public $chat;

    /**
     * Create a new notification instance.
     */
    public function __construct(Message $message, Chat $chat)
    {
        $this->message = $message;
        $this->chat = $chat;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $sender = $this->message->sender;
        $contextMessage = '';
        
        if ($this->chat->related_to_type && $this->chat->related_to_id) {
            $contextMessage = ' regarding your ' . $this->chat->related_to_type;
        }

        return (new MailMessage)
            ->subject('New Message from ' . $sender->name . ' - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have received a new message' . $contextMessage . '.')
            ->line('**Message from ' . $sender->name . ':**')
            ->line('"' . $this->message->content . '"')
            ->line('**Sender Details:**')
            ->line('Name: ' . $sender->name)
            ->line('Email: ' . $sender->email)
            ->line('Phone: ' . ($sender->phone_number ?: 'Not provided'))
            ->action('Reply to Message', url('/chats/' . $this->chat->id))
            ->line('You can reply directly through our platform.')
            ->line('Thank you for using Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message_id' => $this->message->id,
            'chat_id' => $this->chat->id,
            'sender_id' => $this->message->sender_id,
            'type' => 'new_message',
            'title' => 'New Message from ' . $this->message->sender->name,
            'message' => substr($this->message->content, 0, 100) . (strlen($this->message->content) > 100 ? '...' : ''),
            'action_url' => '/chats/' . $this->chat->id,
        ];
    }
}
