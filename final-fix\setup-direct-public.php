<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

echo "=== SETTING UP DIRECT PUBLIC ACCESS ===\n\n";

try {
    echo "🔧 STEP 1: CREATING PUBLIC DIRECTORIES...\n";
    
    // Create directories in public
    $directories = [
        public_path('car-images'),
        public_path('driver-profiles'),
        public_path('uploads'), // For any other uploads
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ Created: {$dir}\n";
        } else {
            echo "✓ Exists: {$dir}\n";
        }
    }
    
    echo "\n🔧 STEP 2: COPYING EXISTING IMAGES...\n";
    
    // Copy car images
    $sourceCarImages = storage_path('app/public/car-images');
    $targetCarImages = public_path('car-images');
    
    if (is_dir($sourceCarImages)) {
        $carImages = glob($sourceCarImages . '/*');
        foreach ($carImages as $image) {
            $filename = basename($image);
            $target = $targetCarImages . '/' . $filename;
            
            if (!file_exists($target)) {
                copy($image, $target);
                echo "✅ Copied car image: {$filename}\n";
            } else {
                echo "✓ Car image exists: {$filename}\n";
            }
        }
    }
    
    // Copy driver images
    $sourceDriverImages = storage_path('app/public/driver-profiles');
    $targetDriverImages = public_path('driver-profiles');
    
    if (is_dir($sourceDriverImages)) {
        $driverImages = glob($sourceDriverImages . '/*');
        foreach ($driverImages as $image) {
            $filename = basename($image);
            $target = $targetDriverImages . '/' . $filename;
            
            if (!file_exists($target)) {
                copy($image, $target);
                echo "✅ Copied driver image: {$filename}\n";
            } else {
                echo "✓ Driver image exists: {$filename}\n";
            }
        }
    }
    
    echo "\n🔧 STEP 3: SETTING PERMISSIONS...\n";
    chmod(public_path('car-images'), 0755);
    chmod(public_path('driver-profiles'), 0755);
    chmod(public_path('uploads'), 0755);
    
    // Set file permissions
    $allImages = array_merge(
        glob(public_path('car-images') . '/*'),
        glob(public_path('driver-profiles') . '/*')
    );
    
    foreach ($allImages as $image) {
        chmod($image, 0644);
    }
    
    echo "✅ Permissions set\n";
    
    echo "\n🔧 STEP 4: UPDATING DATABASE URLS...\n";
    
    // Update car images
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        $newImages = [];
        
        foreach ($images as $image) {
            $filename = basename($image);
            $newImages[] = 'https://ebisera.com/api/public/car-images/' . $filename;
        }
        
        DB::table('cars')
            ->where('id', $car->id)
            ->update(['images' => json_encode($newImages)]);
        
        echo "✅ Updated car ID {$car->id}\n";
    }
    
    // Update driver images
    $drivers = DB::table('drivers')->whereNotNull('profile_image')->where('profile_image', '!=', '')->get();
    foreach ($drivers as $driver) {
        $filename = basename($driver->profile_image);
        $newUrl = 'https://ebisera.com/api/public/driver-profiles/' . $filename;
        
        DB::table('drivers')
            ->where('id', $driver->id)
            ->update(['profile_image' => $newUrl]);
        
        echo "✅ Updated driver ID {$driver->id}\n";
    }
    
    echo "\n🔧 STEP 5: CLEARING CACHES...\n";
    $kernel->call('config:clear');
    $kernel->call('cache:clear');
    $kernel->call('config:cache');
    echo "✅ Caches cleared and updated\n";
    
    echo "\n🎉 SETUP COMPLETE!\n\n";
    echo "📋 NEW IMAGE PATHS:\n";
    echo "Car images: https://ebisera.com/api/public/car-images/\n";
    echo "Driver images: https://ebisera.com/api/public/driver-profiles/\n";
    echo "New uploads will automatically go to these directories!\n\n";
    
    echo "📊 SUMMARY:\n";
    echo "Car images: " . count(glob(public_path('car-images') . '/*')) . " files\n";
    echo "Driver images: " . count(glob(public_path('driver-profiles') . '/*')) . " files\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
