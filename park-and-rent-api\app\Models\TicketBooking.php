<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketBooking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'ticket_type',
        'passenger_name',
        'passenger_email',
        'passenger_phone',
        'departure_country',
        'departure_city',
        'destination_country',
        'destination_city',
        'departure_date',
        'return_date',
        'trip_type',
        'passengers_count',
        'class_type',
        'special_requests',
        'estimated_price',
        'status',
        'admin_notes',
        'contacted_at',
        'confirmed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'departure_date' => 'date',
        'return_date' => 'date',
        'passengers_count' => 'integer',
        'estimated_price' => 'decimal:2',
        'contacted_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    /**
     * Get the user that made the ticket booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the East African countries list.
     */
    public static function getEastAfricanCountries(): array
    {
        return [
            'Uganda' => 'Uganda',
            'Kenya' => 'Kenya',
            'Tanzania' => 'Tanzania',
            'Burundi' => 'Burundi',
            'South Sudan' => 'South Sudan',
            'Ethiopia' => 'Ethiopia',
            'Somalia' => 'Somalia',
            'Djibouti' => 'Djibouti',
        ];
    }

    /**
     * Get major cities for East African countries.
     */
    public static function getMajorCities(): array
    {
        return [
            'Uganda' => ['Kampala', 'Entebbe', 'Jinja', 'Mbarara', 'Gulu'],
            'Kenya' => ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret'],
            'Tanzania' => ['Dar es Salaam', 'Dodoma', 'Arusha', 'Mwanza', 'Zanzibar'],
            'Burundi' => ['Bujumbura', 'Gitega', 'Muyinga', 'Ngozi', 'Ruyigi'],
            'South Sudan' => ['Juba', 'Wau', 'Malakal', 'Bentiu', 'Bor'],
            'Ethiopia' => ['Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Hawassa'],
            'Somalia' => ['Mogadishu', 'Hargeisa', 'Bosaso', 'Kismayo', 'Galkayo'],
            'Djibouti' => ['Djibouti City', 'Ali Sabieh', 'Dikhil', 'Tadjourah', 'Obock'],
        ];
    }
}
