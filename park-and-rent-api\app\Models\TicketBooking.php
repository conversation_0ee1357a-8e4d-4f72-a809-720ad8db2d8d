<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketBooking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'ticket_type',
        'passenger_name',
        'passenger_email',
        'passenger_phone',
        'departure_country',
        'departure_city',
        'destination_country',
        'destination_city',
        'departure_date',
        'return_date',
        'trip_type',
        'passengers_count',
        'class_type',
        'special_requests',
        'estimated_price',
        'status',
        'admin_notes',
        'contacted_at',
        'confirmed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'departure_date' => 'date',
        'return_date' => 'date',
        'passengers_count' => 'integer',
        'estimated_price' => 'decimal:2',
        'contacted_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    /**
     * Get the user that made the ticket booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the East African countries list (for bus travel).
     */
    public static function getEastAfricanCountries(): array
    {
        return [
            'Uganda' => 'Uganda',
            'Kenya' => 'Kenya',
            'Tanzania' => 'Tanzania',
            'Burundi' => 'Burundi',
            'South Sudan' => 'South Sudan',
            'Ethiopia' => 'Ethiopia',
            'Somalia' => 'Somalia',
            'Djibouti' => 'Djibouti',
        ];
    }

    /**
     * Get worldwide countries list (for flight travel).
     */
    public static function getWorldwideCountries(): array
    {
        return [
            // East Africa
            'Uganda' => 'Uganda',
            'Kenya' => 'Kenya',
            'Tanzania' => 'Tanzania',
            'Burundi' => 'Burundi',
            'South Sudan' => 'South Sudan',
            'Ethiopia' => 'Ethiopia',
            'Somalia' => 'Somalia',
            'Djibouti' => 'Djibouti',

            // Popular African Destinations
            'South Africa' => 'South Africa',
            'Nigeria' => 'Nigeria',
            'Ghana' => 'Ghana',
            'Morocco' => 'Morocco',
            'Egypt' => 'Egypt',
            'Senegal' => 'Senegal',
            'Ivory Coast' => 'Ivory Coast',
            'Cameroon' => 'Cameroon',
            'Zimbabwe' => 'Zimbabwe',
            'Zambia' => 'Zambia',
            'Botswana' => 'Botswana',
            'Namibia' => 'Namibia',

            // Europe
            'United Kingdom' => 'United Kingdom',
            'Germany' => 'Germany',
            'France' => 'France',
            'Netherlands' => 'Netherlands',
            'Belgium' => 'Belgium',
            'Switzerland' => 'Switzerland',
            'Italy' => 'Italy',
            'Spain' => 'Spain',
            'Sweden' => 'Sweden',
            'Norway' => 'Norway',
            'Denmark' => 'Denmark',

            // North America
            'United States' => 'United States',
            'Canada' => 'Canada',

            // Asia
            'China' => 'China',
            'India' => 'India',
            'Japan' => 'Japan',
            'South Korea' => 'South Korea',
            'Singapore' => 'Singapore',
            'Malaysia' => 'Malaysia',
            'Thailand' => 'Thailand',
            'Vietnam' => 'Vietnam',
            'Philippines' => 'Philippines',
            'Indonesia' => 'Indonesia',
            'United Arab Emirates' => 'United Arab Emirates',
            'Qatar' => 'Qatar',
            'Saudi Arabia' => 'Saudi Arabia',

            // Oceania
            'Australia' => 'Australia',
            'New Zealand' => 'New Zealand',
        ];
    }

    /**
     * Get major cities for East African countries (for bus travel).
     */
    public static function getEastAfricanCities(): array
    {
        return [
            'Uganda' => ['Kampala', 'Entebbe', 'Jinja', 'Mbarara', 'Gulu', 'Fort Portal', 'Masaka', 'Mbale'],
            'Kenya' => ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi', 'Kitale'],
            'Tanzania' => ['Dar es Salaam', 'Dodoma', 'Arusha', 'Mwanza', 'Zanzibar', 'Mbeya', 'Morogoro', 'Tanga'],
            'Burundi' => ['Bujumbura', 'Gitega', 'Muyinga', 'Ngozi', 'Ruyigi', 'Kayanza', 'Bururi', 'Makamba'],
            'South Sudan' => ['Juba', 'Wau', 'Malakal', 'Bentiu', 'Bor', 'Yei', 'Torit', 'Aweil'],
            'Ethiopia' => ['Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Hawassa', 'Bahir Dar', 'Dessie', 'Jimma'],
            'Somalia' => ['Mogadishu', 'Hargeisa', 'Bosaso', 'Kismayo', 'Galkayo', 'Berbera', 'Merca', 'Baidoa'],
            'Djibouti' => ['Djibouti City', 'Ali Sabieh', 'Dikhil', 'Tadjourah', 'Obock', 'Arta']
        ];
    }

    /**
     * Get major cities for worldwide countries (for flight travel).
     */
    public static function getWorldwideCities(): array
    {
        return [
            // East Africa
            'Uganda' => ['Kampala', 'Entebbe'],
            'Kenya' => ['Nairobi', 'Mombasa'],
            'Tanzania' => ['Dar es Salaam', 'Kilimanjaro', 'Zanzibar'],
            'Burundi' => ['Bujumbura'],
            'South Sudan' => ['Juba'],
            'Ethiopia' => ['Addis Ababa'],
            'Somalia' => ['Mogadishu'],
            'Djibouti' => ['Djibouti City'],

            // Popular African Destinations
            'South Africa' => ['Johannesburg', 'Cape Town', 'Durban', 'Pretoria'],
            'Nigeria' => ['Lagos', 'Abuja', 'Kano', 'Port Harcourt'],
            'Ghana' => ['Accra', 'Kumasi'],
            'Morocco' => ['Casablanca', 'Rabat', 'Marrakech', 'Fez'],
            'Egypt' => ['Cairo', 'Alexandria', 'Luxor', 'Aswan'],
            'Senegal' => ['Dakar'],
            'Ivory Coast' => ['Abidjan', 'Yamoussoukro'],
            'Cameroon' => ['Douala', 'Yaoundé'],
            'Zimbabwe' => ['Harare', 'Bulawayo'],
            'Zambia' => ['Lusaka', 'Ndola'],
            'Botswana' => ['Gaborone'],
            'Namibia' => ['Windhoek'],

            // Europe
            'United Kingdom' => ['London', 'Manchester', 'Birmingham', 'Edinburgh', 'Glasgow'],
            'Germany' => ['Berlin', 'Munich', 'Frankfurt', 'Hamburg', 'Cologne'],
            'France' => ['Paris', 'Lyon', 'Marseille', 'Nice', 'Toulouse'],
            'Netherlands' => ['Amsterdam', 'Rotterdam', 'The Hague'],
            'Belgium' => ['Brussels', 'Antwerp'],
            'Switzerland' => ['Zurich', 'Geneva', 'Basel'],
            'Italy' => ['Rome', 'Milan', 'Venice', 'Florence', 'Naples'],
            'Spain' => ['Madrid', 'Barcelona', 'Valencia', 'Seville'],
            'Sweden' => ['Stockholm', 'Gothenburg', 'Malmö'],
            'Norway' => ['Oslo', 'Bergen'],
            'Denmark' => ['Copenhagen'],

            // North America
            'United States' => ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Miami', 'San Francisco', 'Washington DC', 'Boston'],
            'Canada' => ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],

            // Asia
            'China' => ['Beijing', 'Shanghai', 'Guangzhou', 'Shenzhen'],
            'India' => ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata'],
            'Japan' => ['Tokyo', 'Osaka', 'Kyoto', 'Nagoya'],
            'South Korea' => ['Seoul', 'Busan'],
            'Singapore' => ['Singapore'],
            'Malaysia' => ['Kuala Lumpur', 'Penang'],
            'Thailand' => ['Bangkok', 'Phuket', 'Chiang Mai'],
            'Vietnam' => ['Ho Chi Minh City', 'Hanoi'],
            'Philippines' => ['Manila', 'Cebu'],
            'Indonesia' => ['Jakarta', 'Bali'],
            'United Arab Emirates' => ['Dubai', 'Abu Dhabi'],
            'Qatar' => ['Doha'],
            'Saudi Arabia' => ['Riyadh', 'Jeddah'],

            // Oceania
            'Australia' => ['Sydney', 'Melbourne', 'Brisbane', 'Perth'],
            'New Zealand' => ['Auckland', 'Wellington', 'Christchurch']
        ];
    }

    /**
     * Get major cities (legacy method for backward compatibility).
     */
    public static function getMajorCities(): array
    {
        return [
            'Uganda' => ['Kampala', 'Entebbe', 'Jinja', 'Mbarara', 'Gulu'],
            'Kenya' => ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret'],
            'Tanzania' => ['Dar es Salaam', 'Dodoma', 'Arusha', 'Mwanza', 'Zanzibar'],
            'Burundi' => ['Bujumbura', 'Gitega', 'Muyinga', 'Ngozi', 'Ruyigi'],
            'South Sudan' => ['Juba', 'Wau', 'Malakal', 'Bentiu', 'Bor'],
            'Ethiopia' => ['Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Hawassa'],
            'Somalia' => ['Mogadishu', 'Hargeisa', 'Bosaso', 'Kismayo', 'Galkayo'],
            'Djibouti' => ['Djibouti City', 'Ali Sabieh', 'Dikhil', 'Tadjourah', 'Obock'],
        ];
    }
}
