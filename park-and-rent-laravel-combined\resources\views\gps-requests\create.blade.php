@extends('layouts.app')

@section('title', 'Request GPS Installation')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center mb-4">
                <a href="{{ route('gps-requests.index') }}" class="text-primary-600 hover:text-primary-700 mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-gray-900">Request GPS Installation</h1>
            </div>
            <p class="text-gray-600">Schedule GPS installation for your car to enable tracking and enhanced security</p>
        </div>

        @if($cars->count() === 0)
            <!-- No Cars Available -->
            <div class="bg-white shadow rounded-lg p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No cars available for GPS installation</h3>
                <p class="mt-1 text-sm text-gray-500">All your cars either already have GPS installed or you don't have any cars listed yet.</p>
                <div class="mt-6">
                    <a href="{{ route('owner.cars.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Add a Car
                    </a>
                </div>
            </div>
        @else
            <!-- GPS Installation Request Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">GPS Installation Details</h2>
                    <p class="text-sm text-gray-600">Please provide the following information to schedule your GPS installation</p>
                </div>

                <form action="{{ route('gps-requests.store') }}" method="POST" class="p-6 space-y-6">
                    @csrf

                    <!-- Car Selection -->
                    <div>
                        <label for="car_id" class="block text-sm font-medium text-gray-700">Select Car *</label>
                        <select name="car_id" id="car_id" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Choose a car...</option>
                            @foreach($cars as $car)
                                <option value="{{ $car->id }}" {{ old('car_id') == $car->id ? 'selected' : '' }}>
                                    {{ $car->make }} {{ $car->model }} ({{ $car->year }}) - {{ $car->license_plate }}
                                </option>
                            @endforeach
                        </select>
                        @error('car_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Preferred Date and Time -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="preferred_date" class="block text-sm font-medium text-gray-700">Preferred Date *</label>
                            <input type="date" name="preferred_date" id="preferred_date" value="{{ old('preferred_date') }}" required
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            @error('preferred_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="preferred_time" class="block text-sm font-medium text-gray-700">Preferred Time *</label>
                            <select name="preferred_time" id="preferred_time" required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                                <option value="">Select time...</option>
                                <option value="08:00-10:00" {{ old('preferred_time') == '08:00-10:00' ? 'selected' : '' }}>8:00 AM - 10:00 AM</option>
                                <option value="10:00-12:00" {{ old('preferred_time') == '10:00-12:00' ? 'selected' : '' }}>10:00 AM - 12:00 PM</option>
                                <option value="12:00-14:00" {{ old('preferred_time') == '12:00-14:00' ? 'selected' : '' }}>12:00 PM - 2:00 PM</option>
                                <option value="14:00-16:00" {{ old('preferred_time') == '14:00-16:00' ? 'selected' : '' }}>2:00 PM - 4:00 PM</option>
                                <option value="16:00-18:00" {{ old('preferred_time') == '16:00-18:00' ? 'selected' : '' }}>4:00 PM - 6:00 PM</option>
                            </select>
                            @error('preferred_time')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-700">Contact Phone *</label>
                        <input type="tel" name="contact_phone" id="contact_phone" value="{{ old('contact_phone', '0788613669') }}" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                               placeholder="0788613669">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Installation Address -->
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700">Installation Address *</label>
                        <textarea name="address" id="address" rows="3" required
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Please provide the full address where the GPS installation should take place...">{{ old('address') }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">Include street address, city, and any specific location details</p>
                        @error('address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Special Instructions -->
                    <div>
                        <label for="special_instructions" class="block text-sm font-medium text-gray-700">Special Instructions</label>
                        <textarea name="special_instructions" id="special_instructions" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Any special requirements, access instructions, or preferences...">{{ old('special_instructions') }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">Optional: Parking instructions, building access codes, preferred installation location, etc.</p>
                        @error('special_instructions')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Information Box -->
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">
                                    GPS Installation Information
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Installation typically takes 1-2 hours</li>
                                        <li>Professional technician will contact you 24 hours before installation</li>
                                        <li>GPS device includes real-time tracking and anti-theft features</li>
                                        <li>Installation is free for Park & Rent car owners</li>
                                        <li>You can reschedule or cancel up to 24 hours before the appointment</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                        <a href="{{ route('gps-requests.index') }}" 
                           class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium">
                            Submit Request
                        </button>
                    </div>
                </form>
            </div>
        @endif
    </div>
</div>
@endsection
