<?php
// Simple test file to verify API is working
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

echo json_encode([
    'status' => 'ok',
    'message' => 'Laravel API is accessible',
    'timestamp' => date('Y-m-d H:i:s'),
    'path' => __DIR__,
    'url_tested' => $_SERVER['REQUEST_URI'] ?? 'unknown'
]);
?>
