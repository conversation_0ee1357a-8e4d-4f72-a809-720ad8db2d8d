<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingCancelled extends Notification implements ShouldQueue
{
    use Queueable;

    public $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $itemType = $this->booking->item_type === 'car' ? 'Car Rental' : 'Driver Service';
        $startDate = $this->booking->start_time->format('M j, Y \a\t g:i A');

        return (new MailMessage)
            ->subject('Booking Cancelled - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your booking has been cancelled.')
            ->line('**Cancelled Booking Details:**')
            ->line('Type: ' . $itemType)
            ->line('Booking ID: #' . $this->booking->id)
            ->line('Original Start Time: ' . $startDate)
            ->line('Total Amount: RWF ' . number_format($this->booking->total_price))
            ->action('View Booking Details', url('/bookings/' . $this->booking->id))
            ->line('If you have any questions about this cancellation, please contact <NAME_EMAIL> or 0788613669.')
            ->line('Thank you for using Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_cancelled',
            'title' => 'Booking Cancelled',
            'message' => 'Your ' . ($this->booking->item_type === 'car' ? 'car rental' : 'driver service') . ' booking has been cancelled.',
            'action_url' => '/bookings/' . $this->booking->id,
        ];
    }
}
