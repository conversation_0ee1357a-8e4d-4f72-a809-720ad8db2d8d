<?php

namespace App\Http\Controllers;

use App\Models\GpsInstallationRequest;
use App\Models\Car;
use Illuminate\Http\Request;

class GpsInstallationRequestController extends Controller
{
    /**
     * Display a listing of GPS installation requests.
     */
    public function index()
    {
        $user = auth()->user();
        
        if ($user->role === 'admin') {
            // Ad<PERSON> can see all requests
            $requests = GpsInstallationRequest::with(['car.owner'])
                ->latest()
                ->paginate(15);
        } else {
            // Owners can only see their own requests
            $requests = GpsInstallationRequest::whereHas('car', function($query) use ($user) {
                $query->where('owner_id', $user->id);
            })
            ->with(['car'])
            ->latest()
            ->paginate(15);
        }

        return view('gps-requests.index', compact('requests'));
    }

    /**
     * Show the form for creating a new GPS installation request.
     */
    public function create()
    {
        $user = auth()->user();
        
        // Get user's cars that don't have GPS installed
        $cars = Car::where('owner_id', $user->id)
            ->where('has_gps', false)
            ->get();

        return view('gps-requests.create', compact('cars'));
    }

    /**
     * Store a newly created GPS installation request.
     */
    public function store(Request $request)
    {
        $request->validate([
            'car_id' => 'required|exists:cars,id',
            'preferred_date' => 'required|date|after:today',
            'preferred_time' => 'required|string',
            'contact_phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'special_instructions' => 'nullable|string|max:1000',
        ]);

        $car = Car::findOrFail($request->car_id);
        
        // Check if user owns the car
        if ($car->owner_id !== auth()->id()) {
            abort(403, 'You can only request GPS installation for your own cars.');
        }

        // Check if car already has GPS
        if ($car->has_gps) {
            return redirect()->back()->with('error', 'This car already has GPS installed.');
        }

        // Check if there's already a pending request for this car
        $existingRequest = GpsInstallationRequest::where('car_id', $car->id)
            ->whereIn('status', ['pending', 'scheduled'])
            ->first();

        if ($existingRequest) {
            return redirect()->back()->with('error', 'There is already a pending GPS installation request for this car.');
        }

        GpsInstallationRequest::create([
            'car_id' => $request->car_id,
            'preferred_date' => $request->preferred_date,
            'preferred_time' => $request->preferred_time,
            'contact_phone' => $request->contact_phone,
            'address' => $request->address,
            'special_instructions' => $request->special_instructions,
            'status' => 'pending',
        ]);

        return redirect()->route('gps-requests.index')
            ->with('success', 'GPS installation request submitted successfully! We will contact you within 24 hours.');
    }

    /**
     * Display the specified GPS installation request.
     */
    public function show(GpsInstallationRequest $gpsRequest)
    {
        $user = auth()->user();
        
        // Check authorization
        if ($user->role !== 'admin' && $gpsRequest->car->owner_id !== $user->id) {
            abort(403);
        }

        $gpsRequest->load(['car.owner']);
        
        return view('gps-requests.show', compact('gpsRequest'));
    }

    /**
     * Show the form for editing the specified GPS installation request.
     */
    public function edit(GpsInstallationRequest $gpsRequest)
    {
        $user = auth()->user();
        
        // Only allow editing if request is pending and user owns the car
        if ($gpsRequest->status !== 'pending' || $gpsRequest->car->owner_id !== $user->id) {
            abort(403);
        }

        return view('gps-requests.edit', compact('gpsRequest'));
    }

    /**
     * Update the specified GPS installation request.
     */
    public function update(Request $request, GpsInstallationRequest $gpsRequest)
    {
        $user = auth()->user();
        
        // Only allow updating if request is pending and user owns the car
        if ($gpsRequest->status !== 'pending' || $gpsRequest->car->owner_id !== $user->id) {
            abort(403);
        }

        $request->validate([
            'preferred_date' => 'required|date|after:today',
            'preferred_time' => 'required|string',
            'contact_phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'special_instructions' => 'nullable|string|max:1000',
        ]);

        $gpsRequest->update($request->only([
            'preferred_date',
            'preferred_time',
            'contact_phone',
            'address',
            'special_instructions'
        ]));

        return redirect()->route('gps-requests.index')
            ->with('success', 'GPS installation request updated successfully!');
    }

    /**
     * Remove the specified GPS installation request.
     */
    public function destroy(GpsInstallationRequest $gpsRequest)
    {
        $user = auth()->user();
        
        // Only allow deletion if request is pending and user owns the car
        if ($gpsRequest->status !== 'pending' || $gpsRequest->car->owner_id !== $user->id) {
            abort(403);
        }

        $gpsRequest->delete();

        return redirect()->route('gps-requests.index')
            ->with('success', 'GPS installation request cancelled successfully!');
    }

    /**
     * Admin: Update request status
     */
    public function updateStatus(Request $request, GpsInstallationRequest $gpsRequest)
    {
        // Only admins can update status
        if (auth()->user()->role !== 'admin') {
            abort(403);
        }

        $request->validate([
            'status' => 'required|in:pending,scheduled,in_progress,completed,cancelled',
            'scheduled_date' => 'nullable|date',
            'scheduled_time' => 'nullable|string',
            'technician_notes' => 'nullable|string|max:1000',
        ]);

        $updateData = [
            'status' => $request->status,
            'technician_notes' => $request->technician_notes,
        ];

        if ($request->status === 'scheduled') {
            $updateData['scheduled_date'] = $request->scheduled_date;
            $updateData['scheduled_time'] = $request->scheduled_time;
        }

        if ($request->status === 'completed') {
            $updateData['completed_at'] = now();
            
            // Mark car as having GPS
            $gpsRequest->car->update(['has_gps' => true]);
        }

        $gpsRequest->update($updateData);

        return redirect()->back()
            ->with('success', 'GPS installation request status updated successfully!');
    }
}
