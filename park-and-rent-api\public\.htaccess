# Park & Rent API - Production .htaccess Configuration
# Comprehensive configuration for Laravel API deployment on Hostinger

# ============================================================================
# LARAVEL FRAMEWORK ROUTING
# ============================================================================

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On
    RewriteBase /

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# ============================================================================
# CORS HEADERS FOR API ACCESS
# ============================================================================

<IfModule mod_headers.c>
    # Allow cross-origin requests from your frontend domain
    Header always set Access-Control-Allow-Origin "https://ebisera.com"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-TOKEN, X-Socket-ID"
    Header always set Access-Control-Allow-Credentials "true"
    Header always set Access-Control-Max-Age "86400"

    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# ============================================================================
# SECURITY HEADERS
# ============================================================================

<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# ============================================================================
# DIRECTORY BROWSING PROTECTION
# ============================================================================

# Disable directory browsing
Options -Indexes

# Prevent access to sensitive files and directories
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# Protect Laravel configuration files
<FilesMatch "\.(env|log|md|json|lock|yml|yaml|xml)$">
    Require all denied
</FilesMatch>

# Protect Composer files
<FilesMatch "(composer\.(json|lock)|package\.(json|lock)|yarn\.lock)$">
    Require all denied
</FilesMatch>

# ============================================================================
# FILE UPLOAD AND STATIC ASSETS
# ============================================================================

# Set proper MIME types for uploaded files
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType application/pdf .pdf
</IfModule>

# Enable compression for text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On

    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"

    # API responses (no cache)
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# ============================================================================
# ADDITIONAL SECURITY MEASURES
# ============================================================================

# Prevent execution of PHP files in uploads directory
<Directory "car-images">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>

<Directory "driver-profiles">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>
