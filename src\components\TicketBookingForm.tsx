import React, { useState, useEffect } from 'react';
import { Plane, Bus, Calendar, MapPin, Users, CreditCard, Clock } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { ErrorDisplay, SuccessDisplay } from './ErrorDisplay';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface TicketBookingFormProps {
  onSuccess?: () => void;
}

interface Destinations {
  east_african_countries: Record<string, string>;
  east_african_cities: Record<string, string[]>;
  worldwide_countries: Record<string, string>;
  worldwide_cities: Record<string, string[]>;
}

const TicketBookingForm: React.FC<TicketBookingFormProps> = ({ onSuccess }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [destinations, setDestinations] = useState<Destinations>({
    east_african_countries: {},
    east_african_cities: {},
    worldwide_countries: {},
    worldwide_cities: {}
  });

  const [formData, setFormData] = useState({
    ticket_type: 'flight',
    passenger_name: user?.name || '',
    passenger_email: user?.email || '',
    passenger_phone: '',
    departure_city: '',
    destination_country: '',
    destination_city: '',
    departure_date: '',
    return_date: '',
    trip_type: 'one_way',
    passengers_count: 1,
    class_type: 'economy',
    special_requests: ''
  });

  // Fetch destinations on component mount
  useEffect(() => {
    fetchDestinations();
  }, []);

  const fetchDestinations = async () => {
    try {
      const response = await apiClient.get(API_ENDPOINTS.TICKET_DESTINATIONS);
      if (response.data.success) {
        setDestinations(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch destinations:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear destination city when country changes
    if (name === 'destination_country') {
      setFormData(prev => ({
        ...prev,
        destination_city: ''
      }));
    }

    // Clear return date when switching to one way
    if (name === 'trip_type' && value === 'one_way') {
      setFormData(prev => ({
        ...prev,
        return_date: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await apiClient.post(API_ENDPOINTS.TICKET_BOOKINGS, formData);

      if (response.data.success) {
        setSuccess(response.data.message);
        // Reset form
        setFormData({
          ticket_type: 'flight',
          passenger_name: user?.name || '',
          passenger_email: user?.email || '',
          passenger_phone: '',
          departure_city: '',
          destination_country: '',
          destination_city: '',
          departure_date: '',
          return_date: '',
          trip_type: 'one_way',
          passengers_count: 1,
          class_type: 'economy',
          special_requests: ''
        });
        onSuccess?.();
      } else {
        setError(response.data.message || 'Failed to submit ticket booking');
      }
    } catch (error: any) {
      console.error('Ticket booking error:', error);
      if (error.response?.data?.message) {
        setError(error.response.data.message);
      } else if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Get available countries and cities based on ticket type
  const availableCountries = formData.ticket_type === 'bus'
    ? destinations.east_african_countries
    : destinations.worldwide_countries;

  const availableCitiesData = formData.ticket_type === 'bus'
    ? destinations.east_african_cities
    : destinations.worldwide_cities;

  const availableCities = formData.destination_country
    ? availableCitiesData[formData.destination_country] || []
    : [];

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Book Your Ticket</h2>
        <p className="text-gray-600">Travel to East African countries with ease</p>
      </div>

      <ErrorDisplay error={error} />
      <SuccessDisplay message={success} />

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Ticket Type Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ticket Type
            </label>
            <div className="grid grid-cols-2 gap-2">
              <label className={`flex items-center justify-center p-3 border rounded-lg cursor-pointer ${
                formData.ticket_type === 'flight' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
              }`}>
                <input
                  type="radio"
                  name="ticket_type"
                  value="flight"
                  checked={formData.ticket_type === 'flight'}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <Plane className="h-5 w-5 mr-2" />
                Flight (Worldwide)
              </label>
              <label className={`flex items-center justify-center p-3 border rounded-lg cursor-pointer ${
                formData.ticket_type === 'bus' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
              }`}>
                <input
                  type="radio"
                  name="ticket_type"
                  value="bus"
                  checked={formData.ticket_type === 'bus'}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <Bus className="h-5 w-5 mr-2" />
                Bus (East Africa)
              </label>
            </div>
            {/* Destination Notice */}
            <div className="mt-3 p-3 bg-primary-50 border border-primary-200 rounded-lg">
              <p className="text-sm text-primary-700">
                {formData.ticket_type === 'flight' ? (
                  <span>✈️ <strong>Flight tickets:</strong> Available to destinations worldwide</span>
                ) : (
                  <span>🚌 <strong>Bus tickets:</strong> Available only to East African countries</span>
                )}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trip Type
            </label>
            <select
              name="trip_type"
              value={formData.trip_type}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            >
              <option value="one_way">One Way</option>
              <option value="round_trip">Round Trip</option>
            </select>
          </div>
        </div>

        {/* Passenger Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Passenger Name
            </label>
            <input
              type="text"
              name="passenger_name"
              value={formData.passenger_name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              name="passenger_email"
              value={formData.passenger_email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              name="passenger_phone"
              value={formData.passenger_phone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
        </div>

        {/* Route Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="inline h-4 w-4 mr-1" />
              From (City)
            </label>
            <input
              type="text"
              name="departure_city"
              value={formData.departure_city}
              onChange={handleInputChange}
              placeholder="e.g., Kigali"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To (Country)
            </label>
            <select
              name="destination_country"
              value={formData.destination_country}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            >
              <option value="">Select Country</option>
              {Object.entries(availableCountries).map(([key, value]) => (
                <option key={key} value={key}>{value}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To (City)
            </label>
            <select
              name="destination_city"
              value={formData.destination_city}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
              disabled={!formData.destination_country}
            >
              <option value="">Select City</option>
              {availableCities.map((city) => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Date Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Departure Date
            </label>
            <input
              type="date"
              name="departure_date"
              value={formData.departure_date}
              onChange={handleInputChange}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
          {formData.trip_type === 'round_trip' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Return Date
              </label>
              <input
                type="date"
                name="return_date"
                value={formData.return_date}
                onChange={handleInputChange}
                min={formData.departure_date || new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required={formData.trip_type === 'round_trip'}
              />
            </div>
          )}
        </div>

        {/* Additional Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Users className="inline h-4 w-4 mr-1" />
              Number of Passengers
            </label>
            <select
              name="passengers_count"
              value={formData.passengers_count}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                <option key={num} value={num}>{num} {num === 1 ? 'Passenger' : 'Passengers'}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CreditCard className="inline h-4 w-4 mr-1" />
              Class
            </label>
            <select
              name="class_type"
              value={formData.class_type}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            >
              <option value="economy">Economy</option>
              <option value="business">Business</option>
              <option value="first">First Class</option>
            </select>
          </div>
        </div>

        {/* Special Requests */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Special Requests (Optional)
          </label>
          <textarea
            name="special_requests"
            value={formData.special_requests}
            onChange={handleInputChange}
            rows={3}
            placeholder="Any special requirements, dietary needs, accessibility requests, etc."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-3 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <Clock className="animate-spin h-4 w-4 mr-2" />
                Submitting...
              </>
            ) : (
              <>
                <Plane className="h-4 w-4 mr-2" />
                Submit Booking Request
              </>
            )}
          </button>
        </div>
      </form>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">What happens next?</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Our team will review your booking request</li>
          <li>• We'll contact you within 24 hours with pricing and availability</li>
          <li>• Payment and travel details will be provided separately</li>
          <li>• You'll receive email updates on your booking status</li>
        </ul>
      </div>
    </div>
  );
};

export default TicketBookingForm;
