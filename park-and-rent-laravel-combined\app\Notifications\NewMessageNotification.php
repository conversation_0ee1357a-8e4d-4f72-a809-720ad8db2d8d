<?php

namespace App\Notifications;

use App\Models\Message;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewMessageNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $message;

    /**
     * Create a new notification instance.
     */
    public function __construct(Message $message)
    {
        $this->message = $message;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $senderName = $this->message->sender->name;
        $messagePreview = strlen($this->message->content) > 100 
            ? substr($this->message->content, 0, 100) . '...'
            : $this->message->content;

        $contextInfo = '';
        if ($this->message->chat->relatedTo) {
            if ($this->message->chat->related_to_type === 'App\\Models\\Car') {
                $contextInfo = 'About: ' . $this->message->chat->relatedTo->make . ' ' . $this->message->chat->relatedTo->model;
            } elseif ($this->message->chat->related_to_type === 'App\\Models\\Driver') {
                $contextInfo = 'About: Driver Service';
            }
        }

        $mailMessage = (new MailMessage)
            ->subject('New Message from ' . $senderName . ' - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have received a new message from ' . $senderName . '.');

        if ($contextInfo) {
            $mailMessage->line($contextInfo);
        }

        $mailMessage->line('**Message Preview:**')
                   ->line('"' . $messagePreview . '"')
                   ->action('View Full Conversation', route('chat.show', $this->message->chat))
                   ->line('You can reply directly from the Park & Rent platform.')
                   ->line('Thank you for using Park & Rent!');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message_id' => $this->message->id,
            'chat_id' => $this->message->chat_id,
            'sender_name' => $this->message->sender->name,
            'type' => 'new_message',
            'message' => 'You have a new message'
        ];
    }
}
