@echo off
echo ========================================
echo   Park & Rent API Deployment Script
echo ========================================
echo.

REM Check if park-and-rent-api directory exists
if not exist "park-and-rent-api" (
    echo ERROR: park-and-rent-api directory not found!
    echo Please make sure you have the Laravel API project in the park-and-rent-api folder.
    pause
    exit /b 1
)

echo Step 1: Creating deployment directory structure...
if not exist "deployment\api-upload" mkdir "deployment\api-upload"
if not exist "deployment\api-upload\api" mkdir "deployment\api-upload\api"

echo Step 2: Copying Laravel API files...

REM Copy Laravel application files (excluding public folder)
echo Copying Laravel core files...
xcopy "park-and-rent-api\app" "deployment\api-upload\app\" /E /I /Y
xcopy "park-and-rent-api\bootstrap" "deployment\api-upload\bootstrap\" /E /I /Y
xcopy "park-and-rent-api\config" "deployment\api-upload\config\" /E /I /Y
xcopy "park-and-rent-api\database" "deployment\api-upload\database\" /E /I /Y
xcopy "park-and-rent-api\resources" "deployment\api-upload\resources\" /E /I /Y
xcopy "park-and-rent-api\routes" "deployment\api-upload\routes\" /E /I /Y
xcopy "park-and-rent-api\storage" "deployment\api-upload\storage\" /E /I /Y

REM Copy vendor if exists (or create composer install instruction)
if exist "park-and-rent-api\vendor" (
    echo Copying vendor directory...
    xcopy "park-and-rent-api\vendor" "deployment\api-upload\vendor\" /E /I /Y
) else (
    echo WARNING: vendor directory not found. You'll need to run 'composer install' on the server.
)

REM Copy Laravel public folder contents to api folder
echo Copying public files to api folder...
xcopy "park-and-rent-api\public\*" "deployment\api-upload\api\" /E /Y

REM Copy our custom index.php and .htaccess
echo Copying deployment-specific files...
copy "deployment\api\index.php" "deployment\api-upload\api\index.php" /Y
copy "deployment\api\.htaccess" "deployment\api-upload\api\.htaccess" /Y

REM Copy other Laravel files
echo Copying Laravel configuration files...
copy "park-and-rent-api\artisan" "deployment\api-upload\artisan" /Y
copy "park-and-rent-api\composer.json" "deployment\api-upload\composer.json" /Y
copy "park-and-rent-api\composer.lock" "deployment\api-upload\composer.lock" /Y

REM Copy production environment file
echo Copying production environment file...
copy "deployment\.env.production" "deployment\api-upload\.env" /Y

echo.
echo Step 3: Creating upload instructions...

REM Create upload instructions
echo Creating UPLOAD_INSTRUCTIONS.txt...
(
echo ========================================
echo   UPLOAD INSTRUCTIONS FOR HOSTINGER
echo ========================================
echo.
echo 1. Upload ALL files from 'deployment\api-upload\' to your Hostinger account:
echo    - Upload the 'api' folder to public_html/api/
echo    - Upload all other folders ^(app, bootstrap, config, etc.^) to public_html/
echo.
echo 2. File structure on server should be:
echo    public_html/
echo    ├── api/                    ^(Laravel public files^)
echo    │   ├── index.php
echo    │   ├── .htaccess
echo    │   └── ^(other public files^)
echo    ├── app/                    ^(Laravel app files^)
echo    ├── bootstrap/
echo    ├── config/
echo    ├── database/
echo    ├── resources/
echo    ├── routes/
echo    ├── storage/
echo    ├── vendor/                 ^(if copied^)
echo    ├── .env
echo    ├── artisan
echo    ├── composer.json
echo    └── composer.lock
echo.
echo 3. After uploading, update the .env file with your database credentials:
echo    - DB_DATABASE=park_and_rent
echo    - DB_USERNAME=^(your database username^)
echo    - DB_PASSWORD=^(your database password^)
echo    - APP_KEY=^(generate using: php artisan key:generate^)
echo.
echo 4. Set proper permissions:
echo    - storage/ folder: 755 or 775
echo    - bootstrap/cache/ folder: 755 or 775
echo.
echo 5. If vendor folder wasn't copied, run on server:
echo    composer install --no-dev --optimize-autoloader
echo.
echo 6. Run database migrations:
echo    php artisan migrate
echo    php artisan db:seed
echo.
echo 7. Test the API:
echo    Visit: https://ebisera.com/api/health
echo    Should return: {"status":"ok","message":"Park & Rent API is running"}
echo.
echo ========================================
) > "deployment\api-upload\UPLOAD_INSTRUCTIONS.txt"

echo.
echo ========================================
echo   DEPLOYMENT COMPLETE!
echo ========================================
echo.
echo Files are ready in: deployment\api-upload\
echo.
echo Next steps:
echo 1. Read the UPLOAD_INSTRUCTIONS.txt file
echo 2. Upload files to your Hostinger account
echo 3. Configure database settings in .env
echo 4. Test the API at https://ebisera.com/api/health
echo.
pause
