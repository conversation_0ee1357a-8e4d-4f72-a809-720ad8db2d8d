<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING ALL IMAGES ===\n\n";

try {
    echo "🧪 TESTING CAR IMAGES...\n";
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    
    $carImageCount = 0;
    $carImageErrors = 0;
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        if (is_array($images)) {
            echo "Car ID {$car->id} ({$car->make} {$car->model}):\n";
            
            foreach ($images as $image) {
                $carImageCount++;
                $filename = basename($image);
                $localPath = storage_path('app/public/car-images/' . $filename);
                
                echo "  Image: {$filename}\n";
                echo "    URL: {$image}\n";
                echo "    Local file: " . (file_exists($localPath) ? "✅ EXISTS" : "❌ MISSING") . "\n";
                
                if (!file_exists($localPath)) {
                    $carImageErrors++;
                }
                
                // Check URL format
                if (strpos($image, 'https://ebisera.com/api/public/storage/car-images/') === 0) {
                    echo "    URL format: ✅ CORRECT\n";
                } else {
                    echo "    URL format: ❌ INCORRECT\n";
                    $carImageErrors++;
                }
                echo "\n";
            }
        }
    }
    
    echo "🧪 TESTING DRIVER IMAGES...\n";
    $drivers = DB::table('drivers')->whereNotNull('profile_image_url')->get();
    
    $driverImageCount = 0;
    $driverImageErrors = 0;
    
    foreach ($drivers as $driver) {
        $driverImageCount++;
        $filename = basename($driver->profile_image_url);
        $localPath = storage_path('app/public/driver-profiles/' . $filename);
        
        echo "Driver ID {$driver->id}:\n";
        echo "  Image: {$filename}\n";
        echo "    URL: {$driver->profile_image_url}\n";
        echo "    Local file: " . (file_exists($localPath) ? "✅ EXISTS" : "❌ MISSING") . "\n";
        
        if (!file_exists($localPath)) {
            $driverImageErrors++;
        }
        
        // Check URL format
        if (strpos($driver->profile_image_url, 'https://ebisera.com/api/public/storage/driver-profiles/') === 0) {
            echo "    URL format: ✅ CORRECT\n";
        } else {
            echo "    URL format: ❌ INCORRECT\n";
            $driverImageErrors++;
        }
        echo "\n";
    }
    
    echo "📊 SUMMARY:\n";
    echo "Car images: {$carImageCount} total, {$carImageErrors} errors\n";
    echo "Driver images: {$driverImageCount} total, {$driverImageErrors} errors\n";
    echo "Total images: " . ($carImageCount + $driverImageCount) . "\n";
    echo "Total errors: " . ($carImageErrors + $driverImageErrors) . "\n";
    
    if (($carImageErrors + $driverImageErrors) === 0) {
        echo "\n🎉 ALL IMAGES ARE WORKING CORRECTLY!\n";
    } else {
        echo "\n❌ SOME IMAGES NEED FIXING\n";
        echo "Run the fix scripts to resolve issues.\n";
    }
    
    echo "\n🔗 TEST URLS:\n";
    echo "API: https://ebisera.com/api/public/api/cars\n";
    echo "Frontend: https://ebisera.com\n";
    
    // Test a few actual URLs
    echo "\n🌐 TESTING ACTUAL ACCESS...\n";
    $testImages = [
        'https://ebisera.com/api/public/storage/car-images/gs9azPWSBKYnJ28bAVkYAkhYBcWUVDwU8AFlFBAW.jpg',
        'https://ebisera.com/api/public/storage/car-images/229yJU1Lz7CaVcszTyfYuAPi2Wib57TvMFMXUrM9.jpg'
    ];
    
    foreach ($testImages as $url) {
        $filename = basename($url);
        $localPath = storage_path('app/public/car-images/' . $filename);
        
        echo "Testing: {$filename}\n";
        echo "  Local file: " . (file_exists($localPath) ? "✅ EXISTS" : "❌ MISSING") . "\n";
        echo "  URL: {$url}\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
