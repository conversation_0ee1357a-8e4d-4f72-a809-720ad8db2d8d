<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingStatusUpdateNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;
    protected $oldStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking, string $oldStatus)
    {
        $this->booking = $booking;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $bookingType = $this->booking->bookable_type === 'App\\Models\\Car' ? 'Car Rental' : 'Driver Service';
        $itemName = $this->booking->bookable_type === 'App\\Models\\Car' 
            ? $this->booking->bookable->make . ' ' . $this->booking->bookable->model
            : 'Driver Service';

        $statusMessage = $this->getStatusMessage();

        return (new MailMessage)
            ->subject('Booking Status Update - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($statusMessage)
            ->line('**Booking Details:**')
            ->line('• ' . $bookingType . ': ' . $itemName)
            ->line('• Booking ID: #' . $this->booking->id)
            ->line('• Start Date: ' . \Carbon\Carbon::parse($this->booking->start_date)->format('M d, Y'))
            ->line('• End Date: ' . \Carbon\Carbon::parse($this->booking->end_date)->format('M d, Y'))
            ->line('• Previous Status: ' . ucfirst($this->oldStatus))
            ->line('• Current Status: ' . ucfirst($this->booking->status))
            ->action('View Booking Details', route('bookings.show', $this->booking))
            ->line('If you have any questions, please contact <NAME_EMAIL> or call 0788613669.')
            ->line('Thank you for choosing Park & Rent!');
    }

    /**
     * Get status-specific message
     */
    private function getStatusMessage(): string
    {
        switch ($this->booking->status) {
            case 'confirmed':
                return 'Great news! Your booking has been confirmed by the owner.';
            case 'cancelled':
                return 'We regret to inform you that your booking has been cancelled.';
            case 'completed':
                return 'Your booking has been completed. We hope you had a great experience!';
            case 'in_progress':
                return 'Your booking is now in progress. Enjoy your rental!';
            default:
                return 'Your booking status has been updated.';
        }
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_status_update',
            'old_status' => $this->oldStatus,
            'new_status' => $this->booking->status,
            'message' => 'Your booking status has been updated'
        ];
    }
}
