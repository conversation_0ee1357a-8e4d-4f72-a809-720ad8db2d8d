Stack trace:
Frame         Function      Args
0007FFFF3440  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF3440, 0007FFFF2340) msys-2.0.dll+0x1FE8E
0007FFFF3440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3718) msys-2.0.dll+0x67F9
0007FFFF3440  000210046832 (000210286019, 0007FFFF32F8, 0007FFFF3440, 000000000000) msys-2.0.dll+0x6832
0007FFFF3440  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3440  000210068E24 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3720  00021006A225 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBAECF0000 ntdll.dll
7FFBAE100000 KERNEL32.DLL
7FFBAC130000 KERNELBASE.dll
7FFBACA20000 USER32.dll
7FFBAC610000 win32u.dll
7FFBAE890000 GDI32.dll
7FFBAC4F0000 gdi32full.dll
7FFBAC640000 msvcp_win.dll
7FFBAC6E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBAE8F0000 advapi32.dll
7FFBAE9B0000 msvcrt.dll
7FFBAD800000 sechost.dll
7FFBAC880000 bcrypt.dll
7FFBAE770000 RPCRT4.dll
7FFBAB710000 CRYPTBASE.DLL
7FFBAC800000 bcryptPrimitives.dll
7FFBACF50000 IMM32.DLL
