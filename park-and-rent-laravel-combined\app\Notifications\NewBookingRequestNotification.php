<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewBookingRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $bookingType = $this->booking->bookable_type === 'App\\Models\\Car' ? 'Car Rental' : 'Driver Service';
        $itemName = $this->booking->bookable_type === 'App\\Models\\Car' 
            ? $this->booking->bookable->make . ' ' . $this->booking->bookable->model
            : 'Driver Service';

        $dashboardRoute = $this->booking->bookable_type === 'App\\Models\\Car' 
            ? route('owner.bookings.index')
            : route('driver.bookings.index');

        return (new MailMessage)
            ->subject('New Booking Request - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have received a new ' . strtolower($bookingType) . ' request.')
            ->line('**Booking Details:**')
            ->line('• ' . $bookingType . ': ' . $itemName)
            ->line('• Booking ID: #' . $this->booking->id)
            ->line('• Customer: ' . $this->booking->user->name)
            ->line('• Start Date: ' . \Carbon\Carbon::parse($this->booking->start_date)->format('M d, Y'))
            ->line('• End Date: ' . \Carbon\Carbon::parse($this->booking->end_date)->format('M d, Y'))
            ->line('• Total Amount: $' . number_format($this->booking->total_amount, 2))
            ->line('• Customer Contact: 0788613669')
            ->action('Review Booking Request', $dashboardRoute)
            ->line('Please review and respond to this booking request as soon as possible.')
            ->line('You can confirm or decline the request from your dashboard.')
            ->line('Thank you for being part of Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'new_booking_request',
            'customer_name' => $this->booking->user->name,
            'message' => 'You have a new booking request'
        ];
    }
}
