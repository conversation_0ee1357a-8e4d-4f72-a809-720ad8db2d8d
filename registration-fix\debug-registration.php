<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== DEBUGGING REGISTRATION ===\n\n";

try {
    echo "🔍 CHECKING REGISTRATION ROUTE...\n";
    
    // Check if registration route exists
    $routes = Route::getRoutes();
    $registrationRoute = null;
    
    foreach ($routes as $route) {
        if ($route->uri() === 'api/register' && in_array('POST', $route->methods())) {
            $registrationRoute = $route;
            break;
        }
    }
    
    if ($registrationRoute) {
        echo "✅ Registration route found: POST /api/register\n";
        echo "Controller: " . $registrationRoute->getActionName() . "\n";
    } else {
        echo "❌ Registration route not found\n";
        
        // Check for alternative routes
        foreach ($routes as $route) {
            if (strpos($route->uri(), 'register') !== false) {
                echo "Found similar route: " . implode('|', $route->methods()) . " /" . $route->uri() . "\n";
            }
        }
    }
    
    echo "\n🔍 CHECKING USERS TABLE STRUCTURE...\n";
    $columns = DB::select("SHOW COLUMNS FROM users");
    
    echo "Users table columns:\n";
    foreach ($columns as $column) {
        $required = $column->Null === 'NO' ? 'REQUIRED' : 'optional';
        $default = $column->Default ? "default: {$column->Default}" : 'no default';
        echo "  - {$column->Field} ({$column->Type}) - {$required} ({$default})\n";
    }
    
    echo "\n🔍 CHECKING VALIDATION RULES...\n";
    
    // Test with sample data
    $testData = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'role' => 'client'
    ];
    
    echo "Testing with sample data:\n";
    foreach ($testData as $key => $value) {
        echo "  {$key}: {$value}\n";
    }
    
    // Check if email already exists
    $existingUser = DB::table('users')->where('email', $testData['email'])->first();
    if ($existingUser) {
        echo "\n⚠️ Email {$testData['email']} already exists in database\n";
        echo "Existing user: ID {$existingUser->id}, Name: {$existingUser->name}\n";
    } else {
        echo "\n✅ Email {$testData['email']} is available\n";
    }
    
    echo "\n🔍 CHECKING REQUIRED FIELDS...\n";
    $requiredFields = [];
    $optionalFields = [];
    
    foreach ($columns as $column) {
        if ($column->Null === 'NO' && $column->Default === null && $column->Extra !== 'auto_increment') {
            $requiredFields[] = $column->Field;
        } else {
            $optionalFields[] = $column->Field;
        }
    }
    
    echo "Required fields for users table:\n";
    foreach ($requiredFields as $field) {
        echo "  ✓ {$field}\n";
    }
    
    echo "\nOptional fields:\n";
    foreach ($optionalFields as $field) {
        echo "  - {$field}\n";
    }
    
    echo "\n🔍 CHECKING EXISTING USERS...\n";
    $userCount = DB::table('users')->count();
    echo "Total users in database: {$userCount}\n";
    
    if ($userCount > 0) {
        $sampleUsers = DB::table('users')->select('id', 'name', 'email', 'role')->limit(5)->get();
        echo "Sample users:\n";
        foreach ($sampleUsers as $user) {
            echo "  ID {$user->id}: {$user->name} ({$user->email}) - Role: {$user->role}\n";
        }
    }
    
    echo "\n🔍 TESTING PASSWORD HASHING...\n";
    $testPassword = 'password123';
    $hashedPassword = Hash::make($testPassword);
    $isValid = Hash::check($testPassword, $hashedPassword);
    
    echo "Test password: {$testPassword}\n";
    echo "Hashed: " . substr($hashedPassword, 0, 30) . "...\n";
    echo "Hash verification: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
