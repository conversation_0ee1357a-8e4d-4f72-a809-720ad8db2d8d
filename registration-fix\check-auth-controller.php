<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CHECKING AUTH CONTROLLER ===\n\n";

try {
    echo "🔍 SEARCHING FOR AUTH CONTROLLERS...\n";
    
    // Check possible controller locations
    $possiblePaths = [
        app_path('Http/Controllers/AuthController.php'),
        app_path('Http/Controllers/Api/AuthController.php'),
        app_path('Http/Controllers/API/AuthController.php'),
        app_path('Http/Controllers/Auth/AuthController.php'),
        app_path('Http/Controllers/Auth/RegisterController.php'),
        app_path('Http/Controllers/Auth/LoginController.php')
    ];
    
    $foundControllers = [];
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $foundControllers[] = $path;
            echo "✅ Found: {$path}\n";
        }
    }
    
    if (empty($foundControllers)) {
        echo "❌ No auth controllers found\n";
        
        // Search for any files containing 'register'
        echo "\n🔍 SEARCHING FOR FILES WITH 'register'...\n";
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(app_path('Http/Controllers'))
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                if (stripos($content, 'register') !== false) {
                    echo "Found 'register' in: " . $file->getPathname() . "\n";
                }
            }
        }
    } else {
        echo "\n🔍 ANALYZING FOUND CONTROLLERS...\n";
        
        foreach ($foundControllers as $controllerPath) {
            echo "\n--- Analyzing: {$controllerPath} ---\n";
            
            $content = file_get_contents($controllerPath);
            $lines = explode("\n", $content);
            
            // Look for register method
            $hasRegister = false;
            $registerMethodLines = [];
            $inRegisterMethod = false;
            $braceCount = 0;
            
            foreach ($lines as $lineNum => $line) {
                $trimmedLine = trim($line);
                
                // Check for register method
                if (preg_match('/function\s+register|public\s+function\s+register/', $trimmedLine)) {
                    $hasRegister = true;
                    $inRegisterMethod = true;
                    $braceCount = 0;
                    echo "✅ Found register method at line " . ($lineNum + 1) . "\n";
                }
                
                if ($inRegisterMethod) {
                    $registerMethodLines[] = "Line " . ($lineNum + 1) . ": " . $trimmedLine;
                    
                    // Count braces to find method end
                    $braceCount += substr_count($line, '{') - substr_count($line, '}');
                    
                    if ($braceCount <= 0 && count($registerMethodLines) > 1) {
                        $inRegisterMethod = false;
                    }
                }
                
                // Look for validation rules
                if (stripos($trimmedLine, 'validate') !== false || stripos($trimmedLine, 'rules') !== false) {
                    echo "Found validation at line " . ($lineNum + 1) . ": {$trimmedLine}\n";
                }
            }
            
            if ($hasRegister) {
                echo "\nRegister method code (first 15 lines):\n";
                foreach (array_slice($registerMethodLines, 0, 15) as $line) {
                    echo "  {$line}\n";
                }
                
                if (count($registerMethodLines) > 15) {
                    echo "  ... (truncated, total " . count($registerMethodLines) . " lines)\n";
                }
            } else {
                echo "❌ No register method found in this controller\n";
            }
            
            // Check for other relevant methods
            $methods = [];
            foreach ($lines as $line) {
                if (preg_match('/public\s+function\s+(\w+)/', $line, $matches)) {
                    $methods[] = $matches[1];
                }
            }
            
            echo "Available methods: " . implode(', ', $methods) . "\n";
        }
    }
    
    echo "\n🔍 CHECKING ROUTES...\n";
    
    // Check all API routes
    $routes = Route::getRoutes();
    $authRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'register') !== false || strpos($uri, 'login') !== false || strpos($uri, 'auth') !== false) {
            $authRoutes[] = [
                'methods' => implode('|', $route->methods()),
                'uri' => $uri,
                'action' => $route->getActionName()
            ];
        }
    }
    
    echo "Authentication-related routes:\n";
    foreach ($authRoutes as $route) {
        echo "  {$route['methods']} /{$route['uri']} -> {$route['action']}\n";
    }
    
    echo "\n📋 SUMMARY:\n";
    echo "Controllers found: " . count($foundControllers) . "\n";
    echo "Auth routes found: " . count($authRoutes) . "\n";
    
    if (empty($foundControllers)) {
        echo "\n⚠️ NO AUTH CONTROLLERS FOUND!\n";
        echo "You may need to create an AuthController or check if registration is handled elsewhere.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
