<!DOCTYPE html>
<html>
<head>
    <title>Park & Rent API Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Park & Rent API Debug Tool</h1>

    <div class="test">
        <h3>Test 1: API Health Check</h3>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result"></div>
    </div>

    <div class="test">
        <h3>Test 2: Cars API</h3>
        <button onclick="testCars()">Test Cars Endpoint</button>
        <div id="cars-result"></div>
    </div>

    <div class="test">
        <h3>Test 3: Login API</h3>
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test">
        <h3>Test 4: CORS Check</h3>
        <button onclick="testCORS()">Test CORS Headers</button>
        <div id="cors-result"></div>
    </div>

    <script>
        const API_BASE = 'https://ebisera.com/api/public/api';

        async function testHealth() {
            const result = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                result.innerHTML = `<div class="success">✅ Success: <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testCars() {
            const result = document.getElementById('cars-result');
            try {
                const response = await fetch(`${API_BASE}/cars`);
                const data = await response.json();
                result.innerHTML = `<div class="success">✅ Success: Found ${data.cars ? data.cars.length : 'unknown'} cars<pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre></div>`;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('login-result');

            // Test multiple credential combinations
            const credentials = [
                { email: '<EMAIL>', password: 'password' },
                { email: '<EMAIL>', password: 'password' },
                { email: '<EMAIL>', password: 'password' }
            ];

            result.innerHTML = '<div>Testing multiple credentials...</div>';

            for (let cred of credentials) {
                try {
                    const response = await fetch(`${API_BASE}/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(cred)
                    });
                    const data = await response.json();

                    if (response.ok) {
                        result.innerHTML += `<div class="success">✅ Login Success with ${cred.email}: <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                        break;
                    } else {
                        result.innerHTML += `<div class="error">❌ Login Failed with ${cred.email} (${response.status}): ${data.message}</div>`;
                    }
                } catch (error) {
                    result.innerHTML += `<div class="error">❌ Error with ${cred.email}: ${error.message}</div>`;
                }
            }
        }

        async function testCORS() {
            const result = document.getElementById('cors-result');
            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'OPTIONS'
                });
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    if (key.toLowerCase().includes('access-control')) {
                        headers[key] = value;
                    }
                }
                result.innerHTML = `<div class="success">✅ CORS Headers: <pre>${JSON.stringify(headers, null, 2)}</pre></div>`;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ CORS Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
