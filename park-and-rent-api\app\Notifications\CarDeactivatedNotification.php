<?php

namespace App\Notifications;

use App\Models\Car;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CarDeactivatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $car;
    public $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(Car $car, string $reason = null)
    {
        $this->car = $car;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $carName = $this->car->make . ' ' . $this->car->model . ' (' . $this->car->year . ')';

        $mailMessage = (new MailMessage)
            ->subject('Car Deactivated - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We are writing to inform you that your car has been deactivated by our administration team.')
            ->line('**Car Details:**')
            ->line('• Car: ' . $carName)
            ->line('• Car ID: #' . $this->car->id)
            ->line('• Location: ' . $this->car->location)
            ->line('• Deactivation Date: ' . now()->format('M j, Y \a\t g:i A'));

        if ($this->reason) {
            $mailMessage->line('• Reason: ' . $this->reason);
        }

        $mailMessage->line('**What this means:**')
            ->line('• Your car is no longer visible to potential renters')
            ->line('• No new bookings can be made for this car')
            ->line('• Existing confirmed bookings will continue as scheduled')
            ->line('• You can still access your car details in your dashboard')
            ->line('**Next Steps:**')
            ->line('If you believe this deactivation was made in error, or if you would like to understand the reason for deactivation, please contact our support team immediately.')
            ->line('Our team will review your case and provide assistance to help you get your car reactivated if appropriate.')
            ->action('Contact Support', 'mailto:<EMAIL>')
            ->line('**Contact Information:**')
            ->line('• Email: <EMAIL>')
            ->line('• Phone: 0788613669')
            ->line('• Support Hours: Monday - Friday, 8:00 AM - 6:00 PM')
            ->line('We appreciate your understanding and look forward to resolving this matter quickly.')
            ->line('Thank you for being part of the Park & Rent community!');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'car_id' => $this->car->id,
            'type' => 'car_deactivated',
            'title' => 'Car Deactivated',
            'message' => 'Your car ' . $this->car->make . ' ' . $this->car->model . ' has been deactivated by administration.',
            'reason' => $this->reason,
            'action_url' => '/owner/cars/' . $this->car->id,
        ];
    }
}
