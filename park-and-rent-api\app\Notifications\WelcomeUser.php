<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeUser extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $roleMessage = match($this->user->role) {
            'client' => 'You can now browse and book cars and drivers.',
            'owner' => 'You can now list your cars for rent and manage bookings.',
            'driver' => 'You can now offer your driving services to clients.',
            'admin' => 'You have full administrative access to the platform.',
            default => 'Welcome to our platform!'
        };

        return (new MailMessage)
            ->subject('Welcome to Park & Rent!')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Welcome to Park & Rent! Your account has been successfully created.')
            ->line('**Account Details:**')
            ->line('Email: ' . $this->user->email)
            ->line('Role: ' . ucfirst($this->user->role))
            ->line('Phone: ' . ($this->user->phone_number ?: 'Not provided'))
            ->line($roleMessage)
            ->action('Get Started', url('/dashboard'))
            ->line('If you have any questions, please contact <NAME_EMAIL> or **********.')
            ->line('Thank you for joining Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'type' => 'welcome',
            'title' => 'Welcome to Park & Rent!',
            'message' => 'Your account has been successfully created. Welcome to our platform!',
            'action_url' => '/dashboard',
        ];
    }
}
