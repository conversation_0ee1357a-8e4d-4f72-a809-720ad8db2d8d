import { apiClient } from '../config/api';
import { Car, CarFilters } from '../types';

export interface CarResponse {
  success: boolean;
  data: Car[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface CarDetailResponse {
  success: boolean;
  data: Car;
}

export const carService = {
  // Get all cars with filters
  getCars: async (filters?: CarFilters): Promise<CarResponse> => {
    const params = new URLSearchParams();
    
    if (filters?.search) params.append('search', filters.search);
    if (filters?.location) params.append('location', filters.location);
    if (filters?.min_price) params.append('min_price', filters.min_price.toString());
    if (filters?.max_price) params.append('max_price', filters.max_price.toString());
    if (filters?.make) params.append('make', filters.make);
    if (filters?.transmission) params.append('transmission', filters.transmission);
    if (filters?.fuel_type) params.append('fuel_type', filters.fuel_type);
    if (filters?.min_year) params.append('min_year', filters.min_year.toString());
    if (filters?.max_year) params.append('max_year', filters.max_year.toString());
    if (filters?.seats) params.append('seats', filters.seats.toString());
    if (filters?.has_gps) params.append('has_gps', '1');
    if (filters?.available_from) params.append('available_from', filters.available_from);
    if (filters?.available_to) params.append('available_to', filters.available_to);
    if (filters?.sort_by) params.append('sort_by', filters.sort_by);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.per_page) params.append('per_page', filters.per_page.toString());
    
    if (filters?.features && filters.features.length > 0) {
      filters.features.forEach(feature => {
        params.append('features[]', feature);
      });
    }

    const response = await apiClient.get(`/v1/cars?${params.toString()}`);
    return response.data;
  },

  // Get car by ID
  getCar: async (id: string): Promise<CarDetailResponse> => {
    const response = await apiClient.get(`/v1/cars/${id}`);
    return response.data;
  },

  // Create new car (authenticated)
  createCar: async (carData: Partial<Car>): Promise<CarDetailResponse> => {
    const response = await apiClient.post('/v1/my-cars', carData);
    return response.data;
  },

  // Update car (authenticated)
  updateCar: async (id: string, carData: Partial<Car>): Promise<CarDetailResponse> => {
    const response = await apiClient.put(`/v1/my-cars/${id}`, carData);
    return response.data;
  },

  // Delete car (authenticated)
  deleteCar: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete(`/v1/my-cars/${id}`);
    return response.data;
  },

  // Get my cars (authenticated)
  getMyCars: async (): Promise<CarResponse> => {
    const response = await apiClient.get('/v1/my-cars');
    return response.data;
  }
};
