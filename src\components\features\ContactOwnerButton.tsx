import React, { useState } from 'react';
import { Phone, AlertCircle } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import { User } from '../../types';

interface ContactOwnerButtonProps {
  ownerPhone?: string;
  ownerId: string;
  buttonStyle?: 'full' | 'compact';
}

const ContactOwnerButton: React.FC<ContactOwnerButtonProps> = ({
  ownerPhone = '**********', // Updated phone number
  ownerId,
  buttonStyle = 'full',
}) => {
  const { user, isAuthenticated } = useAuth();
  const [isPhoneVisible, setIsPhoneVisible] = useState(false);

  const handleShowPhone = () => {
    if (isAuthenticated && user?.role === 'client' && user?.licenseVerificationStatus === 'verified') {
      setIsPhoneVisible(true);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <AlertCircle size={20} className="text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="ml-3">
            <h3 className="font-medium text-yellow-800">Authentication required</h3>
            <p className="text-sm text-yellow-700 mt-1">
              Please log in to contact the car owner.
            </p>
            <div className="mt-3">
              <Button
                variant="primary"
                onClick={() => window.location.href = '/login'}
              >
                Log In
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (user?.role !== 'client') {
    return (
      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <AlertCircle size={20} className="text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="ml-3">
            <h3 className="font-medium text-yellow-800">Owner account detected</h3>
            <p className="text-sm text-yellow-700 mt-1">
              You need a client account to contact car owners.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (user?.licenseVerificationStatus !== 'verified') {
    return (
      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <AlertCircle size={20} className="text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="ml-3">
            <h3 className="font-medium text-yellow-800">License verification required</h3>
            <p className="text-sm text-yellow-700 mt-1">
              Your driving license needs to be verified before you can contact car owners.
            </p>
            <div className="mt-3">
              <Button
                variant="primary"
                onClick={() => window.location.href = '/account/verification'}
              >
                Verify License
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (buttonStyle === 'compact') {
    return (
      <Button
        variant="outline"
        onClick={handleShowPhone}
        className="flex-1 flex items-center justify-center"
        disabled={isPhoneVisible}
      >
        <Phone size={18} className="mr-2" />
        {isPhoneVisible ? ownerPhone : 'Show Contact'}
      </Button>
    );
  }

  return (
    <div className="mt-4">
      {isPhoneVisible ? (
        <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
          <div className="flex items-center">
            <Phone size={20} className="text-success-500 flex-shrink-0" />
            <div className="ml-3">
              <h3 className="font-medium text-success-800">Contact the owner</h3>
              <p className="text-lg font-medium text-success-700 mt-1">
                {ownerPhone}
              </p>
              <p className="text-sm text-success-600 mt-2">
                Call or text the owner to arrange rental details directly.
              </p>
            </div>
          </div>
        </div>
      ) : (
        <Button
          variant="primary"
          onClick={handleShowPhone}
          className="w-full flex items-center justify-center"
        >
          <Phone size={18} className="mr-2" />
          Show Phone Number
        </Button>
      )}

      <p className="text-sm text-gray-500 mt-2">
        <AlertCircle size={14} className="inline mr-1" />
        We only connect you with the owner. All arrangements and payments are handled directly between you and the owner.
      </p>
    </div>
  );
};

export default ContactOwnerButton;