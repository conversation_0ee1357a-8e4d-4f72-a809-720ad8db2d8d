@extends('layouts.app')

@section('title', 'Chat with ' . $otherUser->name)

@section('content')
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('chat.index') }}" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                        
                        <!-- Avatar -->
                        <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        
                        <div>
                            <h2 class="text-lg font-medium text-gray-900">{{ $otherUser->name }}</h2>
                            @if($chat->relatedTo)
                                <div class="flex items-center text-sm text-gray-600">
                                    @if($chat->related_to_type === 'App\\Models\\Car')
                                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                        </svg>
                                        <span>About: {{ $chat->relatedTo->make }} {{ $chat->relatedTo->model }}</span>
                                    @elseif($chat->related_to_type === 'App\\Models\\Driver')
                                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                        <span>About: Driver Service</span>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="text-right">
                        <p class="text-sm text-gray-600">{{ $otherUser->email }}</p>
                        <p class="text-sm text-gray-600">0788613669</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Container -->
        <div class="bg-white shadow rounded-lg flex flex-col" style="height: 600px;">
            <!-- Messages Area -->
            <div id="messages-container" class="flex-1 overflow-y-auto p-6 space-y-4">
                @foreach($chat->messages as $message)
                    <div class="flex {{ $message->sender_id === auth()->id() ? 'justify-end' : 'justify-start' }}">
                        <div class="max-w-xs lg:max-w-md">
                            @if($message->sender_id !== auth()->id())
                                <!-- Other user's message -->
                                <div class="flex items-start space-x-2">
                                    <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="bg-gray-100 rounded-lg px-4 py-2">
                                            <p class="text-sm text-gray-900">{{ $message->content }}</p>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">{{ $message->created_at->format('H:i') }}</p>
                                    </div>
                                </div>
                            @else
                                <!-- Own message -->
                                <div class="flex items-start space-x-2 justify-end">
                                    <div class="text-right">
                                        <div class="bg-primary-600 rounded-lg px-4 py-2">
                                            <p class="text-sm text-white">{{ $message->content }}</p>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">{{ $message->created_at->format('H:i') }}</p>
                                    </div>
                                    <div class="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Message Input -->
            <div class="border-t border-gray-200 p-4">
                <form id="message-form" action="{{ route('chat.send-message', $chat) }}" method="POST" class="flex space-x-4">
                    @csrf
                    <div class="flex-1">
                        <textarea 
                            name="message" 
                            id="message-input"
                            rows="2" 
                            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 resize-none"
                            placeholder="Type your message..."
                            required></textarea>
                    </div>
                    <div class="flex-shrink-0">
                        <button 
                            type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            <span class="ml-2 hidden sm:block">Send</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('messages-container');
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');

    // Scroll to bottom of messages
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Initial scroll to bottom
    scrollToBottom();

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(messageForm);
        const messageContent = messageInput.value.trim();
        
        if (!messageContent) return;

        // Add message to UI immediately
        addMessageToUI(messageContent, true);
        
        // Clear input
        messageInput.value = '';
        
        // Send message via AJAX
        fetch(messageForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                // Remove the message if sending failed
                const lastMessage = messagesContainer.lastElementChild;
                if (lastMessage) {
                    lastMessage.remove();
                }
                alert('Failed to send message. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Remove the message if sending failed
            const lastMessage = messagesContainer.lastElementChild;
            if (lastMessage) {
                lastMessage.remove();
            }
            alert('Failed to send message. Please try again.');
        });
    });

    // Add message to UI
    function addMessageToUI(content, isOwn) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `flex ${isOwn ? 'justify-end' : 'justify-start'}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        if (isOwn) {
            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md">
                    <div class="flex items-start space-x-2 justify-end">
                        <div class="text-right">
                            <div class="bg-primary-600 rounded-lg px-4 py-2">
                                <p class="text-sm text-white">${content}</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">${timeString}</p>
                        </div>
                        <div class="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                    </div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md">
                    <div class="flex items-start space-x-2">
                        <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <div class="bg-gray-100 rounded-lg px-4 py-2">
                                <p class="text-sm text-gray-900">${content}</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">${timeString}</p>
                        </div>
                    </div>
                </div>
            `;
        }

        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
    }

    // Handle Enter key to send message
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            messageForm.dispatchEvent(new Event('submit'));
        }
    });

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });
});
</script>
@endsection
