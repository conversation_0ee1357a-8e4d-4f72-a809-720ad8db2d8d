<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CHECKING AND FIXING DRIVER MODEL ===\n\n";

try {
    // Check if Driver model exists and has image URL issues
    $driverModelPath = app_path('Models/Driver.php');
    
    if (file_exists($driverModelPath)) {
        echo "✅ Driver model found at: {$driverModelPath}\n";
        
        $content = file_get_contents($driverModelPath);
        
        // Check if it has URL generation issues
        if (strpos($content, 'localhost') !== false || strpos($content, '127.0.0.1') !== false) {
            echo "❌ Driver model contains localhost references\n";
            
            // Fix localhost references
            $content = str_replace('http://localhost:8000', 'https://ebisera.com/api/public', $content);
            $content = str_replace('http://127.0.0.1:8000', 'https://ebisera.com/api/public', $content);
            
            // Backup original
            copy($driverModelPath, $driverModelPath . '.backup');
            
            // Write fixed content
            file_put_contents($driverModelPath, $content);
            
            echo "✅ Fixed Driver model (backup created)\n";
        } else {
            echo "✅ Driver model looks good\n";
        }
        
        // Show current content related to images
        echo "\n📋 Driver model image-related code:\n";
        $lines = explode("\n", $content);
        foreach ($lines as $lineNum => $line) {
            if (stripos($line, 'image') !== false || stripos($line, 'url') !== false || stripos($line, 'storage') !== false) {
                echo "Line " . ($lineNum + 1) . ": " . trim($line) . "\n";
            }
        }
        
    } else {
        echo "ℹ️ Driver model not found - this is normal if using database directly\n";
    }
    
    // Check Car model too
    $carModelPath = app_path('Models/Car.php');
    
    if (file_exists($carModelPath)) {
        echo "\n✅ Car model found at: {$carModelPath}\n";
        
        $content = file_get_contents($carModelPath);
        
        // Check if it has URL generation issues
        if (strpos($content, 'localhost') !== false || strpos($content, '127.0.0.1') !== false) {
            echo "❌ Car model contains localhost references\n";
            
            // Fix localhost references
            $content = str_replace('http://localhost:8000', 'https://ebisera.com/api/public', $content);
            $content = str_replace('http://127.0.0.1:8000', 'https://ebisera.com/api/public', $content);
            
            // Backup original
            copy($carModelPath, $carModelPath . '.backup');
            
            // Write fixed content
            file_put_contents($carModelPath, $content);
            
            echo "✅ Fixed Car model (backup created)\n";
        } else {
            echo "✅ Car model looks good\n";
        }
    }
    
    echo "\n🎯 Model check complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
