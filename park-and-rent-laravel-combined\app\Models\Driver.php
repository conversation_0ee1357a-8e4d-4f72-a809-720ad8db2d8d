<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Driver extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'age',
        'experience',
        'profile_image',
        'license_number',
        'license_verification_status',
        'location',
        'price_per_hour',
        'rating',
        'reviews',
        'specialties',
        'availability_notes',
        'is_available',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'age' => 'integer',
        'experience' => 'integer',
        'price_per_hour' => 'decimal:2',
        'rating' => 'decimal:2',
        'reviews' => 'integer',
        'specialties' => 'array',
        'is_available' => 'boolean',
    ];

    /**
     * Get the user that owns the driver profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bookings for the driver.
     */
    public function bookings(): MorphMany
    {
        return $this->morphMany(Booking::class, 'item', 'item_type', 'item_id');
    }
}
