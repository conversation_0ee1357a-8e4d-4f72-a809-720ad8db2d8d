<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\TicketBooking;
use App\Notifications\TicketBookingReceived;
use App\Notifications\TicketBookingStatusUpdated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;
use App\Models\User;

class TicketBookingController extends Controller
{
    /**
     * Display a listing of ticket bookings.
     * Public users can only see their own bookings.
     * Admins can see all bookings.
     */
    public function index()
    {
        if (auth()->user()->role === 'admin') {
            $ticketBookings = TicketBooking::with('user')
                ->orderBy('created_at', 'desc')
                ->get();
        } else {
            $ticketBookings = TicketBooking::where('user_id', auth()->id())
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return response()->json([
            'success' => true,
            'data' => $ticketBookings
        ]);
    }

    /**
     * Store a newly created ticket booking.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ticket_type' => 'required|in:flight,bus',
            'passenger_name' => 'required|string|max:255',
            'passenger_email' => 'required|email|max:255',
            'passenger_phone' => 'required|string|max:20',
            'departure_city' => 'required|string|max:255',
            'destination_country' => 'required|string|max:255',
            'destination_city' => 'required|string|max:255',
            'departure_date' => 'required|date|after:today',
            'return_date' => 'nullable|date|after:departure_date',
            'trip_type' => 'required|in:one_way,round_trip',
            'passengers_count' => 'required|integer|min:1|max:10',
            'class_type' => 'required|in:economy,business,first',
            'special_requests' => 'nullable|string|max:1000',
        ], [
            'ticket_type.required' => 'Please select a ticket type (Flight or Bus).',
            'passenger_name.required' => 'Passenger name is required.',
            'passenger_email.required' => 'Passenger email is required.',
            'passenger_email.email' => 'Please enter a valid email address.',
            'passenger_phone.required' => 'Passenger phone number is required.',
            'departure_city.required' => 'Departure city is required.',
            'destination_country.required' => 'Destination country is required.',
            'destination_city.required' => 'Destination city is required.',
            'departure_date.required' => 'Departure date is required.',
            'departure_date.after' => 'Departure date must be in the future.',
            'return_date.after' => 'Return date must be after departure date.',
            'trip_type.required' => 'Please select trip type (One Way or Round Trip).',
            'passengers_count.required' => 'Number of passengers is required.',
            'passengers_count.min' => 'At least 1 passenger is required.',
            'passengers_count.max' => 'Maximum 10 passengers allowed.',
            'class_type.required' => 'Please select a class type.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your booking information.',
                'errors' => $validator->errors(),
                'error_type' => 'validation_failed'
            ], 422);
        }

        // Validate destination country is East African
        $eastAfricanCountries = TicketBooking::getEastAfricanCountries();
        if (!array_key_exists($request->destination_country, $eastAfricanCountries)) {
            return response()->json([
                'success' => false,
                'message' => 'We only provide tickets to East African countries.',
                'error' => 'Please select a valid East African destination.',
                'error_type' => 'invalid_destination'
            ], 422);
        }

        try {
            $ticketBooking = TicketBooking::create([
                'user_id' => auth()->id(),
                'ticket_type' => $request->ticket_type,
                'passenger_name' => $request->passenger_name,
                'passenger_email' => $request->passenger_email,
                'passenger_phone' => $request->passenger_phone,
                'departure_country' => 'Rwanda', // Default departure country
                'departure_city' => $request->departure_city,
                'destination_country' => $request->destination_country,
                'destination_city' => $request->destination_city,
                'departure_date' => $request->departure_date,
                'return_date' => $request->return_date,
                'trip_type' => $request->trip_type,
                'passengers_count' => $request->passengers_count,
                'class_type' => $request->class_type,
                'special_requests' => $request->special_requests,
                'status' => 'pending',
            ]);

            // Notify all admins about the new ticket booking
            $admins = User::where('role', 'admin')->get();
            Notification::send($admins, new TicketBookingReceived($ticketBooking));

            return response()->json([
                'success' => true,
                'message' => 'Ticket booking submitted successfully! Our team will contact you soon.',
                'data' => $ticketBooking
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit ticket booking.',
                'error' => 'Unable to process your booking at this time. Please try again later.',
                'error_type' => 'server_error',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Display the specified ticket booking.
     */
    public function show(string $id)
    {
        $ticketBooking = TicketBooking::with('user')->findOrFail($id);

        // Users can only view their own bookings unless they're admin
        if (auth()->user()->role !== 'admin' && $ticketBooking->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access.',
                'error' => 'You can only view your own ticket bookings.',
                'error_type' => 'unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $ticketBooking
        ]);
    }

    /**
     * Update the specified ticket booking (Admin only).
     */
    public function update(Request $request, string $id)
    {
        if (auth()->user()->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access.',
                'error' => 'Only administrators can update ticket bookings.',
                'error_type' => 'unauthorized'
            ], 403);
        }

        $ticketBooking = TicketBooking::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:pending,contacted,confirmed,cancelled',
            'estimated_price' => 'sometimes|numeric|min:0',
            'admin_notes' => 'sometimes|nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors(),
                'error_type' => 'validation_failed'
            ], 422);
        }

        $oldStatus = $ticketBooking->status;
        $updateData = $request->only(['status', 'estimated_price', 'admin_notes']);

        // Set timestamps based on status
        if ($request->has('status')) {
            if ($request->status === 'contacted' && $oldStatus !== 'contacted') {
                $updateData['contacted_at'] = now();
            } elseif ($request->status === 'confirmed' && $oldStatus !== 'confirmed') {
                $updateData['confirmed_at'] = now();
            }
        }

        $ticketBooking->update($updateData);

        // Notify user if status changed
        if ($request->has('status') && $request->status !== $oldStatus) {
            $ticketBooking->user->notify(new TicketBookingStatusUpdated($ticketBooking, $oldStatus));
        }

        return response()->json([
            'success' => true,
            'message' => 'Ticket booking updated successfully.',
            'data' => $ticketBooking
        ]);
    }

    /**
     * Remove the specified ticket booking.
     */
    public function destroy(string $id)
    {
        $ticketBooking = TicketBooking::findOrFail($id);

        // Users can only cancel their own bookings, admins can delete any
        if (auth()->user()->role !== 'admin' && $ticketBooking->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access.',
                'error' => 'You can only cancel your own ticket bookings.',
                'error_type' => 'unauthorized'
            ], 403);
        }

        // If user is cancelling their own booking, just update status
        if (auth()->user()->role !== 'admin') {
            $ticketBooking->update(['status' => 'cancelled']);
            return response()->json([
                'success' => true,
                'message' => 'Ticket booking cancelled successfully.'
            ]);
        }

        // Admin can actually delete the booking
        $ticketBooking->delete();

        return response()->json([
            'success' => true,
            'message' => 'Ticket booking deleted successfully.'
        ]);
    }

    /**
     * Get East African countries and cities for the booking form.
     */
    public function getDestinations()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'countries' => TicketBooking::getEastAfricanCountries(),
                'cities' => TicketBooking::getMajorCities()
            ]
        ]);
    }

    /**
     * Get user's ticket bookings.
     */
    public function myTicketBookings()
    {
        $user = auth()->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
                'error' => 'Authentication required to view ticket bookings.',
                'error_type' => 'unauthenticated'
            ], 401);
        }

        $ticketBookings = TicketBooking::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $ticketBookings
        ]);
    }
}
