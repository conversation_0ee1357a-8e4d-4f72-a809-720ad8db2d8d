#!/bin/bash

echo "========================================"
echo "Laravel Backend Setup Script"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

cd park-and-rent-api

# Step 1: Install Composer dependencies
print_status "Step 1: Installing Composer dependencies..."
composer install
if [ $? -ne 0 ]; then
    print_error "Composer install failed!"
    exit 1
fi

# Step 2: Setup environment file
print_status "Step 2: Setting up environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    print_status ".env file created from .env.example"
else
    print_warning ".env file already exists"
fi

# Step 3: Generate application key
print_status "Step 3: Generating application key..."
php artisan key:generate

# Step 4: Create database (SQLite for development)
print_status "Step 4: Creating database (SQLite for development)..."
if [ ! -f database/database.sqlite ]; then
    touch database/database.sqlite
    print_status "SQLite database created"
fi

# Step 5: Run migrations
print_status "Step 5: Running migrations..."
php artisan migrate
if [ $? -ne 0 ]; then
    print_warning "Migrations failed. Check your database configuration."
fi

# Step 6: Seed database
print_status "Step 6: Seeding database..."
php artisan db:seed
if [ $? -ne 0 ]; then
    print_warning "Database seeding failed."
fi

# Step 7: Create storage link
print_status "Step 7: Creating storage link..."
php artisan storage:link

# Step 8: Clear caches
print_status "Step 8: Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

cd ..

echo
echo "========================================"
print_status "Laravel Backend Setup Complete!"
echo "========================================"
echo
print_status "You can now start the development server with:"
echo "cd park-and-rent-api"
echo "php artisan serve"
echo
print_status "The API will be available at: http://127.0.0.1:8000"
echo
