<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Models\User;
use App\Models\GpsInstallationRequest;
use App\Models\TicketBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Notifications\CarDeactivatedNotification;
use App\Notifications\DriverDeactivatedNotification;

class AdminController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Middleware is handled in routes/api.php
    }

    /**
     * Get dashboard statistics.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboard()
    {
        // Check if user is admin
        if (!auth()->check() || auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized. Admin access required.'], 403);
        }

        try {
            $stats = [
                'users_count' => User::count(),
                'cars_count' => Car::count(),
                'drivers_count' => Driver::count(),
                'bookings_count' => Booking::count(),
                'pending_bookings' => Booking::where('status', 'pending')->count(),
                'completed_bookings' => Booking::where('status', 'completed')->count(),
                'cancelled_bookings' => Booking::where('status', 'cancelled')->count(),
                'pending_driver_verifications' => Driver::where('license_verification_status', 'pending')->count(),
                'revenue' => Booking::where('status', 'completed')->sum('total_price') ?? 0,
                'recent_bookings' => Booking::with(['user'])->orderBy('created_at', 'desc')->limit(5)->get(),
                'recent_users' => User::orderBy('created_at', 'desc')->limit(5)->get(),

                // Ticket Booking Statistics
                'ticket_bookings_count' => TicketBooking::count(),
                'pending_ticket_bookings' => TicketBooking::where('status', 'pending')->count(),
                'confirmed_ticket_bookings' => TicketBooking::where('status', 'confirmed')->count(),
                'cancelled_ticket_bookings' => TicketBooking::where('status', 'cancelled')->count(),
                'flight_bookings' => TicketBooking::where('ticket_type', 'flight')->count(),
                'bus_bookings' => TicketBooking::where('ticket_type', 'bus')->count(),
                'recent_ticket_bookings' => TicketBooking::orderBy('created_at', 'desc')->limit(5)->get(),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            Log::error('Admin dashboard error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to fetch dashboard data',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }



    /**
     * Get all users.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function users()
    {
        $users = User::all();
        return response()->json($users);
    }

    /**
     * Get all cars.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cars()
    {
        $cars = Car::with('owner')->get();
        return response()->json($cars);
    }

    /**
     * Get all drivers.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function drivers()
    {
        $drivers = Driver::with('user')->get();
        return response()->json($drivers);
    }

    /**
     * Get all bookings.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function bookings()
    {
        $bookings = Booking::with(['user'])->get();
        return response()->json($bookings);
    }

    /**
     * Verify a driver's license.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyDriverLicense(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,verified,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $driver = Driver::findOrFail($id);
        $driver->update([
            'license_verification_status' => $request->status
        ]);

        // Also update the user's license verification status
        $user = User::find($driver->user_id);
        $user->update([
            'license_verification_status' => $request->status
        ]);

        return response()->json([
            'message' => 'Driver license verification status updated successfully',
            'status' => $request->status
        ]);
    }

    /**
     * Update user role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserRole(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'role' => 'required|in:client,owner,driver,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::findOrFail($id);
        $user->update([
            'role' => $request->role
        ]);

        return response()->json([
            'message' => 'User role updated successfully',
            'role' => $request->role
        ]);
    }

    /**
     * Get revenue statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function revenue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|in:daily,weekly,monthly,yearly',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $query = Booking::where('status', 'completed');

        // Apply date filters if provided
        if ($request->has('start_date')) {
            $query->where('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date')) {
            $query->where('created_at', '<=', $request->end_date);
        }

        // Group by period if specified
        if ($request->has('period')) {
            $period = $request->period;

            if ($period === 'daily') {
                $stats = $query->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('date')
                ->orderBy('date')
                ->get();
            } elseif ($period === 'weekly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('WEEK(created_at) as week'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year', 'week')
                ->orderBy('year')
                ->orderBy('week')
                ->get();
            } elseif ($period === 'monthly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year', 'month')
                ->orderBy('year')
                ->orderBy('month')
                ->get();
            } elseif ($period === 'yearly') {
                $stats = $query->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('year')
                ->orderBy('year')
                ->get();
            }

            return response()->json($stats);
        }

        // If no period specified, return total
        $total = [
            'revenue' => $query->sum('total_price'),
            'count' => $query->count(),
        ];

        return response()->json($total);
    }

    /**
     * Get all GPS installation requests.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function gpsRequests()
    {
        $requests = GpsInstallationRequest::with(['user', 'car'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($requests);
    }

    /**
     * Update GPS installation request status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateGpsRequestStatus(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,approved,rejected,completed',
            'admin_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $gpsRequest = GpsInstallationRequest::findOrFail($id);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        if ($request->status === 'approved') {
            $updateData['approved_at'] = now();
        } elseif ($request->status === 'completed') {
            $updateData['completed_at'] = now();
        }

        $gpsRequest->update($updateData);

        return response()->json([
            'message' => 'GPS installation request status updated successfully',
            'request' => $gpsRequest->load(['user', 'car'])
        ]);
    }

    /**
     * Create a new user.
     */
    public function createUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'role' => 'required|in:client,owner,driver,admin',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => bcrypt($request->password),
        ]);

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user
        ], 201);
    }

    /**
     * Update a user.
     */
    public function updateUser(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'role' => 'required|in:client,owner,driver,admin',
            'password' => 'nullable|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::findOrFail($id);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ];

        if ($request->password) {
            $updateData['password'] = bcrypt($request->password);
        }

        $user->update($updateData);

        return response()->json([
            'message' => 'User updated successfully',
            'user' => $user
        ]);
    }

    /**
     * Delete a user.
     */
    public function deleteUser(string $id)
    {
        $user = User::findOrFail($id);
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Create a new car.
     */
    public function createCar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'pricePerHour' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string',
            'ownerId' => 'required|exists:users,id',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Verify the selected user has owner role
        $owner = User::findOrFail($request->ownerId);
        if ($owner->role !== 'owner') {
            return response()->json(['error' => 'Selected user must have owner role'], 422);
        }

        $car = Car::create([
            'make' => $request->make,
            'model' => $request->model,
            'year' => $request->year,
            'price_per_hour' => $request->pricePerHour,
            'location' => $request->location,
            'description' => $request->description,
            'owner_id' => $request->ownerId,
            'is_active' => true,
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $filename = Str::random(40) . '.' . $image->getClientOriginalExtension();
                $image->storeAs('car-images', $filename, 'public_direct');
                $imagePaths[] = url('/car-images/' . $filename);
            }
            $car->images = json_encode($imagePaths);
            $car->save();
        }

        return response()->json([
            'message' => 'Car created successfully and assigned to owner',
            'car' => $car->load('owner')
        ], 201);
    }

    /**
     * Update a car.
     */
    public function updateCar(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'pricePerHour' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $car = Car::findOrFail($id);

        $car->update([
            'make' => $request->make,
            'model' => $request->model,
            'year' => $request->year,
            'price_per_hour' => $request->pricePerHour,
            'location' => $request->location,
            'description' => $request->description,
        ]);

        return response()->json([
            'message' => 'Car updated successfully',
            'car' => $car->load('owner')
        ]);
    }

    /**
     * Delete a car.
     */
    public function deleteCar(string $id)
    {
        $car = Car::findOrFail($id);
        $car->delete();

        return response()->json([
            'message' => 'Car deleted successfully'
        ]);
    }

    /**
     * Toggle car active status.
     */
    public function toggleCarStatus(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
            'reason' => 'nullable|string|max:500', // Optional reason for deactivation
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $car = Car::with('owner')->findOrFail($id);
        $wasActive = $car->is_active;
        $car->is_active = $request->is_active;
        $car->save();

        // Send email notification if car is being deactivated
        if ($wasActive && !$request->is_active) {
            $reason = $request->reason ?: 'Administrative decision';
            $car->owner->notify(new CarDeactivatedNotification($car, $reason));
        }

        $statusMessage = $request->is_active ? 'activated' : 'deactivated';
        $notificationMessage = $request->is_active
            ? 'Car activated successfully'
            : 'Car deactivated successfully. Owner has been notified via email.';

        return response()->json([
            'message' => $notificationMessage,
            'car' => $car->load('owner'),
            'status' => $statusMessage
        ]);
    }

    /**
     * Toggle driver active status.
     */
    public function toggleDriverStatus(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
            'reason' => 'nullable|string|max:500', // Optional reason for deactivation
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $driver = Driver::with('user')->findOrFail($id);
        $wasActive = $driver->is_active;
        $driver->is_active = $request->is_active;
        $driver->save();

        // Send email notification if driver is being deactivated
        if ($wasActive && !$request->is_active) {
            $reason = $request->reason ?: 'Administrative decision';
            $driver->user->notify(new DriverDeactivatedNotification($driver, $reason));
        }

        $statusMessage = $request->is_active ? 'activated' : 'deactivated';
        $notificationMessage = $request->is_active
            ? 'Driver activated successfully'
            : 'Driver deactivated successfully. Driver has been notified via email.';

        return response()->json([
            'message' => $notificationMessage,
            'driver' => $driver->load('user'),
            'status' => $statusMessage
        ]);
    }
}
