@extends('layouts.app')

@section('title', $car->make . ' ' . $car->model)

@section('content')
<div class="bg-white">
    <!-- Back Button -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <a href="{{ route('cars.index') }}" class="inline-flex items-center text-primary-600 hover:text-primary-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Cars
        </a>
    </div>

    <!-- Car Details -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Image Gallery -->
            <div class="space-y-4">
                <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden">
                    @if($car->images && count($car->images) > 0)
                        <img id="mainImage" class="w-full h-96 object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                    @else
                        <div class="w-full h-96 flex items-center justify-center bg-gray-300">
                            <svg class="h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    @endif
                </div>

                <!-- Thumbnail Gallery -->
                @if($car->images && count($car->images) > 1)
                <div class="grid grid-cols-4 gap-2">
                    @foreach($car->images as $index => $image)
                    <button onclick="changeMainImage('{{ asset('storage/' . $image) }}')"
                            class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden hover:opacity-75 transition-opacity">
                        <img class="w-full h-20 object-cover" src="{{ asset('storage/' . $image) }}" alt="Car image {{ $index + 1 }}">
                    </button>
                    @endforeach
                </div>
                @endif
            </div>

            <!-- Car Information -->
            <div class="space-y-6">
                <!-- Header -->
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $car->make }} {{ $car->model }}</h1>
                    <p class="text-lg text-gray-600">{{ $car->year }}</p>
                    <div class="flex items-center mt-2">
                        <svg class="h-5 w-5 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span class="text-gray-600">{{ $car->location }}</span>
                    </div>
                </div>

                <!-- Price -->
                <div class="bg-primary-50 p-4 rounded-lg">
                    <div class="flex items-baseline">
                        <span class="text-3xl font-bold text-primary-600">${{ $car->price_per_day }}</span>
                        <span class="text-lg text-gray-600 ml-2">per day</span>
                    </div>
                    @if($car->price_per_hour)
                    <div class="flex items-baseline mt-1">
                        <span class="text-xl font-semibold text-primary-600">${{ $car->price_per_hour }}</span>
                        <span class="text-sm text-gray-600 ml-2">per hour</span>
                    </div>
                    @endif
                </div>

                <!-- Car Details -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-600">Transmission</div>
                        <div class="font-medium">{{ ucfirst($car->transmission) }}</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-600">Fuel Type</div>
                        <div class="font-medium">{{ ucfirst($car->fuel_type) }}</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-600">Seats</div>
                        <div class="font-medium">{{ $car->seats }} seats</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-600">Mileage</div>
                        <div class="font-medium">{{ number_format($car->mileage) }} km</div>
                    </div>
                </div>

                <!-- Features -->
                @if($car->features && count($car->features) > 0)
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Features</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($car->features as $feature)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                            {{ $feature }}
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Description -->
                @if($car->description)
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Description</h3>
                    <p class="text-gray-600">{{ $car->description }}</p>
                </div>
                @endif

                <!-- Owner Info -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Owner</h3>
                    <div class="flex items-center space-x-3">
                        <div class="h-12 w-12 bg-gray-300 rounded-full flex items-center justify-center">
                            @if($car->owner && $car->owner->profile_image)
                                <img class="h-12 w-12 rounded-full object-cover" src="{{ asset('storage/' . $car->owner->profile_image) }}" alt="{{ $car->owner->name }}">
                            @else
                                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            @endif
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{{ $car->owner ? $car->owner->name : 'Car Owner' }}</div>
                            <div class="text-sm text-gray-600">Member since {{ $car->owner ? $car->owner->created_at->format('Y') : '2024' }}</div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    @auth
                        @if(auth()->user()->role === 'client')
                            <button onclick="openBookingModal()" class="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-lg font-medium transition-colors">
                                Book This Car
                            </button>
                            <div class="grid grid-cols-2 gap-3">
                                <a href="{{ route('chat.start-car-owner', $car) }}" class="flex items-center justify-center border border-primary-600 text-primary-600 hover:bg-primary-50 px-4 py-2 rounded-md font-medium transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    Chat
                                </a>
                                <button onclick="openContactModal()" class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-md font-medium transition-colors">
                                    Contact
                                </button>
                            </div>
                        @else
                            <div class="text-center text-gray-600 py-4">
                                <p>Only verified clients can book cars.</p>
                                @if(auth()->user()->role !== 'client')
                                <p class="text-sm">Switch to client account to book this car.</p>
                                @endif
                            </div>
                        @endif
                    @else
                        <a href="{{ route('login') }}" class="block w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center transition-colors">
                            Login to Book
                        </a>
                        <a href="{{ route('register') }}" class="block w-full border border-primary-600 text-primary-600 hover:bg-primary-50 px-6 py-3 rounded-md text-lg font-medium text-center transition-colors">
                            Sign Up
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Book {{ $car->make }} {{ $car->model }}</h3>
                <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="{{ route('bookings.store') }}" method="POST">
                @csrf
                <input type="hidden" name="car_id" value="{{ $car->id }}">

                <div class="space-y-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" name="start_date" id="start_date" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                        <input type="date" name="end_date" id="end_date" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700">Message to Owner (Optional)</label>
                        <textarea name="message" id="message" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Tell the owner about your trip..."></textarea>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="flex justify-between text-sm">
                            <span>Price per day:</span>
                            <span>${{ $car->price_per_day }}</span>
                        </div>
                        <div class="flex justify-between text-sm mt-1">
                            <span>Total days:</span>
                            <span id="totalDays">-</span>
                        </div>
                        <div class="flex justify-between font-medium text-lg mt-2 pt-2 border-t">
                            <span>Total:</span>
                            <span id="totalPrice">$0</span>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex space-x-3">
                    <button type="button" onclick="closeBookingModal()"
                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md">
                        Cancel
                    </button>
                    <button type="submit"
                            class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md">
                        Book Now
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Contact Modal -->
<div id="contactModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Contact Owner</h3>
                <button onclick="closeContactModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="text-center">
                <div class="mb-4">
                    <svg class="mx-auto h-12 w-12 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                </div>
                <p class="text-gray-600 mb-4">Contact the owner directly:</p>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="font-medium text-lg">0788613669</p>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                </div>
                <button onclick="closeContactModal()"
                        class="mt-4 w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function changeMainImage(src) {
    document.getElementById('mainImage').src = src;
}

function openBookingModal() {
    document.getElementById('bookingModal').classList.remove('hidden');
    calculatePrice();
}

function closeBookingModal() {
    document.getElementById('bookingModal').classList.add('hidden');
}

function openContactModal() {
    document.getElementById('contactModal').classList.remove('hidden');
}

function closeContactModal() {
    document.getElementById('contactModal').classList.add('hidden');
}

function calculatePrice() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    const pricePerDay = {{ $car->price_per_day }};

    startDate.addEventListener('change', updatePrice);
    endDate.addEventListener('change', updatePrice);

    function updatePrice() {
        if (startDate.value && endDate.value) {
            const start = new Date(startDate.value);
            const end = new Date(endDate.value);
            const timeDiff = end.getTime() - start.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (daysDiff > 0) {
                document.getElementById('totalDays').textContent = daysDiff;
                document.getElementById('totalPrice').textContent = '$' + (daysDiff * pricePerDay);
            } else {
                document.getElementById('totalDays').textContent = '-';
                document.getElementById('totalPrice').textContent = '$0';
            }
        }
    }
}

// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
    document.getElementById('end_date').min = today;
});
</script>
@endsection
