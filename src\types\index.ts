export interface User {
  id: string;
  email: string;
  name: string;
  role: 'client' | 'owner' | 'driver' | 'admin';
  phoneNumber?: string;
  isPhoneVerified?: boolean;
  licenseImageUrl?: string;
  licenseVerificationStatus?: 'pending' | 'verified' | 'rejected';
  createdAt: string;
}

export interface Car {
  id: string;
  ownerId: string;
  make: string;
  model: string;
  year: number;
  images: string[];
  description: string;
  features: string[];
  location: string;
  pricePerHour: number;
  availabilityNotes: string;
  isActive: boolean;
  createdAt: string;
}

export interface Driver {
  id: string;
  userId: string;
  name: string;
  age: number;
  experience: number; // in years
  profileImage: string;
  licenseNumber: string;
  licenseVerificationStatus: 'pending' | 'verified' | 'rejected';
  location: string;
  pricePerHour: number;
  rating: number;
  reviews: number;
  specialties: string[];
  availabilityNotes: string;
  isAvailable: boolean;
  createdAt: string;
}

export interface Booking {
  id: string;
  userId: string;
  itemType: 'car' | 'driver';
  itemId: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AppState {
  auth: AuthState;
  cars: Car[];
  filteredCars: Car[];
  selectedCar: Car | null;
  drivers: Driver[];
  filteredDrivers: Driver[];
  selectedDriver: Driver | null;
  bookings: Booking[];
  isLoading: boolean;
  error: string | null;
}

// Filter interfaces
export interface CarFilters {
  search?: string;
  location?: string;
  min_price?: number;
  max_price?: number;
  make?: string;
  transmission?: 'automatic' | 'manual';
  fuel_type?: 'petrol' | 'diesel' | 'hybrid' | 'electric';
  min_year?: number;
  max_year?: number;
  seats?: number;
  features?: string[];
  has_gps?: boolean;
  available_from?: string;
  available_to?: string;
  sort_by?: 'latest' | 'price_low' | 'price_high' | 'year_new' | 'year_old';
  page?: number;
  per_page?: number;
}

export interface DriverFilters {
  search?: string;
  location?: string;
  min_price?: number;
  max_price?: number;
  min_experience?: number;
  min_rating?: number;
  specialties?: string[];
  languages?: string[];
  sort_by?: 'rating' | 'price_low' | 'price_high' | 'experience';
  page?: number;
  per_page?: number;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  timestamp: string;
  isRead: boolean;
}