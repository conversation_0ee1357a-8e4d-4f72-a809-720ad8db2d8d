<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Notifications\BookingStatusUpdateNotification;
use Illuminate\Http\Request;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings for owner's cars.
     */
    public function index()
    {
        $ownerId = auth()->id();

        // Get bookings for cars owned by this user
        $bookings = Booking::whereHas('bookable', function($query) use ($ownerId) {
            $query->where('bookable_type', 'App\\Models\\Car')
                  ->whereHas('owner', function($q) use ($ownerId) {
                      $q->where('id', $ownerId);
                  });
        })
        ->with(['bookable', 'user'])
        ->latest()
        ->paginate(10);

        return view('owner.bookings.index', compact('bookings'));
    }

    /**
     * Confirm a booking
     */
    public function confirm(Booking $booking)
    {
        // Ensure this booking is for a car owned by the authenticated user
        if (!$this->isOwnerBooking($booking)) {
            abort(403);
        }

        $oldStatus = $booking->status;
        $booking->update(['status' => 'confirmed']);

        // Send notification to customer
        $booking->load(['user', 'bookable']);
        $booking->user->notify(new BookingStatusUpdateNotification($booking, $oldStatus));

        return redirect()->back()
            ->with('success', 'Booking confirmed successfully! The customer will be notified.');
    }

    /**
     * Reject a booking
     */
    public function reject(Booking $booking)
    {
        // Ensure this booking is for a car owned by the authenticated user
        if (!$this->isOwnerBooking($booking)) {
            abort(403);
        }

        $oldStatus = $booking->status;
        $booking->update(['status' => 'cancelled']);

        // Send notification to customer
        $booking->load(['user', 'bookable']);
        $booking->user->notify(new BookingStatusUpdateNotification($booking, $oldStatus));

        return redirect()->back()
            ->with('success', 'Booking rejected. The customer will be notified.');
    }

    /**
     * Check if the booking belongs to the authenticated owner
     */
    private function isOwnerBooking(Booking $booking)
    {
        if ($booking->bookable_type !== 'App\\Models\\Car') {
            return false;
        }

        $car = Car::find($booking->bookable_id);
        return $car && $car->owner_id === auth()->id();
    }
}
