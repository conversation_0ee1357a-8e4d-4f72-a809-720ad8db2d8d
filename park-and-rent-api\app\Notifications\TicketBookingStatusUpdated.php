<?php

namespace App\Notifications;

use App\Models\TicketBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TicketBookingStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    public $ticketBooking;
    public $oldStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(TicketBooking $ticketBooking, string $oldStatus)
    {
        $this->ticketBooking = $ticketBooking;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $ticketType = ucfirst($this->ticketBooking->ticket_type);
        $statusMessage = $this->getStatusMessage();
        $departureDate = $this->ticketBooking->departure_date->format('M j, Y');

        $mailMessage = (new MailMessage)
            ->subject('Ticket Booking Update - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($statusMessage)
            ->line('**Booking Details:**')
            ->line('• Ticket Type: ' . $ticketType)
            ->line('• Booking ID: #' . $this->ticketBooking->id)
            ->line('• Passenger: ' . $this->ticketBooking->passenger_name)
            ->line('• From: ' . $this->ticketBooking->departure_city . ', ' . $this->ticketBooking->departure_country)
            ->line('• To: ' . $this->ticketBooking->destination_city . ', ' . $this->ticketBooking->destination_country)
            ->line('• Departure Date: ' . $departureDate)
            ->line('• Previous Status: ' . ucfirst($this->oldStatus))
            ->line('• Current Status: ' . ucfirst($this->ticketBooking->status));

        // Add estimated price if available
        if ($this->ticketBooking->estimated_price) {
            $mailMessage->line('• Estimated Price: $' . number_format($this->ticketBooking->estimated_price, 2));
        }

        // Add admin notes if available
        if ($this->ticketBooking->admin_notes) {
            $mailMessage->line('• Notes: ' . $this->ticketBooking->admin_notes);
        }

        $mailMessage->action('View Booking Details', url('/my-ticket-bookings/' . $this->ticketBooking->id))
            ->line('If you have any questions, please contact <NAME_EMAIL> or call 0788613669.')
            ->line('Thank you for choosing Park & Rent!');

        return $mailMessage;
    }

    /**
     * Get status-specific message
     */
    private function getStatusMessage(): string
    {
        switch ($this->ticketBooking->status) {
            case 'contacted':
                return 'Our team has reviewed your ticket booking request and will contact you soon with pricing and availability.';
            case 'confirmed':
                return 'Great news! Your ticket booking has been confirmed. Payment and travel details will be provided separately.';
            case 'cancelled':
                return 'We regret to inform you that your ticket booking request has been cancelled.';
            default:
                return 'Your ticket booking status has been updated.';
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_booking_id' => $this->ticketBooking->id,
            'type' => 'ticket_booking_status_updated',
            'old_status' => $this->oldStatus,
            'new_status' => $this->ticketBooking->status,
            'destination' => $this->ticketBooking->destination_country,
            'message' => 'Your ticket booking status has been updated to ' . $this->ticketBooking->status . '.'
        ];
    }
}
