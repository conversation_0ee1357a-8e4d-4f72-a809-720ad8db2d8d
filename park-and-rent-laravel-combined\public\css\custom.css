/* Custom CSS for Park & Rent - Green Theme */

/* Override primary colors to green */
.bg-primary-50 { background-color: #f0fdf4 !important; }
.bg-primary-100 { background-color: #dcfce7 !important; }
.bg-primary-200 { background-color: #bbf7d0 !important; }
.bg-primary-300 { background-color: #86efac !important; }
.bg-primary-400 { background-color: #4ade80 !important; }
.bg-primary-500 { background-color: #078533 !important; }
.bg-primary-600 { background-color: #16a34a !important; }
.bg-primary-700 { background-color: #15803d !important; }
.bg-primary-800 { background-color: #166534 !important; }
.bg-primary-900 { background-color: #14532d !important; }

.text-primary-50 { color: #f0fdf4 !important; }
.text-primary-100 { color: #dcfce7 !important; }
.text-primary-200 { color: #bbf7d0 !important; }
.text-primary-300 { color: #86efac !important; }
.text-primary-400 { color: #4ade80 !important; }
.text-primary-500 { color: #078533 !important; }
.text-primary-600 { color: #16a34a !important; }
.text-primary-700 { color: #15803d !important; }
.text-primary-800 { color: #166534 !important; }
.text-primary-900 { color: #14532d !important; }

.border-primary-50 { border-color: #f0fdf4 !important; }
.border-primary-100 { border-color: #dcfce7 !important; }
.border-primary-200 { border-color: #bbf7d0 !important; }
.border-primary-300 { border-color: #86efac !important; }
.border-primary-400 { border-color: #4ade80 !important; }
.border-primary-500 { border-color: #078533 !important; }
.border-primary-600 { border-color: #16a34a !important; }
.border-primary-700 { border-color: #15803d !important; }
.border-primary-800 { border-color: #166534 !important; }
.border-primary-900 { border-color: #14532d !important; }

/* Hover states */
.hover\:bg-primary-400:hover { background-color: #4ade80 !important; }
.hover\:bg-primary-500:hover { background-color: #078533 !important; }
.hover\:bg-primary-600:hover { background-color: #16a34a !important; }
.hover\:bg-primary-700:hover { background-color: #15803d !important; }

.hover\:text-primary-600:hover { color: #16a34a !important; }
.hover\:text-primary-700:hover { color: #15803d !important; }

/* Focus states */
.focus\:ring-primary-500:focus { 
    --tw-ring-color: #078533 !important; 
    box-shadow: 0 0 0 3px rgba(7, 133, 51, 0.1) !important;
}
.focus\:border-primary-500:focus { border-color: #078533 !important; }

/* Gradients */
.from-primary-600 { --tw-gradient-from: #16a34a !important; }
.via-primary-700 { --tw-gradient-via: #15803d !important; }
.to-primary-800 { --tw-gradient-to: #166534 !important; }

/* Additional utility classes for better styling */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

/* Custom button styles */
.btn-primary {
    background-color: #16a34a !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: background-color 0.2s !important;
}

.btn-primary:hover {
    background-color: #15803d !important;
}

/* Custom card styles */
.card-hover {
    transition: box-shadow 0.2s ease-in-out !important;
}

.card-hover:hover {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}
