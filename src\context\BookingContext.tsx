import React, { createContext, useContext, useState, useEffect } from 'react';
import { Booking } from '../types';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface BookingContextType {
  bookings: Booking[];
  userBookings: Booking[];
  isLoading: boolean;
  error: string | null;
  fetchBookings: () => Promise<void>;
  getBookingById: (id: string) => Promise<Booking | null>;
  createBooking: (booking: Omit<Booking, 'id' | 'createdAt'>) => Promise<Booking>;
  updateBookingStatus: (id: string, status: Booking['status']) => Promise<Booking>;
  cancelBooking: (id: string) => Promise<Booking>;
  getUserBookings: (userId: string) => Booking[];
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export const useBookings = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBookings must be used within a BookingProvider');
  }
  return context;
};

export const BookingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [userBookings, setUserBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBookings = async () => {
    // Check if user is authenticated before making the request
    const token = localStorage.getItem('auth_token');
    if (!token) {
      setIsLoading(false);
      setBookings([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(API_ENDPOINTS.MY_BOOKINGS);
      const bookingsData = response.data || [];

      // Convert backend format to frontend format
      const frontendBookings = bookingsData.map((booking: any) => ({
        id: booking.id,
        userId: booking.user_id,
        itemType: booking.item_type,
        itemId: booking.item_id,
        startTime: booking.start_time,
        endTime: booking.end_time,
        totalPrice: booking.total_price,
        status: booking.status,
        notes: booking.notes,
        createdAt: booking.created_at,
      }));

      setBookings(frontendBookings);
    } catch (err) {
      // Only log error if it's not a 401 (unauthorized)
      if (err.response?.status !== 401) {
        setError('Failed to fetch bookings');
        console.error(err);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getBookingById = async (id: string): Promise<Booking | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(`${API_ENDPOINTS.BOOKINGS}/${id}`);
      const booking = response.data.booking || response.data;

      // Convert backend format to frontend format
      const frontendBooking: Booking = {
        id: booking.id,
        userId: booking.user_id,
        itemType: booking.item_type,
        itemId: booking.item_id,
        startTime: booking.start_time,
        endTime: booking.end_time,
        totalPrice: booking.total_price,
        status: booking.status,
        notes: booking.notes,
        createdAt: booking.created_at,
      };

      return frontendBooking;
    } catch (err) {
      setError('Failed to fetch booking');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt'>): Promise<Booking> => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare data for API (convert frontend format to backend format)
      const apiData = {
        item_type: bookingData.itemType,
        item_id: bookingData.itemId,
        start_time: bookingData.startTime,
        end_time: bookingData.endTime,
        notes: bookingData.notes || '',
      };

      const response = await apiClient.post(API_ENDPOINTS.BOOKINGS, apiData);
      const newBooking = response.data.booking || response.data;

      // Convert backend format to frontend format
      const frontendBooking: Booking = {
        id: newBooking.id,
        userId: newBooking.user_id,
        itemType: newBooking.item_type,
        itemId: newBooking.item_id,
        startTime: newBooking.start_time,
        endTime: newBooking.end_time,
        totalPrice: newBooking.total_price,
        status: newBooking.status,
        notes: newBooking.notes,
        createdAt: newBooking.created_at,
      };

      setBookings(prevBookings => [...prevBookings, frontendBooking]);

      return frontendBooking;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to create booking';
      setError(errorMessage);
      console.error('Booking creation error:', err);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const updateBookingStatus = async (id: string, status: Booking['status']): Promise<Booking> => {
    setIsLoading(true);
    setError(null);

    try {
      // Find and update booking
      const bookingIndex = bookings.findIndex(booking => booking.id === id);

      if (bookingIndex === -1) {
        throw new Error('Booking not found');
      }

      const updatedBooking = {
        ...bookings[bookingIndex],
        status,
      };

      const updatedBookings = [...bookings];
      updatedBookings[bookingIndex] = updatedBooking;

      setBookings(updatedBookings);

      // Update user bookings if necessary
      setUserBookings(prevUserBookings => {
        const userBookingIndex = prevUserBookings.findIndex(booking => booking.id === id);
        if (userBookingIndex !== -1) {
          const updatedUserBookings = [...prevUserBookings];
          updatedUserBookings[userBookingIndex] = updatedBooking;
          return updatedUserBookings;
        }
        return prevUserBookings;
      });

      return updatedBooking;
    } catch (err) {
      setError('Failed to update booking status');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const cancelBooking = async (id: string): Promise<Booking> => {
    return updateBookingStatus(id, 'cancelled');
  };

  const getUserBookings = (userId: string): Booking[] => {
    return bookings.filter(booking => booking.userId === userId);
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const value = {
    bookings,
    userBookings,
    isLoading,
    error,
    fetchBookings,
    getBookingById,
    createBooking,
    updateBookingStatus,
    cancelBooking,
    getUserBookings,
  };

  return <BookingContext.Provider value={value}>{children}</BookingContext.Provider>;
};

export default BookingContext;
