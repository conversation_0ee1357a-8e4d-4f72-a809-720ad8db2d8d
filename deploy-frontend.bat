@echo off
echo ========================================
echo   Park & Rent Frontend Deployment Script
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "src" (
    echo ERROR: src directory not found!
    echo Please run this script from the React project root directory.
    pause
    exit /b 1
)

if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Please run this script from the React project root directory.
    pause
    exit /b 1
)

echo Step 1: Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo Step 2: Building React application for production...
call npm run build
if errorlevel 1 (
    echo ERROR: Failed to build React application!
    pause
    exit /b 1
)

echo.
echo Step 3: Creating deployment package...
if not exist "deployment\frontend-upload" mkdir "deployment\frontend-upload"

REM Copy built files
echo Copying built files...
xcopy "dist\*" "deployment\frontend-upload\" /E /Y

REM Create .htaccess for React Router
echo Creating .htaccess for React Router...
(
echo ^<IfModule mod_rewrite.c^>
echo     RewriteEngine On
echo     RewriteBase /
echo.
echo     # Handle Angular and React Router
echo     RewriteRule ^index\.html$ - [L]
echo     RewriteCond %%{REQUEST_FILENAME} !-f
echo     RewriteCond %%{REQUEST_FILENAME} !-d
echo     RewriteRule . /index.html [L]
echo ^</IfModule^>
echo.
echo # Security Headers
echo ^<IfModule mod_headers.c^>
echo     Header always set X-Content-Type-Options nosniff
echo     Header always set X-Frame-Options DENY
echo     Header always set X-XSS-Protection "1; mode=block"
echo     Header always set Referrer-Policy "strict-origin-when-cross-origin"
echo ^</IfModule^>
echo.
echo # Compression
echo ^<IfModule mod_deflate.c^>
echo     AddOutputFilterByType DEFLATE text/plain
echo     AddOutputFilterByType DEFLATE text/html
echo     AddOutputFilterByType DEFLATE text/xml
echo     AddOutputFilterByType DEFLATE text/css
echo     AddOutputFilterByType DEFLATE application/xml
echo     AddOutputFilterByType DEFLATE application/xhtml+xml
echo     AddOutputFilterByType DEFLATE application/rss+xml
echo     AddOutputFilterByType DEFLATE application/javascript
echo     AddOutputFilterByType DEFLATE application/x-javascript
echo ^</IfModule^>
echo.
echo # Cache Control
echo ^<IfModule mod_expires.c^>
echo     ExpiresActive on
echo     ExpiresByType text/css "access plus 1 year"
echo     ExpiresByType application/javascript "access plus 1 year"
echo     ExpiresByType image/png "access plus 1 year"
echo     ExpiresByType image/jpg "access plus 1 year"
echo     ExpiresByType image/jpeg "access plus 1 year"
echo     ExpiresByType image/gif "access plus 1 year"
echo     ExpiresByType image/svg+xml "access plus 1 year"
echo ^</IfModule^>
) > "deployment\frontend-upload\.htaccess"

echo.
echo Step 4: Creating upload instructions...
(
echo ========================================
echo   FRONTEND UPLOAD INSTRUCTIONS
echo ========================================
echo.
echo 1. Upload ALL files from 'deployment\frontend-upload\' to:
echo    public_html/ ^(root directory, NOT in a subfolder^)
echo.
echo 2. Make sure these files are in public_html/:
echo    - index.html
echo    - .htaccess
echo    - assets/ folder
echo    - All other built files
echo.
echo 3. DO NOT upload to public_html/api/ - that's for the Laravel API
echo.
echo 4. After upload, your structure should be:
echo    public_html/
echo    ├── index.html              ^(React app^)
echo    ├── .htaccess               ^(React routing^)
echo    ├── assets/                 ^(React assets^)
echo    ├── api/                    ^(Laravel API - separate upload^)
echo    │   ├── index.php
echo    │   └── .htaccess
echo    ├── app/                    ^(Laravel files^)
echo    ├── bootstrap/
echo    └── ^(other Laravel folders^)
echo.
echo 5. Test the frontend:
echo    Visit: https://ebisera.com
echo    Should load the Park ^& Rent React application
echo.
echo 6. Test API connection:
echo    The React app should be able to connect to https://ebisera.com/api/api
echo.
echo ========================================
) > "deployment\frontend-upload\UPLOAD_INSTRUCTIONS.txt"

echo.
echo ========================================
echo   FRONTEND DEPLOYMENT COMPLETE!
echo ========================================
echo.
echo Files are ready in: deployment\frontend-upload\
echo.
echo Next steps:
echo 1. Read the UPLOAD_INSTRUCTIONS.txt file
echo 2. Upload files to public_html/ ^(root directory^)
echo 3. Test at https://ebisera.com
echo.
pause
