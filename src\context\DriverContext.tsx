import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Driver } from '../types';
import { mockDrivers } from '../data/mockData';
import axios from 'axios';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface DriverContextType {
  drivers: Driver[];
  filteredDrivers: Driver[];
  selectedDriver: Driver | null;
  isLoading: boolean;
  error: string | null;
  fetchDrivers: () => Promise<void>;
  getDriverById: (id: string) => Promise<Driver | null>;
  setSelectedDriver: (driver: Driver | null) => void;
  addDriver: (driver: Omit<Driver, 'id' | 'createdAt'>) => Promise<Driver>;
  updateDriver: (id: string, driver: Partial<Driver>) => Promise<Driver>;
  deleteDriver: (id: string) => Promise<void>;
  filterDrivers: (query: string) => void;
  toggleDriverAvailability: (id: string) => Promise<Driver>;
}

const DriverContext = createContext<DriverContextType | undefined>(undefined);

export const useDrivers = () => {
  const context = useContext(DriverContext);
  if (!context) {
    throw new Error('useDrivers must be used within a DriverProvider');
  }
  return context;
};

export const DriverProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>([]);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasInitialized, setHasInitialized] = useState(false);

  const fetchDrivers = useCallback(async () => {
    console.log('DriverContext: Starting to fetch drivers...');
    setIsLoading(true);
    setError(null);

    try {
      // Make the API request to get drivers
      console.log('DriverContext: Making API request to', API_ENDPOINTS.DRIVERS);
      const response = await apiClient.get(API_ENDPOINTS.DRIVERS);
      console.log('DriverContext: API response received:', response.status, response.data);

      // If successful, the API will return the drivers data
      if (response.data && Array.isArray(response.data)) {
        // Transform the API response to match our Driver type
        const fetchedDrivers: Driver[] = response.data.map((driver: any) => ({
          id: driver.id.toString(),
          userId: driver.user_id.toString(),
          name: driver.name,
          age: driver.age,
          experience: driver.experience,
          profileImage: driver.profile_image || '',
          licenseNumber: driver.license_number,
          licenseVerificationStatus: driver.license_verification_status,
          location: driver.location,
          pricePerHour: driver.price_per_hour,
          rating: Number(driver.rating || 0),
          reviews: Number(driver.reviews || 0),
          specialties: driver.specialties || [],
          availabilityNotes: driver.availability_notes || '',
          isAvailable: driver.is_available,
          createdAt: driver.created_at,
        }));

        console.log('DriverContext: Successfully transformed', fetchedDrivers.length, 'drivers');
        setDrivers(fetchedDrivers);
        setFilteredDrivers(fetchedDrivers);
        setHasInitialized(true);
      } else {
        // API didn't return expected format
        console.warn('API did not return expected data format for drivers');
        setDrivers([]);
        setFilteredDrivers([]);
        setError('API did not return expected data format');
      }
    } catch (err) {
      console.error('Error fetching drivers from API:', err);
      // Clear drivers on error
      setDrivers([]);
      setFilteredDrivers([]);
      setError('Failed to fetch drivers from server');
    } finally {
      setIsLoading(false);
      setHasInitialized(true);
    }
  }, []);

  const getDriverById = async (id: string): Promise<Driver | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // First try to find in existing drivers array
      let driver = drivers.find(driver => driver.id === id) || null;

      // If not found in local array, try to fetch from API
      if (!driver) {
        try {
          const response = await apiClient.get(`/drivers/${id}`);
          if (response.data) {
            // Transform the API response to match our Driver type
            driver = {
              id: response.data.id.toString(),
              userId: response.data.user_id.toString(),
              name: response.data.name,
              age: response.data.age,
              experience: response.data.experience,
              profileImage: response.data.profile_image || '',
              licenseNumber: response.data.license_number,
              licenseVerificationStatus: response.data.license_verification_status,
              location: response.data.location,
              pricePerHour: response.data.price_per_hour,
              rating: Number(response.data.rating || 0),
              reviews: Number(response.data.reviews || 0),
              specialties: response.data.specialties || [],
              availabilityNotes: response.data.availability_notes || '',
              isAvailable: response.data.is_available,
              isActive: response.data.is_active,
              createdAt: response.data.created_at,
            };
          }
        } catch (apiError) {
          console.error('Error fetching driver from API:', apiError);
          // Fall back to mock data if API fails
          driver = mockDrivers.find(driver => driver.id === id) || null;
        }
      }

      setSelectedDriver(driver);
      return driver;
    } catch (err) {
      setError('Failed to fetch driver');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const addDriver = async (driverData: Omit<Driver, 'id' | 'createdAt'>, profileImageFile?: File): Promise<Driver> => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare FormData for API request
      const formData = new FormData();
      formData.append('name', driverData.name);
      formData.append('age', driverData.age.toString());
      formData.append('experience', driverData.experience.toString());
      formData.append('license_number', driverData.licenseNumber);
      formData.append('location', driverData.location);
      formData.append('price_per_hour', driverData.pricePerHour.toString());
      formData.append('specialties', JSON.stringify(driverData.specialties));
      if (driverData.availabilityNotes) {
        formData.append('availability_notes', driverData.availabilityNotes);
      }

      // Add profile image if provided
      if (profileImageFile) {
        formData.append('profile_image', profileImageFile);
      }

      // Get auth token for explicit header
      const authToken = localStorage.getItem('auth_token');

      // Make the API request
      const response = await apiClient.post(API_ENDPOINTS.DRIVERS, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${authToken}`,
        }
      });

      // Transform the API response to match our Driver type
      const newDriver: Driver = {
        id: response.data.driver.id.toString(),
        userId: response.data.driver.user_id.toString(),
        name: response.data.driver.name,
        age: response.data.driver.age,
        experience: response.data.driver.experience,
        profileImage: response.data.driver.profile_image || '',
        licenseNumber: response.data.driver.license_number,
        licenseVerificationStatus: response.data.driver.license_verification_status,
        location: response.data.driver.location,
        pricePerHour: response.data.driver.price_per_hour,
        rating: Number(response.data.driver.rating || 0),
        reviews: Number(response.data.driver.reviews || 0),
        specialties: response.data.driver.specialties || [],
        availabilityNotes: response.data.driver.availability_notes || '',
        isAvailable: response.data.driver.is_available,
        createdAt: response.data.driver.created_at,
      };

      setDrivers(prevDrivers => [...prevDrivers, newDriver]);
      setFilteredDrivers(prevDrivers => [...prevDrivers, newDriver]);
      return newDriver;
    } catch (err) {
      // Handle API errors
      console.error('Add driver error:', err);
      if (axios.isAxiosError(err) && err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        if (err.response.data && err.response.data.errors) {
          const errorMessages = Object.values(err.response.data.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else if (err.response.data && err.response.data.message) {
          setError(err.response.data.message);
        } else {
          setError(`Failed to add driver: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'Failed to add driver');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateDriver = async (id: string, driverData: Partial<Driver>): Promise<Driver> => {
    setIsLoading(true);
    setError(null);

    try {
      // Find and update driver
      const driverIndex = drivers.findIndex(driver => driver.id === id);

      if (driverIndex === -1) {
        throw new Error('Driver not found');
      }

      const updatedDriver = {
        ...drivers[driverIndex],
        ...driverData,
      };

      const updatedDrivers = [...drivers];
      updatedDrivers[driverIndex] = updatedDriver;

      setDrivers(updatedDrivers);
      setFilteredDrivers(updatedDrivers);

      if (selectedDriver?.id === id) {
        setSelectedDriver(updatedDriver);
      }

      return updatedDriver;
    } catch (err) {
      setError('Failed to update driver');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDriver = async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Filter out the driver with the given ID
      const updatedDrivers = drivers.filter(driver => driver.id !== id);

      setDrivers(updatedDrivers);
      setFilteredDrivers(updatedDrivers);

      if (selectedDriver?.id === id) {
        setSelectedDriver(null);
      }
    } catch (err) {
      setError('Failed to delete driver');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const filterDrivers = useCallback((query: string) => {
    if (!query.trim()) {
      setFilteredDrivers(drivers);
      return;
    }

    const lowerQuery = query.toLowerCase();
    const filtered = drivers.filter(driver =>
      driver.name.toLowerCase().includes(lowerQuery) ||
      driver.location.toLowerCase().includes(lowerQuery) ||
      driver.specialties.some(specialty => specialty.toLowerCase().includes(lowerQuery))
    );

    setFilteredDrivers(filtered);
  }, [drivers]);

  const toggleDriverAvailability = async (id: string): Promise<Driver> => {
    const driver = drivers.find(d => d.id === id);
    if (!driver) {
      throw new Error('Driver not found');
    }

    return updateDriver(id, { isAvailable: !driver.isAvailable });
  };

  useEffect(() => {
    if (!hasInitialized) {
      fetchDrivers();
    }
  }, [hasInitialized, fetchDrivers]);

  const value = {
    drivers,
    filteredDrivers,
    selectedDriver,
    isLoading,
    error,
    fetchDrivers,
    getDriverById,
    setSelectedDriver,
    addDriver,
    updateDriver,
    deleteDriver,
    filterDrivers,
    toggleDriverAvailability,
  };

  return <DriverContext.Provider value={value}>{children}</DriverContext.Provider>;
};

export default DriverContext;
