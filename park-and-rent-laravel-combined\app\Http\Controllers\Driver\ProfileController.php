<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    /**
     * Show the driver profile creation form.
     */
    public function create()
    {
        $driver = Driver::where('user_id', auth()->id())->first();
        
        if ($driver) {
            return redirect()->route('driver.profile.edit');
        }

        return view('driver.profile.create');
    }

    /**
     * Store a newly created driver profile.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:18|max:80',
            'location' => 'required|string|max:255',
            'experience' => 'required|integer|min:0|max:50',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'nullable|numeric|min:0',
            'bio' => 'nullable|string|max:1000',
            'specialties' => 'nullable|array',
            'specialties.*' => 'string|max:255',
            'languages' => 'nullable|array',
            'languages.*' => 'string|max:255',
            'license_number' => 'required|string|max:255',
            'license_expiry' => 'required|date|after:today',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'license_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'is_available' => 'boolean',
        ]);

        $driverData = $request->except(['profile_image', 'license_document', 'id_document']);
        $driverData['user_id'] = auth()->id();
        $driverData['is_available'] = $request->has('is_available');
        $driverData['license_verification_status'] = 'pending';
        $driverData['rating'] = 5.0; // Default rating
        $driverData['reviews'] = 0;

        // Handle file uploads
        if ($request->hasFile('profile_image')) {
            $path = $request->file('profile_image')->store('profiles', 'public');
            $driverData['profile_image'] = $path;
        }

        if ($request->hasFile('license_document')) {
            $path = $request->file('license_document')->store('driver-documents', 'public');
            $driverData['license_document'] = $path;
        }

        if ($request->hasFile('id_document')) {
            $path = $request->file('id_document')->store('driver-documents', 'public');
            $driverData['id_document'] = $path;
        }

        Driver::create($driverData);

        return redirect()->route('driver.dashboard')
            ->with('success', 'Driver profile created successfully! Your license verification is pending.');
    }

    /**
     * Show the driver profile edit form.
     */
    public function edit()
    {
        $driver = Driver::where('user_id', auth()->id())->firstOrFail();
        return view('driver.profile.edit', compact('driver'));
    }

    /**
     * Update the driver profile.
     */
    public function update(Request $request)
    {
        $driver = Driver::where('user_id', auth()->id())->firstOrFail();

        $request->validate([
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:18|max:80',
            'location' => 'required|string|max:255',
            'experience' => 'required|integer|min:0|max:50',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'nullable|numeric|min:0',
            'bio' => 'nullable|string|max:1000',
            'specialties' => 'nullable|array',
            'specialties.*' => 'string|max:255',
            'languages' => 'nullable|array',
            'languages.*' => 'string|max:255',
            'license_number' => 'required|string|max:255',
            'license_expiry' => 'required|date|after:today',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'license_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'is_available' => 'boolean',
        ]);

        $driverData = $request->except(['profile_image', 'license_document', 'id_document']);
        $driverData['is_available'] = $request->has('is_available');

        // Handle file uploads
        if ($request->hasFile('profile_image')) {
            // Delete old image
            if ($driver->profile_image) {
                Storage::disk('public')->delete($driver->profile_image);
            }
            $path = $request->file('profile_image')->store('profiles', 'public');
            $driverData['profile_image'] = $path;
        }

        if ($request->hasFile('license_document')) {
            // Delete old document
            if ($driver->license_document) {
                Storage::disk('public')->delete($driver->license_document);
            }
            $path = $request->file('license_document')->store('driver-documents', 'public');
            $driverData['license_document'] = $path;
            // Reset verification status if license document is updated
            $driverData['license_verification_status'] = 'pending';
        }

        if ($request->hasFile('id_document')) {
            // Delete old document
            if ($driver->id_document) {
                Storage::disk('public')->delete($driver->id_document);
            }
            $path = $request->file('id_document')->store('driver-documents', 'public');
            $driverData['id_document'] = $path;
        }

        $driver->update($driverData);

        return redirect()->route('driver.dashboard')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Toggle driver availability.
     */
    public function toggleAvailability()
    {
        $driver = Driver::where('user_id', auth()->id())->firstOrFail();
        
        $driver->update(['is_available' => !$driver->is_available]);

        $status = $driver->is_available ? 'available' : 'unavailable';
        return redirect()->back()
            ->with('success', "You are now {$status} for bookings.");
    }
}
