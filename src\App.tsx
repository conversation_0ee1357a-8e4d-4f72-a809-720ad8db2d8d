import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

import { AuthProvider } from './context/AuthContext';
import { CarProvider } from './context/CarContext';
import { DriverProvider } from './context/DriverContext';
import { BookingProvider } from './context/BookingContext';
import { AdminProvider } from './context/AdminContext';

// Pages
import HomePage from './pages/HomePage';
import CarsListPage from './pages/CarsListPage';
import CarDetailPage from './pages/CarDetailPage';
import DriversListPage from './pages/DriversListPage';
import DriverDetailPage from './pages/DriverDetailPage';
import DriverRegistrationPage from './pages/driver/DriverRegistrationPage';
import DriverDashboardPage from './pages/driver/DriverDashboardPage';
import ClientDashboardPage from './pages/client/ClientDashboardPage';
import AdminDashboardPage from './pages/admin/AdminDashboardPage';
import LoginPage from './pages/auth/LoginPage';
import SignupPage from './pages/auth/SignupPage';
import AccountVerificationPage from './pages/auth/AccountVerificationPage';
import OwnerDashboardPage from './pages/owner/OwnerDashboardPage';
import OwnerMessagesPage from './pages/owner/OwnerMessagesPage';
import OwnerBookingsPage from './pages/owner/OwnerBookingsPage';
import AddCarPage from './pages/owner/AddCarPage';
import EditCarPage from './pages/owner/EditCarPage';
import GPSRequestPage from './pages/owner/GPSRequestPage';
import EditProfilePage from './pages/client/EditProfilePage';
import TicketBookingPage from './pages/TicketBookingPage';
import MyTicketBookingsPage from './pages/MyTicketBookingsPage';

// Protected route component
const ProtectedRoute: React.FC<{
  element: React.ReactNode;
  isAuthenticated: boolean;
  redirectPath?: string;
}> = ({ element, isAuthenticated, redirectPath = '/login' }) => {
  return isAuthenticated ? (
    <>{element}</>
  ) : (
    <Navigate to={redirectPath} replace />
  );
};

function App() {
  // Title update
  useEffect(() => {
    document.title = 'Park & Rent - Peer-to-Peer Car Rentals';
  }, []);

  return (
    <AuthProvider>
      <CarProvider>
        <DriverProvider>
          <BookingProvider>
            <AdminProvider>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/cars" element={<CarsListPage />} />
              <Route path="/cars/:id" element={<CarDetailPage />} />
              <Route path="/drivers" element={<DriversListPage />} />
              <Route path="/drivers/:id" element={<DriverDetailPage />} />
              <Route path="/ticket-booking" element={<TicketBookingPage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />

              {/* Protected routes */}
              <Route
                path="/account/verification"
                element={
                  <ProtectedRoute
                    element={<AccountVerificationPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner"
                element={
                  <ProtectedRoute
                    element={<OwnerDashboardPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/dashboard"
                element={
                  <ProtectedRoute
                    element={<OwnerDashboardPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/add-car"
                element={
                  <ProtectedRoute
                    element={<AddCarPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/edit-car/:id"
                element={
                  <ProtectedRoute
                    element={<EditCarPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/gps-request"
                element={
                  <ProtectedRoute
                    element={<GPSRequestPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/messages"
                element={
                  <ProtectedRoute
                    element={<OwnerMessagesPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/owner/bookings"
                element={
                  <ProtectedRoute
                    element={<OwnerBookingsPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/driver/register"
                element={
                  <ProtectedRoute
                    element={<DriverRegistrationPage />}
                    isAuthenticated={true} // In a real app, use auth context here
                  />
                }
              />
              <Route
                path="/driver"
                element={
                  <ProtectedRoute
                    element={<DriverDashboardPage />}
                    isAuthenticated={true}
                  />
                }
              />
              <Route
                path="/driver/dashboard"
                element={
                  <ProtectedRoute
                    element={<DriverDashboardPage />}
                    isAuthenticated={true}
                  />
                }
              />
              <Route
                path="/account"
                element={
                  <ProtectedRoute
                    element={<ClientDashboardPage />}
                    isAuthenticated={true}
                  />
                }
              />
              <Route
                path="/account/edit-profile"
                element={
                  <ProtectedRoute
                    element={<EditProfilePage />}
                    isAuthenticated={true}
                  />
                }
              />
              <Route
                path="/admin"
                element={
                  <ProtectedRoute
                    element={<AdminDashboardPage />}
                    isAuthenticated={true}
                  />
                }
              />
              <Route
                path="/admin/dashboard"
                element={
                  <ProtectedRoute
                    element={<AdminDashboardPage />}
                    isAuthenticated={true}
                  />
                }
              />

              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            </AdminProvider>
          </BookingProvider>
        </DriverProvider>
      </CarProvider>
    </AuthProvider>
  );
}

export default App;