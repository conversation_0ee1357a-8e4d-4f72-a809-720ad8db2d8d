<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\JsonResponse;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'Park & Rent API is running',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Cars
    Route::get('/cars', [\App\Http\Controllers\Api\CarController::class, 'index']);
    Route::get('/cars/{car}', [\App\Http\Controllers\Api\CarController::class, 'show']);
    
    // Drivers
    Route::get('/drivers', [\App\Http\Controllers\Api\DriverController::class, 'index']);
    Route::get('/drivers/{driver}', [\App\Http\Controllers\Api\DriverController::class, 'show']);
    
    // Authentication
    Route::post('/register', [\App\Http\Controllers\Api\AuthController::class, 'register']);
    Route::post('/login', [\App\Http\Controllers\Api\AuthController::class, 'login']);
});

// Protected routes (authentication required)
Route::middleware('auth:sanctum')->prefix('v1')->group(function () {
    // User
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    Route::post('/logout', [\App\Http\Controllers\Api\AuthController::class, 'logout']);
    
    // Bookings
    Route::apiResource('bookings', \App\Http\Controllers\Api\BookingController::class);
    Route::get('/my-bookings', [\App\Http\Controllers\Api\BookingController::class, 'myBookings']);
    
    // Cars (owner operations)
    Route::apiResource('my-cars', \App\Http\Controllers\Api\CarController::class)->except(['index', 'show']);
    
    // Driver profile
    Route::get('/my-driver-profile', [\App\Http\Controllers\Api\DriverController::class, 'myProfile']);
    Route::post('/my-driver-profile', [\App\Http\Controllers\Api\DriverController::class, 'updateProfile']);
    
    // Chat and Messages
    Route::apiResource('chats', \App\Http\Controllers\Api\ChatController::class);
    Route::get('/my-chats', [\App\Http\Controllers\Api\ChatController::class, 'myChats']);
    Route::get('/unread-messages-count', [\App\Http\Controllers\Api\ChatController::class, 'unreadCount']);
    
    Route::apiResource('messages', \App\Http\Controllers\Api\MessageController::class);
    Route::post('/messages/mark-as-read', [\App\Http\Controllers\Api\MessageController::class, 'markAsRead']);
    
    // Owner Dashboard
    Route::prefix('owner')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\Api\OwnerController::class, 'dashboard']);
        Route::get('/bookings', [\App\Http\Controllers\Api\OwnerController::class, 'bookings']);
        Route::get('/messages', [\App\Http\Controllers\Api\OwnerController::class, 'messages']);
        Route::get('/chats', [\App\Http\Controllers\Api\OwnerController::class, 'chats']);
    });
    
    // Admin routes
    Route::middleware('role:admin')->prefix('admin')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\Api\AdminController::class, 'dashboard']);
        Route::apiResource('users', \App\Http\Controllers\Api\AdminController::class);
        Route::apiResource('cars', \App\Http\Controllers\Api\AdminController::class);
        Route::apiResource('drivers', \App\Http\Controllers\Api\AdminController::class);
        Route::apiResource('bookings', \App\Http\Controllers\Api\AdminController::class);
        Route::get('/revenue', [\App\Http\Controllers\Api\AdminController::class, 'revenue']);
    });
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'error' => 'API endpoint not found',
        'message' => 'The requested API endpoint does not exist.',
        'available_endpoints' => [
            'GET /api/health' => 'Health check',
            'GET /api/v1/cars' => 'List cars',
            'GET /api/v1/drivers' => 'List drivers',
            'POST /api/v1/register' => 'User registration',
            'POST /api/v1/login' => 'User login',
        ]
    ], 404);
});
