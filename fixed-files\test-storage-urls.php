<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING STORAGE URL CONFIGURATION ===\n\n";

try {
    // Check app configuration
    echo "APP_URL: " . config('app.url') . "\n";
    echo "FILESYSTEM_DISK: " . config('filesystems.default') . "\n";
    echo "Storage URL config: " . config('filesystems.disks.public.url') . "\n";
    echo "Asset URL: " . config('app.asset_url') . "\n\n";
    
    // Test URL generation
    echo "=== URL GENERATION TESTS ===\n";
    
    $testFiles = [
        'car-images/test.jpg',
        'car-images/gs9azPWSBKYnJ28bAVkYAkhYBcWUVDwU8AFlFBAW.jpg',
        'driver-profiles/test-profile.jpg'
    ];
    
    foreach ($testFiles as $file) {
        $url = Storage::disk('public')->url($file);
        echo "File: {$file}\n";
        echo "Generated URL: {$url}\n";
        echo "Expected: https://ebisera.com/api/public/storage/{$file}\n";
        echo "Match: " . ($url === "https://ebisera.com/api/public/storage/{$file}" ? "✅ YES" : "❌ NO") . "\n\n";
    }
    
    // Check storage structure
    echo "=== STORAGE STRUCTURE ===\n";
    
    $storagePath = storage_path('app/public');
    echo "Storage path: {$storagePath}\n";
    echo "Storage exists: " . (is_dir($storagePath) ? "✅ YES" : "❌ NO") . "\n";
    
    $carImagesPath = storage_path('app/public/car-images');
    echo "Car images path: {$carImagesPath}\n";
    echo "Car images exists: " . (is_dir($carImagesPath) ? "✅ YES" : "❌ NO") . "\n";
    
    if (is_dir($carImagesPath)) {
        $images = glob($carImagesPath . '/*');
        echo "Images found: " . count($images) . "\n";
        
        foreach (array_slice($images, 0, 5) as $image) {
            echo "  - " . basename($image) . "\n";
        }
        if (count($images) > 5) {
            echo "  ... and " . (count($images) - 5) . " more\n";
        }
    }
    
    echo "\n=== STORAGE LINK CHECK ===\n";
    $publicStoragePath = public_path('storage');
    echo "Public storage path: {$publicStoragePath}\n";
    echo "Link exists: " . (file_exists($publicStoragePath) ? "✅ YES" : "❌ NO") . "\n";
    
    if (is_link($publicStoragePath)) {
        echo "Link target: " . readlink($publicStoragePath) . "\n";
        echo "Link valid: " . (is_dir($publicStoragePath) ? "✅ YES" : "❌ NO") . "\n";
    }
    
    echo "\n🎯 Configuration test complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
