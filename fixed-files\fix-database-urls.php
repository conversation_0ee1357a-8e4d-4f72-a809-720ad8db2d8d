<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== FIXING ALL IMAGE URLS IN DATABASE ===\n\n";

try {
    // Fix each car individually with correct URLs
    $updates = [
        4 => [
            'https://ebisera.com/api/public/storage/car-images/5mxxtaamvWv470rLJfZaOetGJQM6C3FzIZiWsroH.jpg',
            'https://ebisera.com/api/public/storage/car-images/7MCfXpG7rOgQHMFDAJoC5lkcitPIyphno4ti0Rsq.jpg'
        ],
        5 => [
            'https://ebisera.com/api/public/storage/car-images/ALlIcZARbwr82F9ngPGkVTG4hLZRcsg61omtBRH2.jpg',
            'https://ebisera.com/api/public/storage/car-images/yFJwrE37xXpUReVJmSBIWLrng8rXig3SYuRSUkP3.jpg'
        ],
        8 => [
            'https://ebisera.com/api/public/storage/car-images/gs9azPWSBKYnJ28bAVkYAkhYBcWUVDwU8AFlFBAW.jpg',
            'https://ebisera.com/api/public/storage/car-images/vv7MoTHvxcCWhFduxay26zb0iCQ11mH7xiLimoLG.jpg'
        ],
        9 => [
            'https://ebisera.com/api/public/storage/car-images/mVXxK7klKfo53JExpTlK4Flae7F1k3aPsR2aUwsu.jpg'
        ],
        10 => [
            'https://ebisera.com/api/public/storage/car-images/UzzlqayGgV6XEHFiB1WOXvkZMS9evxx5rGTjDF2O.jpg',
            'https://ebisera.com/api/public/storage/car-images/CKekC7t4BV0A9q5xmyKHupFKXBoCDGngaI7IBXp1.png'
        ]
    ];
    
    foreach ($updates as $carId => $images) {
        $jsonImages = json_encode($images);
        
        DB::table('cars')
            ->where('id', $carId)
            ->update(['images' => $jsonImages]);
        
        echo "✅ Updated car ID {$carId} with " . count($images) . " images\n";
        foreach ($images as $image) {
            echo "   - " . basename($image) . "\n";
        }
        echo "\n";
    }
    
    // Clear placeholder data
    DB::table('cars')
        ->whereIn('id', [1, 2, 3])
        ->update(['images' => '[]']);
    
    echo "✅ Cleared placeholder data for cars 1, 2, 3\n\n";
    
    // Show final results
    echo "=== FINAL DATABASE STATE ===\n";
    $cars = DB::table('cars')->select('id', 'make', 'model', 'images')->get();
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        $imageCount = is_array($images) ? count($images) : 0;
        echo "Car {$car->id}: {$car->make} {$car->model} - {$imageCount} images\n";
        
        if ($imageCount > 0) {
            foreach ($images as $image) {
                echo "  ✓ " . $image . "\n";
            }
        }
        echo "\n";
    }
    
    echo "🎉 All image URLs fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
