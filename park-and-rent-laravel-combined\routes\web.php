<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CarController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Client\ClientDashboardController;
// use App\Http\Controllers\Owner\OwnerDashboardController;
// use App\Http\Controllers\Driver\DriverDashboardController;
// use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\ProfileController;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Test route
Route::get('/test', function () {
    return 'Test route works!';
});

// Cars routes
Route::get('/cars', [CarController::class, 'index'])->name('cars.index');
Route::get('/cars/{car}', [CarController::class, 'show'])->name('cars.show');

// Drivers routes
Route::get('/drivers', [DriverController::class, 'index'])->name('drivers.index');
Route::get('/drivers/{driver}', [DriverController::class, 'show'])->name('drivers.show');

// Authentication routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Booking routes
    Route::resource('bookings', BookingController::class);
    Route::get('/my-bookings', [BookingController::class, 'myBookings'])->name('bookings.my');

    // Client dashboard
    Route::middleware('role:client')->group(function () {
        Route::get('/client/dashboard', [ClientDashboardController::class, 'index'])->name('client.dashboard');
    });

    // TODO: Add dashboard routes after creating controllers
    // Owner dashboard
    // Route::middleware('role:owner')->group(function () {
    //     Route::prefix('owner')->name('owner.')->group(function () {
    //         Route::get('/dashboard', [OwnerDashboardController::class, 'index'])->name('dashboard');
    //     });
    // });

    // Driver dashboard
    // Route::middleware('role:driver')->group(function () {
    //     Route::prefix('driver')->name('driver.')->group(function () {
    //         Route::get('/dashboard', [DriverDashboardController::class, 'index'])->name('dashboard');
    //     });
    // });

    // Admin dashboard
    // Route::middleware('role:admin')->group(function () {
    //     Route::prefix('admin')->name('admin.')->group(function () {
    //         Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    //     });
    // });
});
