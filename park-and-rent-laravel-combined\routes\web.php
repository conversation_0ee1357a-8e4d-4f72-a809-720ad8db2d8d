<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CarController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Cars routes
Route::get('/cars', [CarController::class, 'index'])->name('cars.index');
Route::get('/cars/{car}', [CarController::class, 'show'])->name('cars.show');

// Drivers routes
Route::get('/drivers', [DriverController::class, 'index'])->name('drivers.index');
Route::get('/drivers/{driver}', [DriverController::class, 'show'])->name('drivers.show');

// Authentication routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Booking routes
    Route::resource('bookings', BookingController::class);
    Route::get('/my-bookings', [BookingController::class, 'myBookings'])->name('bookings.my');

    // File uploads
    Route::post('/upload/car-images', [\App\Http\Controllers\FileUploadController::class, 'uploadCarImages'])->name('upload.car-images');
    Route::post('/upload/profile-image', [\App\Http\Controllers\FileUploadController::class, 'uploadProfileImage'])->name('upload.profile-image');
    Route::post('/upload/driver-documents', [\App\Http\Controllers\FileUploadController::class, 'uploadDriverDocuments'])->name('upload.driver-documents');
    Route::delete('/upload/file', [\App\Http\Controllers\FileUploadController::class, 'deleteFile'])->name('upload.delete-file');

    // Chat/Messaging routes
    Route::get('/chat', [\App\Http\Controllers\ChatController::class, 'index'])->name('chat.index');
    Route::get('/chat/{chat}', [\App\Http\Controllers\ChatController::class, 'show'])->name('chat.show');
    Route::post('/chat/start', [\App\Http\Controllers\ChatController::class, 'startChat'])->name('chat.start');
    Route::post('/chat/{chat}/send', [\App\Http\Controllers\ChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::get('/chat/{chat}/messages', [\App\Http\Controllers\ChatController::class, 'getMessages'])->name('chat.get-messages');
    Route::patch('/chat/{chat}/read', [\App\Http\Controllers\ChatController::class, 'markAsRead'])->name('chat.mark-read');
    Route::get('/chat/unread-count', [\App\Http\Controllers\ChatController::class, 'getUnreadCount'])->name('chat.unread-count');
    Route::get('/chat/start-car-owner/{car}', [\App\Http\Controllers\ChatController::class, 'startCarOwnerChat'])->name('chat.start-car-owner');
    Route::get('/chat/start-driver/{driver}', [\App\Http\Controllers\ChatController::class, 'startDriverChat'])->name('chat.start-driver');

    // GPS Installation Requests
    Route::resource('gps-requests', \App\Http\Controllers\GpsInstallationRequestController::class);
    Route::patch('/gps-requests/{gpsRequest}/status', [\App\Http\Controllers\GpsInstallationRequestController::class, 'updateStatus'])->name('gps-requests.update-status');

    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Role-specific dashboard routes
    Route::middleware('role:client')->group(function () {
        Route::get('/client/dashboard', [DashboardController::class, 'index'])->name('client.dashboard');
    });

    Route::middleware('role:owner')->group(function () {
        Route::prefix('owner')->name('owner.')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

            // Car management routes
            Route::resource('cars', \App\Http\Controllers\Owner\CarController::class);
            Route::patch('/cars/{car}/toggle-status', [\App\Http\Controllers\Owner\CarController::class, 'toggleStatus'])->name('cars.toggle-status');

            // Booking management routes
            Route::get('/bookings', [\App\Http\Controllers\Owner\BookingController::class, 'index'])->name('bookings.index');
            Route::patch('/bookings/{booking}/confirm', [\App\Http\Controllers\Owner\BookingController::class, 'confirm'])->name('bookings.confirm');
            Route::patch('/bookings/{booking}/reject', [\App\Http\Controllers\Owner\BookingController::class, 'reject'])->name('bookings.reject');
        });
    });

    Route::middleware('role:driver')->group(function () {
        Route::prefix('driver')->name('driver.')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

            // Profile management
            Route::get('/profile/create', [\App\Http\Controllers\Driver\ProfileController::class, 'create'])->name('profile.create');
            Route::post('/profile', [\App\Http\Controllers\Driver\ProfileController::class, 'store'])->name('profile.store');
            Route::get('/profile/edit', [\App\Http\Controllers\Driver\ProfileController::class, 'edit'])->name('profile.edit');
            Route::put('/profile', [\App\Http\Controllers\Driver\ProfileController::class, 'update'])->name('profile.update');
            Route::patch('/profile/toggle-availability', [\App\Http\Controllers\Driver\ProfileController::class, 'toggleAvailability'])->name('profile.toggle-availability');

            // Booking management
            Route::get('/bookings', [\App\Http\Controllers\Driver\BookingController::class, 'index'])->name('bookings.index');
            Route::patch('/bookings/{booking}/accept', [\App\Http\Controllers\Driver\BookingController::class, 'accept'])->name('bookings.accept');
            Route::patch('/bookings/{booking}/reject', [\App\Http\Controllers\Driver\BookingController::class, 'reject'])->name('bookings.reject');

            // Earnings
            Route::get('/earnings', [\App\Http\Controllers\Driver\EarningsController::class, 'index'])->name('earnings.index');
        });
    });

    Route::middleware('role:admin')->group(function () {
        Route::prefix('admin')->name('admin.')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

            // User management
            Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
            Route::patch('/users/{user}/toggle-verification', [\App\Http\Controllers\Admin\UserController::class, 'toggleVerification'])->name('users.toggle-verification');
            Route::patch('/users/{user}/change-password', [\App\Http\Controllers\Admin\UserController::class, 'changePassword'])->name('users.change-password');

            // Car management
            Route::resource('cars', \App\Http\Controllers\Admin\CarController::class);
            Route::patch('/cars/{car}/toggle-status', [\App\Http\Controllers\Admin\CarController::class, 'toggleStatus'])->name('cars.toggle-status');

            // Driver management
            Route::resource('drivers', \App\Http\Controllers\Admin\DriverController::class);
            Route::patch('/drivers/{driver}/verify-license', [\App\Http\Controllers\Admin\DriverController::class, 'verifyLicense'])->name('drivers.verify-license');
            Route::patch('/drivers/{driver}/toggle-status', [\App\Http\Controllers\Admin\DriverController::class, 'toggleStatus'])->name('drivers.toggle-status');

            // Booking management
            Route::resource('bookings', \App\Http\Controllers\Admin\BookingController::class);

            // Settings
            Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
            Route::put('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
        });
    });


});
