<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingConfirmationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $bookingType = $this->booking->bookable_type === 'App\\Models\\Car' ? 'Car Rental' : 'Driver Service';
        $itemName = $this->booking->bookable_type === 'App\\Models\\Car' 
            ? $this->booking->bookable->make . ' ' . $this->booking->bookable->model
            : 'Driver Service';

        return (new MailMessage)
            ->subject('Booking Confirmation - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your ' . strtolower($bookingType) . ' booking has been confirmed.')
            ->line('**Booking Details:**')
            ->line('• ' . $bookingType . ': ' . $itemName)
            ->line('• Booking ID: #' . $this->booking->id)
            ->line('• Start Date: ' . \Carbon\Carbon::parse($this->booking->start_date)->format('M d, Y'))
            ->line('• End Date: ' . \Carbon\Carbon::parse($this->booking->end_date)->format('M d, Y'))
            ->line('• Total Amount: $' . number_format($this->booking->total_amount, 2))
            ->line('• Status: ' . ucfirst($this->booking->status))
            ->action('View Booking Details', route('bookings.show', $this->booking))
            ->line('If you have any questions, please contact <NAME_EMAIL> or call 0788613669.')
            ->line('Thank you for choosing Park & Rent!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_confirmation',
            'message' => 'Your booking has been confirmed'
        ];
    }
}
