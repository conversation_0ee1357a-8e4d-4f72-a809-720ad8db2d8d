<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== FIXING ALL IMAGES (CARS + DRIVERS) ===\n\n";

try {
    // Fix car images
    echo "🚗 FIXING CAR IMAGES...\n";
    $cars = DB::table('cars')->whereNotNull('images')->where('images', '!=', '[]')->get();
    
    foreach ($cars as $car) {
        $images = json_decode($car->images, true);
        $fixedImages = [];
        $hasChanges = false;
        
        echo "Car ID {$car->id} ({$car->make} {$car->model}):\n";
        
        foreach ($images as $image) {
            $originalImage = $image;
            
            // Extract filename from any URL format
            $filename = basename($image);
            
            // Create correct production URL
            $newImage = 'https://ebisera.com/api/public/storage/car-images/' . $filename;
            
            // Check if image needs fixing
            if ($image !== $newImage) {
                $hasChanges = true;
                echo "  ❌ {$originalImage}\n";
                echo "  ✅ {$newImage}\n";
            } else {
                echo "  ✅ {$image} (already correct)\n";
            }
            
            $fixedImages[] = $newImage;
        }
        
        if ($hasChanges) {
            DB::table('cars')
                ->where('id', $car->id)
                ->update(['images' => json_encode($fixedImages)]);
            echo "  🔄 Updated in database\n";
        }
        echo "\n";
    }
    
    // Fix driver images
    echo "👤 FIXING DRIVER IMAGES...\n";
    $drivers = DB::table('drivers')->whereNotNull('profile_image_url')->get();
    
    foreach ($drivers as $driver) {
        $originalUrl = $driver->profile_image_url;
        
        // Skip if already correct
        if (strpos($originalUrl, 'https://ebisera.com/api/public/storage/') === 0) {
            echo "Driver ID {$driver->id}: ✅ Already correct\n";
            continue;
        }
        
        // Extract filename
        $filename = basename($originalUrl);
        
        // Create correct URL
        $newUrl = 'https://ebisera.com/api/public/storage/driver-profiles/' . $filename;
        
        echo "Driver ID {$driver->id}:\n";
        echo "  ❌ {$originalUrl}\n";
        echo "  ✅ {$newUrl}\n";
        
        // Update database
        DB::table('drivers')
            ->where('id', $driver->id)
            ->update(['profile_image_url' => $newUrl]);
        
        echo "  🔄 Updated in database\n\n";
    }
    
    // Clear example.com placeholder data
    echo "🧹 CLEARING PLACEHOLDER DATA...\n";
    $placeholderCars = DB::table('cars')->where('images', 'LIKE', '%example.com%')->get();
    
    foreach ($placeholderCars as $car) {
        DB::table('cars')->where('id', $car->id)->update(['images' => '[]']);
        echo "Cleared placeholder data for car ID {$car->id}\n";
    }
    
    echo "\n🎉 ALL IMAGE URLS FIXED!\n\n";
    
    // Show summary
    echo "=== SUMMARY ===\n";
    $carsWithImages = DB::table('cars')->where('images', '!=', '[]')->whereNotNull('images')->count();
    $driversWithImages = DB::table('drivers')->whereNotNull('profile_image_url')->count();
    
    echo "Cars with images: {$carsWithImages}\n";
    echo "Drivers with images: {$driversWithImages}\n";
    echo "All URLs now use: https://ebisera.com/api/public/storage/\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
