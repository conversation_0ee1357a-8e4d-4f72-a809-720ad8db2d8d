@echo off
echo ========================================
echo   COMPLETE PARK & RENT DEPLOYMENT
echo ========================================
echo.
echo This script will deploy both:
echo 1. Laravel API Backend (park-and-rent-api)
echo 2. React Frontend
echo.

REM Check prerequisites
echo Checking prerequisites...
if not exist "park-and-rent-api" (
    echo ERROR: park-and-rent-api directory not found!
    echo Please make sure you have the Laravel API project.
    pause
    exit /b 1
)

if not exist "src" (
    echo ERROR: src directory not found!
    echo Please run this script from the React project root.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed!
echo.

REM Clean previous deployment
echo Cleaning previous deployment files...
if exist "deployment\api-upload" rmdir /s /q "deployment\api-upload"
if exist "deployment\frontend-upload" rmdir /s /q "deployment\frontend-upload"

echo.
echo ========================================
echo   STEP 1: DEPLOYING LARAVEL API
echo ========================================
call deploy-api.bat

echo.
echo ========================================
echo   STEP 2: DEPLOYING REACT FRONTEND  
echo ========================================
call deploy-frontend.bat

echo.
echo ========================================
echo   CREATING COMPLETE UPLOAD PACKAGE
echo ========================================

REM Create final upload directory
if not exist "deployment\complete-upload" mkdir "deployment\complete-upload"

echo Combining API and Frontend files...

REM Copy API files (Laravel goes to root, public to api folder)
xcopy "deployment\api-upload\app" "deployment\complete-upload\app\" /E /I /Y
xcopy "deployment\api-upload\bootstrap" "deployment\complete-upload\bootstrap\" /E /I /Y
xcopy "deployment\api-upload\config" "deployment\complete-upload\config\" /E /I /Y
xcopy "deployment\api-upload\database" "deployment\complete-upload\database\" /E /I /Y
xcopy "deployment\api-upload\resources" "deployment\complete-upload\resources\" /E /I /Y
xcopy "deployment\api-upload\routes" "deployment\complete-upload\routes\" /E /I /Y
xcopy "deployment\api-upload\storage" "deployment\complete-upload\storage\" /E /I /Y
if exist "deployment\api-upload\vendor" xcopy "deployment\api-upload\vendor" "deployment\complete-upload\vendor\" /E /I /Y
xcopy "deployment\api-upload\api" "deployment\complete-upload\api\" /E /I /Y
copy "deployment\api-upload\.env" "deployment\complete-upload\.env" /Y
copy "deployment\api-upload\artisan" "deployment\complete-upload\artisan" /Y
copy "deployment\api-upload\composer.json" "deployment\complete-upload\composer.json" /Y
copy "deployment\api-upload\composer.lock" "deployment\complete-upload\composer.lock" /Y

REM Copy Frontend files (to root, but avoid overwriting API files)
echo Copying frontend files...
for %%f in (deployment\frontend-upload\*) do (
    if not "%%~nxf"=="api" (
        if exist "deployment\complete-upload\%%~nxf" (
            echo Skipping %%~nxf ^(API file exists^)
        ) else (
            copy "%%f" "deployment\complete-upload\" /Y
        )
    )
)

REM Copy frontend assets folder
if exist "deployment\frontend-upload\assets" xcopy "deployment\frontend-upload\assets" "deployment\complete-upload\assets\" /E /I /Y

REM Create comprehensive upload instructions
echo Creating final upload instructions...
(
echo ========================================
echo   COMPLETE DEPLOYMENT INSTRUCTIONS
echo ========================================
echo.
echo UPLOAD ALL FILES FROM 'deployment\complete-upload\' TO YOUR HOSTINGER public_html/
echo.
echo Final structure on server:
echo public_html/
echo ├── index.html              ^(React frontend^)
echo ├── .htaccess               ^(React routing^)
echo ├── assets/                 ^(React assets^)
echo ├── api/                    ^(Laravel API public files^)
echo │   ├── index.php
echo │   ├── .htaccess
echo │   └── ^(other Laravel public files^)
echo ├── app/                    ^(Laravel application^)
echo ├── bootstrap/              ^(Laravel bootstrap^)
echo ├── config/                 ^(Laravel config^)
echo ├── database/               ^(Laravel database^)
echo ├── resources/              ^(Laravel resources^)
echo ├── routes/                 ^(Laravel routes^)
echo ├── storage/                ^(Laravel storage^)
echo ├── vendor/                 ^(Laravel dependencies^)
echo ├── .env                    ^(Laravel environment^)
echo ├── artisan                 ^(Laravel CLI^)
echo ├── composer.json
echo └── composer.lock
echo.
echo ========================================
echo   POST-UPLOAD CONFIGURATION
echo ========================================
echo.
echo 1. UPDATE .env FILE:
echo    - DB_DATABASE=park_and_rent
echo    - DB_USERNAME=^(your database username^)
echo    - DB_PASSWORD=^(your database password^)
echo    - Generate APP_KEY: php artisan key:generate
echo.
echo 2. SET PERMISSIONS:
echo    - storage/ folder: 755 or 775
echo    - bootstrap/cache/ folder: 755 or 775
echo.
echo 3. INSTALL DEPENDENCIES ^(if vendor not uploaded^):
echo    composer install --no-dev --optimize-autoloader
echo.
echo 4. RUN DATABASE SETUP:
echo    php artisan migrate
echo    php artisan db:seed
echo.
echo 5. CLEAR CACHES:
echo    php artisan config:cache
echo    php artisan route:cache
echo    php artisan view:cache
echo.
echo ========================================
echo   TESTING
echo ========================================
echo.
echo 1. Test Frontend: https://ebisera.com
echo    Should load Park ^& Rent React application
echo.
echo 2. Test API Health: https://ebisera.com/api/health
echo    Should return: {"status":"ok","message":"Park & Rent API is running"}
echo.
echo 3. Test API Cars: https://ebisera.com/api/api/cars
echo    Should return cars data or empty array
echo.
echo ========================================
echo   TROUBLESHOOTING
echo ========================================
echo.
echo If API returns 500 error:
echo - Check Laravel logs in storage/logs/
echo - Verify database connection in .env
echo - Ensure proper file permissions
echo.
echo If React app shows API errors:
echo - Check browser console for CORS errors
echo - Verify API endpoints are accessible
echo - Check network tab in browser dev tools
echo.
echo ========================================
) > "deployment\complete-upload\COMPLETE_DEPLOYMENT_GUIDE.txt"

echo.
echo ========================================
echo   🎉 DEPLOYMENT PACKAGE READY! 🎉
echo ========================================
echo.
echo ✅ Laravel API prepared
echo ✅ React Frontend built  
echo ✅ Files combined in: deployment\complete-upload\
echo.
echo 📋 Next Steps:
echo 1. Read: deployment\complete-upload\COMPLETE_DEPLOYMENT_GUIDE.txt
echo 2. Upload ALL files to your Hostinger public_html/
echo 3. Configure database settings
echo 4. Test both frontend and API
echo.
echo 🌐 Expected URLs after deployment:
echo   Frontend: https://ebisera.com
echo   API Health: https://ebisera.com/api/health
echo   API Cars: https://ebisera.com/api/api/cars
echo.
pause
