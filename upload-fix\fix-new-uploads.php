<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== FIXING NEW UPLOAD SYSTEM ===\n\n";

try {
    echo "🔧 STEP 1: COPYING ALL MISSING IMAGES...\n";
    
    // Get all images from storage
    $storageCarImages = storage_path('app/public/car-images');
    $publicCarImages = public_path('car-images');
    
    // Ensure public directory exists
    if (!is_dir($publicCarImages)) {
        mkdir($publicCarImages, 0755, true);
        echo "✅ Created public car-images directory\n";
    }
    
    if (is_dir($storageCarImages)) {
        $images = glob($storageCarImages . '/*');
        $copiedCount = 0;
        
        foreach ($images as $imagePath) {
            $filename = basename($imagePath);
            $targetPath = $publicCarImages . '/' . $filename;
            
            if (!file_exists($targetPath)) {
                copy($imagePath, $targetPath);
                chmod($targetPath, 0644);
                echo "✅ Copied: {$filename}\n";
                $copiedCount++;
            }
        }
        
        echo "Copied {$copiedCount} new images\n\n";
    }
    
    echo "🔧 STEP 2: CHECKING SPECIFIC MISSING IMAGES...\n";
    
    $missingImages = [
        '1woQbesqNa0YDkRcueZ8ZeBrVehliHMZM6ufizmB.jpg',
        'Prc1UTR1ykcuKUwBX60TLyPRYoxkZXmgGtTTO3mp.jpg'
    ];
    
    foreach ($missingImages as $filename) {
        $storagePath = $storageCarImages . '/' . $filename;
        $publicPath = $publicCarImages . '/' . $filename;
        
        echo "Checking: {$filename}\n";
        echo "  Storage: " . (file_exists($storagePath) ? "✅ EXISTS" : "❌ MISSING") . "\n";
        echo "  Public: " . (file_exists($publicPath) ? "✅ EXISTS" : "❌ MISSING") . "\n";
        
        if (file_exists($storagePath) && !file_exists($publicPath)) {
            copy($storagePath, $publicPath);
            chmod($publicPath, 0644);
            echo "  ✅ COPIED to public\n";
        }
        echo "\n";
    }
    
    echo "🔧 STEP 3: CHECKING FILESYSTEM CONFIG...\n";
    
    // Check current filesystem config
    $currentDisk = config('filesystems.default');
    echo "Current default disk: {$currentDisk}\n";
    
    if ($currentDisk !== 'public_direct') {
        echo "❌ Filesystem still using '{$currentDisk}' instead of 'public_direct'\n";
        echo "📋 TO FIX: Update .env file with FILESYSTEM_DISK=public_direct\n";
    } else {
        echo "✅ Filesystem correctly configured for direct public access\n";
    }
    
    echo "\n🧪 TESTING IMAGE ACCESS...\n";
    
    foreach ($missingImages as $filename) {
        $url = "https://ebisera.com/api/public/car-images/{$filename}";
        echo "Testing: {$url}\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "  HTTP Status: {$httpCode} " . ($httpCode === 200 ? "✅ OK" : "❌ ERROR") . "\n\n";
    }
    
    echo "🔧 STEP 4: COPYING DRIVER IMAGES TOO...\n";
    
    $storageDriverImages = storage_path('app/public/driver-profiles');
    $publicDriverImages = public_path('driver-profiles');
    
    // Ensure public directory exists
    if (!is_dir($publicDriverImages)) {
        mkdir($publicDriverImages, 0755, true);
        echo "✅ Created public driver-profiles directory\n";
    }
    
    if (is_dir($storageDriverImages)) {
        $driverImages = glob($storageDriverImages . '/*');
        $driverCopiedCount = 0;
        
        foreach ($driverImages as $imagePath) {
            $filename = basename($imagePath);
            $targetPath = $publicDriverImages . '/' . $filename;
            
            if (!file_exists($targetPath)) {
                copy($imagePath, $targetPath);
                chmod($targetPath, 0644);
                echo "✅ Copied driver image: {$filename}\n";
                $driverCopiedCount++;
            }
        }
        
        echo "Copied {$driverCopiedCount} driver images\n\n";
    }
    
    echo "🎯 SUMMARY:\n";
    echo "Total car images in public: " . count(glob($publicCarImages . '/*')) . "\n";
    echo "Total car images in storage: " . count(glob($storageCarImages . '/*')) . "\n";
    echo "Total driver images in public: " . count(glob($publicDriverImages . '/*')) . "\n";
    echo "Total driver images in storage: " . count(glob($storageDriverImages . '/*')) . "\n";
    
    echo "\n✅ ALL IMAGES COPIED SUCCESSFULLY!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
