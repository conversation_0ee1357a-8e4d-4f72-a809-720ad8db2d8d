import{r as e,a as s}from"./vendor-UTipGiiB.js";import{u as t,L as r,a,R as i,b as l,N as n,B as c}from"./router-De09AHy8.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver((e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)})).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}();var o={exports:{}},d={},m=e,x=Symbol.for("react.element"),u=Symbol.for("react.fragment"),h=Object.prototype.hasOwnProperty,p=m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,g={key:!0,ref:!0,__self:!0,__source:!0};function f(e,s,t){var r,a={},i=null,l=null;for(r in void 0!==t&&(i=""+t),void 0!==s.key&&(i=""+s.key),void 0!==s.ref&&(l=s.ref),s)h.call(s,r)&&!g.hasOwnProperty(r)&&(a[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===a[r]&&(a[r]=s[r]);return{$$typeof:x,type:e,key:i,ref:l,props:a,_owner:p.current}}d.Fragment=u,d.jsx=f,d.jsxs=f,o.exports=d;var y,j=o.exports,b=s;function v(e,s){return function(){return e.apply(s,arguments)}}y=b.createRoot,b.hydrateRoot;const{toString:N}=Object.prototype,{getPrototypeOf:w}=Object,{iterator:k,toStringTag:S}=Symbol,C=(e=>s=>{const t=N.call(s);return e[t]||(e[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),A=e=>(e=e.toLowerCase(),s=>C(s)===e),P=e=>s=>typeof s===e,{isArray:E}=Array,_=P("undefined");const z=A("ArrayBuffer");const R=P("string"),T=P("function"),D=P("number"),L=e=>null!==e&&"object"==typeof e,I=e=>{if("object"!==C(e))return!1;const s=w(e);return!(null!==s&&s!==Object.prototype&&null!==Object.getPrototypeOf(s)||S in e||k in e)},O=A("Date"),F=A("File"),q=A("Blob"),U=A("FileList"),B=A("URLSearchParams"),[M,H,$,V]=["ReadableStream","Request","Response","Headers"].map(A);function Y(e,s,{allOwnKeys:t=!1}={}){if(null==e)return;let r,a;if("object"!=typeof e&&(e=[e]),E(e))for(r=0,a=e.length;r<a;r++)s.call(null,e[r],r,e);else{const a=t?Object.getOwnPropertyNames(e):Object.keys(e),i=a.length;let l;for(r=0;r<i;r++)l=a[r],s.call(null,e[l],l,e)}}function W(e,s){s=s.toLowerCase();const t=Object.keys(e);let r,a=t.length;for(;a-- >0;)if(r=t[a],s===r.toLowerCase())return r;return null}const G="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,J=e=>!_(e)&&e!==G;const K=(e=>s=>e&&s instanceof e)("undefined"!=typeof Uint8Array&&w(Uint8Array)),Z=A("HTMLFormElement"),X=(({hasOwnProperty:e})=>(s,t)=>e.call(s,t))(Object.prototype),Q=A("RegExp"),ee=(e,s)=>{const t=Object.getOwnPropertyDescriptors(e),r={};Y(t,((t,a)=>{let i;!1!==(i=s(t,a,e))&&(r[a]=i||t)})),Object.defineProperties(e,r)};const se=A("AsyncFunction"),te=(re="function"==typeof setImmediate,ae=T(G.postMessage),re?setImmediate:ae?(ie=`axios@${Math.random()}`,le=[],G.addEventListener("message",(({source:e,data:s})=>{e===G&&s===ie&&le.length&&le.shift()()}),!1),e=>{le.push(e),G.postMessage(ie,"*")}):e=>setTimeout(e));var re,ae,ie,le;const ne="undefined"!=typeof queueMicrotask?queueMicrotask.bind(G):"undefined"!=typeof process&&process.nextTick||te,ce={isArray:E,isArrayBuffer:z,isBuffer:function(e){return null!==e&&!_(e)&&null!==e.constructor&&!_(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let s;return e&&("function"==typeof FormData&&e instanceof FormData||T(e.append)&&("formdata"===(s=C(e))||"object"===s&&T(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let s;return s="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&z(e.buffer),s},isString:R,isNumber:D,isBoolean:e=>!0===e||!1===e,isObject:L,isPlainObject:I,isReadableStream:M,isRequest:H,isResponse:$,isHeaders:V,isUndefined:_,isDate:O,isFile:F,isBlob:q,isRegExp:Q,isFunction:T,isStream:e=>L(e)&&T(e.pipe),isURLSearchParams:B,isTypedArray:K,isFileList:U,forEach:Y,merge:function e(){const{caseless:s}=J(this)&&this||{},t={},r=(r,a)=>{const i=s&&W(t,a)||a;I(t[i])&&I(r)?t[i]=e(t[i],r):I(r)?t[i]=e({},r):E(r)?t[i]=r.slice():t[i]=r};for(let a=0,i=arguments.length;a<i;a++)arguments[a]&&Y(arguments[a],r);return t},extend:(e,s,t,{allOwnKeys:r}={})=>(Y(s,((s,r)=>{t&&T(s)?e[r]=v(s,t):e[r]=s}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,s,t,r)=>{e.prototype=Object.create(s.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:s.prototype}),t&&Object.assign(e.prototype,t)},toFlatObject:(e,s,t,r)=>{let a,i,l;const n={};if(s=s||{},null==e)return s;do{for(a=Object.getOwnPropertyNames(e),i=a.length;i-- >0;)l=a[i],r&&!r(l,e,s)||n[l]||(s[l]=e[l],n[l]=!0);e=!1!==t&&w(e)}while(e&&(!t||t(e,s))&&e!==Object.prototype);return s},kindOf:C,kindOfTest:A,endsWith:(e,s,t)=>{e=String(e),(void 0===t||t>e.length)&&(t=e.length),t-=s.length;const r=e.indexOf(s,t);return-1!==r&&r===t},toArray:e=>{if(!e)return null;if(E(e))return e;let s=e.length;if(!D(s))return null;const t=new Array(s);for(;s-- >0;)t[s]=e[s];return t},forEachEntry:(e,s)=>{const t=(e&&e[k]).call(e);let r;for(;(r=t.next())&&!r.done;){const t=r.value;s.call(e,t[0],t[1])}},matchAll:(e,s)=>{let t;const r=[];for(;null!==(t=e.exec(s));)r.push(t);return r},isHTMLForm:Z,hasOwnProperty:X,hasOwnProp:X,reduceDescriptors:ee,freezeMethods:e=>{ee(e,((s,t)=>{if(T(e)&&-1!==["arguments","caller","callee"].indexOf(t))return!1;const r=e[t];T(r)&&(s.enumerable=!1,"writable"in s?s.writable=!1:s.set||(s.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")}))}))},toObjectSet:(e,s)=>{const t={},r=e=>{e.forEach((e=>{t[e]=!0}))};return E(e)?r(e):r(String(e).split(s)),t},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,s,t){return s.toUpperCase()+t})),noop:()=>{},toFiniteNumber:(e,s)=>null!=e&&Number.isFinite(e=+e)?e:s,findKey:W,global:G,isContextDefined:J,isSpecCompliantForm:function(e){return!!(e&&T(e.append)&&"FormData"===e[S]&&e[k])},toJSONObject:e=>{const s=new Array(10),t=(e,r)=>{if(L(e)){if(s.indexOf(e)>=0)return;if(!("toJSON"in e)){s[r]=e;const a=E(e)?[]:{};return Y(e,((e,s)=>{const i=t(e,r+1);!_(i)&&(a[s]=i)})),s[r]=void 0,a}}return e};return t(e,0)},isAsyncFn:se,isThenable:e=>e&&(L(e)||T(e))&&T(e.then)&&T(e.catch),setImmediate:te,asap:ne,isIterable:e=>null!=e&&T(e[k])};function oe(e,s,t,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",s&&(this.code=s),t&&(this.config=t),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}ce.inherits(oe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ce.toJSONObject(this.config),code:this.code,status:this.status}}});const de=oe.prototype,me={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{me[e]={value:e}})),Object.defineProperties(oe,me),Object.defineProperty(de,"isAxiosError",{value:!0}),oe.from=(e,s,t,r,a,i)=>{const l=Object.create(de);return ce.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),oe.call(l,e.message,s,t,r,a),l.cause=e,l.name=e.name,i&&Object.assign(l,i),l};function xe(e){return ce.isPlainObject(e)||ce.isArray(e)}function ue(e){return ce.endsWith(e,"[]")?e.slice(0,-2):e}function he(e,s,t){return e?e.concat(s).map((function(e,s){return e=ue(e),!t&&s?"["+e+"]":e})).join(t?".":""):s}const pe=ce.toFlatObject(ce,{},null,(function(e){return/^is[A-Z]/.test(e)}));function ge(e,s,t){if(!ce.isObject(e))throw new TypeError("target must be an object");s=s||new FormData;const r=(t=ce.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,s){return!ce.isUndefined(s[e])}))).metaTokens,a=t.visitor||o,i=t.dots,l=t.indexes,n=(t.Blob||"undefined"!=typeof Blob&&Blob)&&ce.isSpecCompliantForm(s);if(!ce.isFunction(a))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ce.isDate(e))return e.toISOString();if(!n&&ce.isBlob(e))throw new oe("Blob is not supported. Use a Buffer instead.");return ce.isArrayBuffer(e)||ce.isTypedArray(e)?n&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function o(e,t,a){let n=e;if(e&&!a&&"object"==typeof e)if(ce.endsWith(t,"{}"))t=r?t:t.slice(0,-2),e=JSON.stringify(e);else if(ce.isArray(e)&&function(e){return ce.isArray(e)&&!e.some(xe)}(e)||(ce.isFileList(e)||ce.endsWith(t,"[]"))&&(n=ce.toArray(e)))return t=ue(t),n.forEach((function(e,r){!ce.isUndefined(e)&&null!==e&&s.append(!0===l?he([t],r,i):null===l?t:t+"[]",c(e))})),!1;return!!xe(e)||(s.append(he(a,t,i),c(e)),!1)}const d=[],m=Object.assign(pe,{defaultVisitor:o,convertValue:c,isVisitable:xe});if(!ce.isObject(e))throw new TypeError("data must be an object");return function e(t,r){if(!ce.isUndefined(t)){if(-1!==d.indexOf(t))throw Error("Circular reference detected in "+r.join("."));d.push(t),ce.forEach(t,(function(t,i){!0===(!(ce.isUndefined(t)||null===t)&&a.call(s,t,ce.isString(i)?i.trim():i,r,m))&&e(t,r?r.concat(i):[i])})),d.pop()}}(e),s}function fe(e){const s={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return s[e]}))}function ye(e,s){this._pairs=[],e&&ge(e,this,s)}const je=ye.prototype;function be(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ve(e,s,t){if(!s)return e;const r=t&&t.encode||be;ce.isFunction(t)&&(t={serialize:t});const a=t&&t.serialize;let i;if(i=a?a(s,t):ce.isURLSearchParams(s)?s.toString():new ye(s,t).toString(r),i){const s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}je.append=function(e,s){this._pairs.push([e,s])},je.toString=function(e){const s=e?function(s){return e.call(this,s,fe)}:fe;return this._pairs.map((function(e){return s(e[0])+"="+s(e[1])}),"").join("&")};class Ne{constructor(){this.handlers=[]}use(e,s,t){return this.handlers.push({fulfilled:e,rejected:s,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ce.forEach(this.handlers,(function(s){null!==s&&e(s)}))}}const we={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ke={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ye,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Se="undefined"!=typeof window&&"undefined"!=typeof document,Ce="object"==typeof navigator&&navigator||void 0,Ae=Se&&(!Ce||["ReactNative","NativeScript","NS"].indexOf(Ce.product)<0),Pe="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Ee=Se&&window.location.href||"http://localhost",_e={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Se,hasStandardBrowserEnv:Ae,hasStandardBrowserWebWorkerEnv:Pe,navigator:Ce,origin:Ee},Symbol.toStringTag,{value:"Module"})),...ke};function ze(e){function s(e,t,r,a){let i=e[a++];if("__proto__"===i)return!0;const l=Number.isFinite(+i),n=a>=e.length;if(i=!i&&ce.isArray(r)?r.length:i,n)return ce.hasOwnProp(r,i)?r[i]=[r[i],t]:r[i]=t,!l;r[i]&&ce.isObject(r[i])||(r[i]=[]);return s(e,t,r[i],a)&&ce.isArray(r[i])&&(r[i]=function(e){const s={},t=Object.keys(e);let r;const a=t.length;let i;for(r=0;r<a;r++)i=t[r],s[i]=e[i];return s}(r[i])),!l}if(ce.isFormData(e)&&ce.isFunction(e.entries)){const t={};return ce.forEachEntry(e,((e,r)=>{s(function(e){return ce.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,t,0)})),t}return null}const Re={transitional:we,adapter:["xhr","http","fetch"],transformRequest:[function(e,s){const t=s.getContentType()||"",r=t.indexOf("application/json")>-1,a=ce.isObject(e);a&&ce.isHTMLForm(e)&&(e=new FormData(e));if(ce.isFormData(e))return r?JSON.stringify(ze(e)):e;if(ce.isArrayBuffer(e)||ce.isBuffer(e)||ce.isStream(e)||ce.isFile(e)||ce.isBlob(e)||ce.isReadableStream(e))return e;if(ce.isArrayBufferView(e))return e.buffer;if(ce.isURLSearchParams(e))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(a){if(t.indexOf("application/x-www-form-urlencoded")>-1)return function(e,s){return ge(e,new _e.classes.URLSearchParams,Object.assign({visitor:function(e,s,t,r){return _e.isNode&&ce.isBuffer(e)?(this.append(s,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},s))}(e,this.formSerializer).toString();if((i=ce.isFileList(e))||t.indexOf("multipart/form-data")>-1){const s=this.env&&this.env.FormData;return ge(i?{"files[]":e}:e,s&&new s,this.formSerializer)}}return a||r?(s.setContentType("application/json",!1),function(e,s){if(ce.isString(e))try{return(s||JSON.parse)(e),ce.trim(e)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const s=this.transitional||Re.transitional,t=s&&s.forcedJSONParsing,r="json"===this.responseType;if(ce.isResponse(e)||ce.isReadableStream(e))return e;if(e&&ce.isString(e)&&(t&&!this.responseType||r)){const t=!(s&&s.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(a){if(t){if("SyntaxError"===a.name)throw oe.from(a,oe.ERR_BAD_RESPONSE,this,null,this.response);throw a}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:_e.classes.FormData,Blob:_e.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ce.forEach(["delete","get","head","post","put","patch"],(e=>{Re.headers[e]={}}));const Te=ce.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),De=Symbol("internals");function Le(e){return e&&String(e).trim().toLowerCase()}function Ie(e){return!1===e||null==e?e:ce.isArray(e)?e.map(Ie):String(e)}function Oe(e,s,t,r,a){return ce.isFunction(r)?r.call(this,s,t):(a&&(s=t),ce.isString(s)?ce.isString(r)?-1!==s.indexOf(r):ce.isRegExp(r)?r.test(s):void 0:void 0)}class Fe{constructor(e){e&&this.set(e)}set(e,s,t){const r=this;function a(e,s,t){const a=Le(s);if(!a)throw new Error("header name must be a non-empty string");const i=ce.findKey(r,a);(!i||void 0===r[i]||!0===t||void 0===t&&!1!==r[i])&&(r[i||s]=Ie(e))}const i=(e,s)=>ce.forEach(e,((e,t)=>a(e,t,s)));if(ce.isPlainObject(e)||e instanceof this.constructor)i(e,s);else if(ce.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))i((e=>{const s={};let t,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),t=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!t||s[t]&&Te[t]||("set-cookie"===t?s[t]?s[t].push(r):s[t]=[r]:s[t]=s[t]?s[t]+", "+r:r)})),s})(e),s);else if(ce.isObject(e)&&ce.isIterable(e)){let t,r,a={};for(const s of e){if(!ce.isArray(s))throw TypeError("Object iterator must return a key-value pair");a[r=s[0]]=(t=a[r])?ce.isArray(t)?[...t,s[1]]:[t,s[1]]:s[1]}i(a,s)}else null!=e&&a(s,e,t);return this}get(e,s){if(e=Le(e)){const t=ce.findKey(this,e);if(t){const e=this[t];if(!s)return e;if(!0===s)return function(e){const s=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=t.exec(e);)s[r[1]]=r[2];return s}(e);if(ce.isFunction(s))return s.call(this,e,t);if(ce.isRegExp(s))return s.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,s){if(e=Le(e)){const t=ce.findKey(this,e);return!(!t||void 0===this[t]||s&&!Oe(0,this[t],t,s))}return!1}delete(e,s){const t=this;let r=!1;function a(e){if(e=Le(e)){const a=ce.findKey(t,e);!a||s&&!Oe(0,t[a],a,s)||(delete t[a],r=!0)}}return ce.isArray(e)?e.forEach(a):a(e),r}clear(e){const s=Object.keys(this);let t=s.length,r=!1;for(;t--;){const a=s[t];e&&!Oe(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const s=this,t={};return ce.forEach(this,((r,a)=>{const i=ce.findKey(t,a);if(i)return s[i]=Ie(r),void delete s[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,s,t)=>s.toUpperCase()+t))}(a):String(a).trim();l!==a&&delete s[a],s[l]=Ie(r),t[l]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const s=Object.create(null);return ce.forEach(this,((t,r)=>{null!=t&&!1!==t&&(s[r]=e&&ce.isArray(t)?t.join(", "):t)})),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,s])=>e+": "+s)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...s){const t=new this(e);return s.forEach((e=>t.set(e))),t}static accessor(e){const s=(this[De]=this[De]={accessors:{}}).accessors,t=this.prototype;function r(e){const r=Le(e);s[r]||(!function(e,s){const t=ce.toCamelCase(" "+s);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+t,{value:function(e,t,a){return this[r].call(this,s,e,t,a)},configurable:!0})}))}(t,e),s[r]=!0)}return ce.isArray(e)?e.forEach(r):r(e),this}}function qe(e,s){const t=this||Re,r=s||t,a=Fe.from(r.headers);let i=r.data;return ce.forEach(e,(function(e){i=e.call(t,i,a.normalize(),s?s.status:void 0)})),a.normalize(),i}function Ue(e){return!(!e||!e.__CANCEL__)}function Be(e,s,t){oe.call(this,null==e?"canceled":e,oe.ERR_CANCELED,s,t),this.name="CanceledError"}function Me(e,s,t){const r=t.config.validateStatus;t.status&&r&&!r(t.status)?s(new oe("Request failed with status code "+t.status,[oe.ERR_BAD_REQUEST,oe.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t)):e(t)}Fe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ce.reduceDescriptors(Fe.prototype,(({value:e},s)=>{let t=s[0].toUpperCase()+s.slice(1);return{get:()=>e,set(e){this[t]=e}}})),ce.freezeMethods(Fe),ce.inherits(Be,oe,{__CANCEL__:!0});const He=(e,s,t=3)=>{let r=0;const a=function(e,s){e=e||10;const t=new Array(e),r=new Array(e);let a,i=0,l=0;return s=void 0!==s?s:1e3,function(n){const c=Date.now(),o=r[l];a||(a=c),t[i]=n,r[i]=c;let d=l,m=0;for(;d!==i;)m+=t[d++],d%=e;if(i=(i+1)%e,i===l&&(l=(l+1)%e),c-a<s)return;const x=o&&c-o;return x?Math.round(1e3*m/x):void 0}}(50,250);return function(e,s){let t,r,a=0,i=1e3/s;const l=(s,i=Date.now())=>{a=i,t=null,r&&(clearTimeout(r),r=null),e.apply(null,s)};return[(...e)=>{const s=Date.now(),n=s-a;n>=i?l(e,s):(t=e,r||(r=setTimeout((()=>{r=null,l(t)}),i-n)))},()=>t&&l(t)]}((t=>{const i=t.loaded,l=t.lengthComputable?t.total:void 0,n=i-r,c=a(n);r=i;e({loaded:i,total:l,progress:l?i/l:void 0,bytes:n,rate:c||void 0,estimated:c&&l&&i<=l?(l-i)/c:void 0,event:t,lengthComputable:null!=l,[s?"download":"upload"]:!0})}),t)},$e=(e,s)=>{const t=null!=e;return[r=>s[0]({lengthComputable:t,total:e,loaded:r}),s[1]]},Ve=e=>(...s)=>ce.asap((()=>e(...s))),Ye=_e.hasStandardBrowserEnv?((e,s)=>t=>(t=new URL(t,_e.origin),e.protocol===t.protocol&&e.host===t.host&&(s||e.port===t.port)))(new URL(_e.origin),_e.navigator&&/(msie|trident)/i.test(_e.navigator.userAgent)):()=>!0,We=_e.hasStandardBrowserEnv?{write(e,s,t,r,a,i){const l=[e+"="+encodeURIComponent(s)];ce.isNumber(t)&&l.push("expires="+new Date(t).toGMTString()),ce.isString(r)&&l.push("path="+r),ce.isString(a)&&l.push("domain="+a),!0===i&&l.push("secure"),document.cookie=l.join("; ")},read(e){const s=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ge(e,s,t){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(s);return e&&(r||0==t)?function(e,s){return s?e.replace(/\/?\/$/,"")+"/"+s.replace(/^\/+/,""):e}(e,s):s}const Je=e=>e instanceof Fe?{...e}:e;function Ke(e,s){s=s||{};const t={};function r(e,s,t,r){return ce.isPlainObject(e)&&ce.isPlainObject(s)?ce.merge.call({caseless:r},e,s):ce.isPlainObject(s)?ce.merge({},s):ce.isArray(s)?s.slice():s}function a(e,s,t,a){return ce.isUndefined(s)?ce.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,s,0,a)}function i(e,s){if(!ce.isUndefined(s))return r(void 0,s)}function l(e,s){return ce.isUndefined(s)?ce.isUndefined(e)?void 0:r(void 0,e):r(void 0,s)}function n(t,a,i){return i in s?r(t,a):i in e?r(void 0,t):void 0}const c={url:i,method:i,data:i,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:n,headers:(e,s,t)=>a(Je(e),Je(s),0,!0)};return ce.forEach(Object.keys(Object.assign({},e,s)),(function(r){const i=c[r]||a,l=i(e[r],s[r],r);ce.isUndefined(l)&&i!==n||(t[r]=l)})),t}const Ze=e=>{const s=Ke({},e);let t,{data:r,withXSRFToken:a,xsrfHeaderName:i,xsrfCookieName:l,headers:n,auth:c}=s;if(s.headers=n=Fe.from(n),s.url=ve(Ge(s.baseURL,s.url,s.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&n.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ce.isFormData(r))if(_e.hasStandardBrowserEnv||_e.hasStandardBrowserWebWorkerEnv)n.setContentType(void 0);else if(!1!==(t=n.getContentType())){const[e,...s]=t?t.split(";").map((e=>e.trim())).filter(Boolean):[];n.setContentType([e||"multipart/form-data",...s].join("; "))}if(_e.hasStandardBrowserEnv&&(a&&ce.isFunction(a)&&(a=a(s)),a||!1!==a&&Ye(s.url))){const e=i&&l&&We.read(l);e&&n.set(i,e)}return s},Xe="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(s,t){const r=Ze(e);let a=r.data;const i=Fe.from(r.headers).normalize();let l,n,c,o,d,{responseType:m,onUploadProgress:x,onDownloadProgress:u}=r;function h(){o&&o(),d&&d(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;function g(){if(!p)return;const r=Fe.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());Me((function(e){s(e),h()}),(function(e){t(e),h()}),{data:m&&"text"!==m&&"json"!==m?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:r,config:e,request:p}),p=null}p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout,"onloadend"in p?p.onloadend=g:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(g)},p.onabort=function(){p&&(t(new oe("Request aborted",oe.ECONNABORTED,e,p)),p=null)},p.onerror=function(){t(new oe("Network Error",oe.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let s=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||we;r.timeoutErrorMessage&&(s=r.timeoutErrorMessage),t(new oe(s,a.clarifyTimeoutError?oe.ETIMEDOUT:oe.ECONNABORTED,e,p)),p=null},void 0===a&&i.setContentType(null),"setRequestHeader"in p&&ce.forEach(i.toJSON(),(function(e,s){p.setRequestHeader(s,e)})),ce.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),m&&"json"!==m&&(p.responseType=r.responseType),u&&([c,d]=He(u,!0),p.addEventListener("progress",c)),x&&p.upload&&([n,o]=He(x),p.upload.addEventListener("progress",n),p.upload.addEventListener("loadend",o)),(r.cancelToken||r.signal)&&(l=s=>{p&&(t(!s||s.type?new Be(null,e,p):s),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const f=function(e){const s=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return s&&s[1]||""}(r.url);f&&-1===_e.protocols.indexOf(f)?t(new oe("Unsupported protocol "+f+":",oe.ERR_BAD_REQUEST,e)):p.send(a||null)}))},Qe=(e,s)=>{const{length:t}=e=e?e.filter(Boolean):[];if(s||t){let t,r=new AbortController;const a=function(e){if(!t){t=!0,l();const s=e instanceof Error?e:this.reason;r.abort(s instanceof oe?s:new Be(s instanceof Error?s.message:s))}};let i=s&&setTimeout((()=>{i=null,a(new oe(`timeout ${s} of ms exceeded`,oe.ETIMEDOUT))}),s);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:n}=r;return n.unsubscribe=()=>ce.asap(l),n}},es=function*(e,s){let t=e.byteLength;if(t<s)return void(yield e);let r,a=0;for(;a<t;)r=a+s,yield e.slice(a,r),a=r},ss=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const s=e.getReader();try{for(;;){const{done:e,value:t}=await s.read();if(e)break;yield t}}finally{await s.cancel()}},ts=(e,s,t,r)=>{const a=async function*(e,s){for await(const t of ss(e))yield*es(t,s)}(e,s);let i,l=0,n=e=>{i||(i=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:s,value:r}=await a.next();if(s)return n(),void e.close();let i=r.byteLength;if(t){let e=l+=i;t(e)}e.enqueue(new Uint8Array(r))}catch(s){throw n(s),s}},cancel:e=>(n(e),a.return())},{highWaterMark:2})},rs="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,as=rs&&"function"==typeof ReadableStream,is=rs&&("function"==typeof TextEncoder?(e=>s=>e.encode(s))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ls=(e,...s)=>{try{return!!e(...s)}catch(t){return!1}},ns=as&&ls((()=>{let e=!1;const s=new Request(_e.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!s})),cs=as&&ls((()=>ce.isReadableStream(new Response("").body))),os={stream:cs&&(e=>e.body)};var ds;rs&&(ds=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!os[e]&&(os[e]=ce.isFunction(ds[e])?s=>s[e]():(s,t)=>{throw new oe(`Response type '${e}' is not supported`,oe.ERR_NOT_SUPPORT,t)})})));const ms=async(e,s)=>{const t=ce.toFiniteNumber(e.getContentLength());return null==t?(async e=>{if(null==e)return 0;if(ce.isBlob(e))return e.size;if(ce.isSpecCompliantForm(e)){const s=new Request(_e.origin,{method:"POST",body:e});return(await s.arrayBuffer()).byteLength}return ce.isArrayBufferView(e)||ce.isArrayBuffer(e)?e.byteLength:(ce.isURLSearchParams(e)&&(e+=""),ce.isString(e)?(await is(e)).byteLength:void 0)})(s):t},xs={http:null,xhr:Xe,fetch:rs&&(async e=>{let{url:s,method:t,data:r,signal:a,cancelToken:i,timeout:l,onDownloadProgress:n,onUploadProgress:c,responseType:o,headers:d,withCredentials:m="same-origin",fetchOptions:x}=Ze(e);o=o?(o+"").toLowerCase():"text";let u,h=Qe([a,i&&i.toAbortSignal()],l);const p=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(c&&ns&&"get"!==t&&"head"!==t&&0!==(g=await ms(d,r))){let e,t=new Request(s,{method:"POST",body:r,duplex:"half"});if(ce.isFormData(r)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){const[e,s]=$e(g,He(Ve(c)));r=ts(t.body,65536,e,s)}}ce.isString(m)||(m=m?"include":"omit");const a="credentials"in Request.prototype;u=new Request(s,{...x,signal:h,method:t.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:a?m:void 0});let i=await fetch(u);const l=cs&&("stream"===o||"response"===o);if(cs&&(n||l&&p)){const e={};["status","statusText","headers"].forEach((s=>{e[s]=i[s]}));const s=ce.toFiniteNumber(i.headers.get("content-length")),[t,r]=n&&$e(s,He(Ve(n),!0))||[];i=new Response(ts(i.body,65536,t,(()=>{r&&r(),p&&p()})),e)}o=o||"text";let f=await os[ce.findKey(os,o)||"text"](i,e);return!l&&p&&p(),await new Promise(((s,t)=>{Me(s,t,{data:f,headers:Fe.from(i.headers),status:i.status,statusText:i.statusText,config:e,request:u})}))}catch(f){if(p&&p(),f&&"TypeError"===f.name&&/Load failed|fetch/i.test(f.message))throw Object.assign(new oe("Network Error",oe.ERR_NETWORK,e,u),{cause:f.cause||f});throw oe.from(f,f&&f.code,e,u)}})};ce.forEach(xs,((e,s)=>{if(e){try{Object.defineProperty(e,"name",{value:s})}catch(t){}Object.defineProperty(e,"adapterName",{value:s})}}));const us=e=>`- ${e}`,hs=e=>ce.isFunction(e)||null===e||!1===e,ps=e=>{e=ce.isArray(e)?e:[e];const{length:s}=e;let t,r;const a={};for(let i=0;i<s;i++){let s;if(t=e[i],r=t,!hs(t)&&(r=xs[(s=String(t)).toLowerCase()],void 0===r))throw new oe(`Unknown adapter '${s}'`);if(r)break;a[s||"#"+i]=r}if(!r){const e=Object.entries(a).map((([e,s])=>`adapter ${e} `+(!1===s?"is not supported by the environment":"is not available in the build")));throw new oe("There is no suitable adapter to dispatch the request "+(s?e.length>1?"since :\n"+e.map(us).join("\n"):" "+us(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function gs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Be(null,e)}function fs(e){gs(e),e.headers=Fe.from(e.headers),e.data=qe.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return ps(e.adapter||Re.adapter)(e).then((function(s){return gs(e),s.data=qe.call(e,e.transformResponse,s),s.headers=Fe.from(s.headers),s}),(function(s){return Ue(s)||(gs(e),s&&s.response&&(s.response.data=qe.call(e,e.transformResponse,s.response),s.response.headers=Fe.from(s.response.headers))),Promise.reject(s)}))}const ys="1.9.0",js={};["object","boolean","number","function","string","symbol"].forEach(((e,s)=>{js[e]=function(t){return typeof t===e||"a"+(s<1?"n ":" ")+e}}));const bs={};js.transitional=function(e,s,t){return(r,a,i)=>{if(!1===e)throw new oe(function(e,s){return"[Axios v1.9.0] Transitional option '"+e+"'"+s+(t?". "+t:"")}(a," has been removed"+(s?" in "+s:"")),oe.ERR_DEPRECATED);return s&&!bs[a]&&(bs[a]=!0),!e||e(r,a,i)}},js.spelling=function(e){return(e,s)=>!0};const vs={assertOptions:function(e,s,t){if("object"!=typeof e)throw new oe("options must be an object",oe.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const i=r[a],l=s[i];if(l){const s=e[i],t=void 0===s||l(s,i,e);if(!0!==t)throw new oe("option "+i+" must be "+t,oe.ERR_BAD_OPTION_VALUE)}else if(!0!==t)throw new oe("Unknown option "+i,oe.ERR_BAD_OPTION)}},validators:js},Ns=vs.validators;class ws{constructor(e){this.defaults=e||{},this.interceptors={request:new Ne,response:new Ne}}async request(e,s){try{return await this._request(e,s)}catch(t){if(t instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const s=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?s&&!String(t.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+s):t.stack=s}catch(r){}}throw t}}_request(e,s){"string"==typeof e?(s=s||{}).url=e:s=e||{},s=Ke(this.defaults,s);const{transitional:t,paramsSerializer:r,headers:a}=s;void 0!==t&&vs.assertOptions(t,{silentJSONParsing:Ns.transitional(Ns.boolean),forcedJSONParsing:Ns.transitional(Ns.boolean),clarifyTimeoutError:Ns.transitional(Ns.boolean)},!1),null!=r&&(ce.isFunction(r)?s.paramsSerializer={serialize:r}:vs.assertOptions(r,{encode:Ns.function,serialize:Ns.function},!0)),void 0!==s.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),vs.assertOptions(s,{baseUrl:Ns.spelling("baseURL"),withXsrfToken:Ns.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=a&&ce.merge(a.common,a[s.method]);a&&ce.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),s.headers=Fe.concat(i,a);const l=[];let n=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(s)||(n=n&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const c=[];let o;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let d,m=0;if(!n){const e=[fs.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,c),d=e.length,o=Promise.resolve(s);m<d;)o=o.then(e[m++],e[m++]);return o}d=l.length;let x=s;for(m=0;m<d;){const e=l[m++],s=l[m++];try{x=e(x)}catch(u){s.call(this,u);break}}try{o=fs.call(this,x)}catch(u){return Promise.reject(u)}for(m=0,d=c.length;m<d;)o=o.then(c[m++],c[m++]);return o}getUri(e){return ve(Ge((e=Ke(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}ce.forEach(["delete","get","head","options"],(function(e){ws.prototype[e]=function(s,t){return this.request(Ke(t||{},{method:e,url:s,data:(t||{}).data}))}})),ce.forEach(["post","put","patch"],(function(e){function s(s){return function(t,r,a){return this.request(Ke(a||{},{method:e,headers:s?{"Content-Type":"multipart/form-data"}:{},url:t,data:r}))}}ws.prototype[e]=s(),ws.prototype[e+"Form"]=s(!0)}));class ks{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let s;this.promise=new Promise((function(e){s=e}));const t=this;this.promise.then((e=>{if(!t._listeners)return;let s=t._listeners.length;for(;s-- >0;)t._listeners[s](e);t._listeners=null})),this.promise.then=e=>{let s;const r=new Promise((e=>{t.subscribe(e),s=e})).then(e);return r.cancel=function(){t.unsubscribe(s)},r},e((function(e,r,a){t.reason||(t.reason=new Be(e,r,a),s(t.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const s=this._listeners.indexOf(e);-1!==s&&this._listeners.splice(s,1)}toAbortSignal(){const e=new AbortController,s=s=>{e.abort(s)};return this.subscribe(s),e.signal.unsubscribe=()=>this.unsubscribe(s),e.signal}static source(){let e;return{token:new ks((function(s){e=s})),cancel:e}}}const Ss={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ss).forEach((([e,s])=>{Ss[s]=e}));const Cs=function e(s){const t=new ws(s),r=v(ws.prototype.request,t);return ce.extend(r,ws.prototype,t,{allOwnKeys:!0}),ce.extend(r,t,null,{allOwnKeys:!0}),r.create=function(t){return e(Ke(s,t))},r}(Re);Cs.Axios=ws,Cs.CanceledError=Be,Cs.CancelToken=ks,Cs.isCancel=Ue,Cs.VERSION=ys,Cs.toFormData=ge,Cs.AxiosError=oe,Cs.Cancel=Cs.CanceledError,Cs.all=function(e){return Promise.all(e)},Cs.spread=function(e){return function(s){return e.apply(null,s)}},Cs.isAxiosError=function(e){return ce.isObject(e)&&!0===e.isAxiosError},Cs.mergeConfig=Ke,Cs.AxiosHeaders=Fe,Cs.formToJSON=e=>ze(ce.isHTMLForm(e)?new FormData(e):e),Cs.getAdapter=ps,Cs.HttpStatusCode=Ss,Cs.default=Cs;const As=Cs.create({baseURL:"https://ebisera.com/api",timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});As.interceptors.request.use((e=>e),(e=>Promise.reject(e))),As.interceptors.request.use((e=>{const s=localStorage.getItem("auth_token");return s&&(e.headers.Authorization=`Bearer ${s}`),e}),(e=>Promise.reject(e))),As.interceptors.response.use((e=>e),(e=>{var s,t,r,a,i;if(401===(null==(s=e.response)?void 0:s.status)){localStorage.removeItem("auth_token"),localStorage.removeItem("user");const s=window.location.pathname.includes("/login")||window.location.pathname.includes("/signup"),l=(null==(r=null==(t=e.config)?void 0:t.url)?void 0:r.includes("/cars"))||(null==(i=null==(a=e.config)?void 0:a.url)?void 0:i.includes("/drivers"));s||l||(window.location.href="/login")}return Promise.reject(e)}));const Ps="/register",Es="/login",_s="/logout",zs="/users",Rs="/cars",Ts="/my-cars",Ds="/drivers",Ls="/bookings",Is="/my-bookings",Os="/chats",Fs="/my-chats",qs="/unread-messages-count",Us="/messages",Bs={DASHBOARD:"/owner/dashboard",MESSAGES:"/owner/messages",CHATS:"/owner/chats",BOOKINGS:"/owner/bookings"},Ms={DASHBOARD:"/admin/dashboard",USERS:"/admin/users",CARS:"/admin/cars",DRIVERS:"/admin/drivers",BOOKINGS:"/admin/bookings",REVENUE:"/admin/revenue"},Hs=e.createContext(void 0),$s=()=>{const s=e.useContext(Hs);if(void 0===s)throw new Error("useAuth must be used within an AuthProvider");return s},Vs=({children:s})=>{const[t,r]=e.useState(null),[a,i]=e.useState(!0),[l,n]=e.useState(null);e.useEffect((()=>{const e=localStorage.getItem("user");if(e)try{r(JSON.parse(e))}catch(s){localStorage.removeItem("user")}i(!1)}),[]);const c={user:t,isAuthenticated:!!t,isLoading:a,error:l,login:async(e,s)=>{i(!0),n(null);try{const t=await As.post(Es,{email:e,password:s});if(!t.data||!t.data.user)throw new Error("Login failed: Invalid response from server");{const e={id:t.data.user.id.toString(),email:t.data.user.email,name:t.data.user.name,role:t.data.user.role,phoneNumber:t.data.user.phone_number,isPhoneVerified:t.data.user.is_phone_verified,licenseImageUrl:t.data.user.license_image_url,licenseVerificationStatus:t.data.user.license_verification_status,createdAt:t.data.user.created_at};localStorage.setItem("auth_token",t.data.token),r(e),localStorage.setItem("user",JSON.stringify(e))}}catch(t){throw Cs.isAxiosError(t)&&t.response?t.response.data&&t.response.data.message?n(t.response.data.message):n(`Login failed: ${t.response.statusText}`):n(t instanceof Error?t.message:"An error occurred during login"),t}finally{i(!1)}},register:async(e,s)=>{i(!0),n(null);try{const t={name:e.name,email:e.email,password:s,password_confirmation:s,role:e.role||"client",phone_number:e.phoneNumber,license_image_url:e.licenseImageUrl},a=await As.post(Ps,t);if(!a.data||!a.data.user)throw new Error("Registration failed: Invalid response from server");{const e={id:a.data.user.id.toString(),email:a.data.user.email,name:a.data.user.name,role:a.data.user.role,phoneNumber:a.data.user.phone_number,isPhoneVerified:a.data.user.is_phone_verified,licenseImageUrl:a.data.user.license_image_url,licenseVerificationStatus:a.data.user.license_verification_status,createdAt:a.data.user.created_at};localStorage.setItem("auth_token",a.data.token),r(e),localStorage.setItem("user",JSON.stringify(e))}}catch(t){if(Cs.isAxiosError(t)&&t.response)if(t.response.data&&t.response.data.errors){const e=t.response.data.errors;let s="";if(e.email)s="This email address is already registered. Please use a different email or try logging in.";else{s=Object.values(e).flat().join(", ")}n(s)}else t.response.data&&t.response.data.message?n(t.response.data.message):n(`Registration failed: ${t.response.statusText}`);else n(t instanceof Error?t.message:"An error occurred during registration");throw t}finally{i(!1)}},logout:async()=>{try{localStorage.getItem("auth_token")&&await As.post(_s)}catch(e){}finally{r(null),localStorage.removeItem("user"),localStorage.removeItem("auth_token")}},updateUser:async e=>{i(!0),n(null);try{if(!t)throw new Error("No user is logged in");const s={name:e.name,email:e.email,phone_number:e.phoneNumber,license_image_url:e.licenseImageUrl},a=await As.put(`${zs}/${t.id}`,s);if(!a.data||!a.data.user)throw new Error("Update failed: Invalid response from server");{const e={...t,name:a.data.user.name||t.name,email:a.data.user.email||t.email,phoneNumber:a.data.user.phone_number,licenseImageUrl:a.data.user.license_image_url};r(e),localStorage.setItem("user",JSON.stringify(e))}}catch(s){if(Cs.isAxiosError(s)&&s.response)if(s.response.data&&s.response.data.errors){const e=Object.values(s.response.data.errors).flat().join(", ");n(e)}else s.response.data&&s.response.data.message?n(s.response.data.message):n(`Update failed: ${s.response.statusText}`);else n(s instanceof Error?s.message:"An error occurred while updating user data");throw s}finally{i(!1)}}};return j.jsx(Hs.Provider,{value:c,children:s})},Ys=e.createContext(void 0),Ws=()=>{const s=e.useContext(Ys);if(void 0===s)throw new Error("useCars must be used within a CarProvider");return s},Gs=({children:s})=>{const[t,r]=e.useState([]),[a,i]=e.useState([]),[l,n]=e.useState([]),[c,o]=e.useState([]),[d,m]=e.useState(null),[x,u]=e.useState(!0),[h,p]=e.useState(null),g=async()=>{u(!0),p(null);try{const e=await As.get(Rs);if(e.data&&Array.isArray(e.data.cars)){const s=e.data.cars.map((e=>({id:e.id.toString(),ownerId:e.owner_id.toString(),make:e.make,model:e.model,year:e.year,images:e.images||[],description:e.description,features:e.features||[],location:e.location,pricePerHour:e.price_per_hour,availabilityNotes:e.availability_notes||"",isActive:e.is_active,createdAt:e.created_at}))),t=s.filter((e=>e.isActive));r(s),i(t),n(t)}else r([]),i([]),n([]),p("API did not return expected data format")}catch(e){r([]),i([]),n([]),p("Failed to fetch cars from server")}finally{u(!1)}};e.useEffect((()=>{g()}),[]);const f={cars:t,filteredCars:a,activeCars:l,ownerCars:c,selectedCar:d,isLoading:x,error:h,fetchCars:g,fetchOwnerCars:async()=>{u(!0),p(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const e=await As.get(Ts);if(e.data&&Array.isArray(e.data)){const s=e.data.map((e=>({id:e.id.toString(),ownerId:e.owner_id.toString(),make:e.make,model:e.model,year:e.year,images:e.images||[],description:e.description,features:e.features||[],location:e.location,pricePerHour:e.price_per_hour,availabilityNotes:e.availability_notes||"",isActive:e.is_active,createdAt:e.created_at})));o(s)}else o([]),p("API did not return expected data format")}catch(e){o([]),p("Failed to fetch owner cars from server")}finally{u(!1)}},getCarById:async e=>{u(!0),p(null);try{const s=t.find((s=>s.id===e));if(s)return m(s),s;const r=await As.get(`${Rs}/${e}`);if(r.data&&r.data.car){const e={id:r.data.car.id.toString(),ownerId:r.data.car.owner_id.toString(),make:r.data.car.make,model:r.data.car.model,year:r.data.car.year,images:r.data.car.images||[],description:r.data.car.description,features:r.data.car.features||[],location:r.data.car.location,pricePerHour:r.data.car.price_per_hour,availabilityNotes:r.data.car.availability_notes||"",isActive:r.data.car.is_active,createdAt:r.data.car.created_at};return m(e),e}return p("Car not found"),null}catch(s){return p("Failed to fetch car details"),null}finally{u(!1)}},setSelectedCar:m,addCar:async(e,s)=>{u(!0),p(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const t=new FormData;t.append("make",e.make),t.append("model",e.model),t.append("year",e.year.toString()),t.append("description",e.description),t.append("location",e.location),t.append("price_per_hour",e.pricePerHour.toString()),t.append("availability_notes",e.availabilityNotes||""),t.append("is_active",e.isActive?"1":"0"),t.append("features",JSON.stringify(e.features)),s&&s.length>0?s.forEach(((e,s)=>{t.append(`images[${s}]`,e)})):e.images&&e.images.length>0&&t.append("image_urls",JSON.stringify(e.images));const a=localStorage.getItem("auth_token"),l=await As.post(Rs,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${a}`}});if(l.data&&l.data.car){const e={id:l.data.car.id.toString(),ownerId:l.data.car.owner_id.toString(),make:l.data.car.make,model:l.data.car.model,year:l.data.car.year,images:l.data.car.images||[],description:l.data.car.description,features:l.data.car.features||[],location:l.data.car.location,pricePerHour:l.data.car.price_per_hour,availabilityNotes:l.data.car.availability_notes||"",isActive:l.data.car.is_active,createdAt:l.data.car.created_at};return r((s=>[...s,e])),o((s=>[...s,e])),e.isActive&&(i((s=>[...s,e])),n((s=>[...s,e]))),e}throw new Error("Failed to add car: Invalid response from server")}catch(t){if(Cs.isAxiosError(t)&&t.response)if(t.response.data&&t.response.data.errors){const e=Object.values(t.response.data.errors).flat().join(", ");p(e)}else t.response.data&&t.response.data.message?p(t.response.data.message):p(`Failed to add car: ${t.response.statusText}`);else p(t instanceof Error?t.message:"Failed to add car");throw t}finally{u(!1)}},updateCar:async(e,s)=>{u(!0),p(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const t={};s.make&&(t.make=s.make),s.model&&(t.model=s.model),s.year&&(t.year=s.year),s.description&&(t.description=s.description),s.location&&(t.location=s.location),s.pricePerHour&&(t.price_per_hour=s.pricePerHour),void 0!==s.availabilityNotes&&(t.availability_notes=s.availabilityNotes),void 0!==s.isActive&&(t.is_active=s.isActive?1:0),s.features&&(t.features=JSON.stringify(s.features));const a=await As.put(`${Rs}/${e}`,t);if(a.data&&a.data.car){const s={id:a.data.car.id.toString(),ownerId:a.data.car.owner_id.toString(),make:a.data.car.make,model:a.data.car.model,year:a.data.car.year,images:a.data.car.images||[],description:a.data.car.description,features:a.data.car.features||[],location:a.data.car.location,pricePerHour:a.data.car.price_per_hour,availabilityNotes:a.data.car.availability_notes||"",isActive:a.data.car.is_active,createdAt:a.data.car.created_at};return r((t=>{const r=[...t],a=r.findIndex((s=>s.id===e));return-1!==a&&(r[a]=s),r})),o((t=>{const r=[...t],a=r.findIndex((s=>s.id===e));return-1!==a&&(r[a]=s),r})),i((t=>{const r=[...t],a=r.findIndex((s=>s.id===e));return s.isActive?-1===a?r.push(s):r[a]=s:-1!==a&&r.splice(a,1),r})),n((t=>{const r=[...t],a=r.findIndex((s=>s.id===e));return s.isActive?-1===a?r.push(s):r[a]=s:-1!==a&&r.splice(a,1),r})),(null==d?void 0:d.id)===e&&m(s),s}throw new Error("Failed to update car: Invalid response from server")}catch(t){if(Cs.isAxiosError(t)&&t.response)if(t.response.data&&t.response.data.errors){const e=Object.values(t.response.data.errors).flat().join(", ");p(e)}else t.response.data&&t.response.data.message?p(t.response.data.message):p(`Failed to update car: ${t.response.statusText}`);else p(t instanceof Error?t.message:"Failed to update car");throw t}finally{u(!1)}},deleteCar:async e=>{u(!0),p(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");await As.delete(`${Rs}/${e}`),r((s=>s.filter((s=>s.id!==e)))),o((s=>s.filter((s=>s.id!==e)))),i((s=>s.filter((s=>s.id!==e)))),n((s=>s.filter((s=>s.id!==e)))),(null==d?void 0:d.id)===e&&m(null)}catch(s){throw Cs.isAxiosError(s)&&s.response?s.response.data&&s.response.data.message?p(s.response.data.message):p(`Failed to delete car: ${s.response.statusText}`):p(s instanceof Error?s.message:"Failed to delete car"),s}finally{u(!1)}},filterCars:e=>{const s=l;if(!e.trim())return void i(s);const t=e.toLowerCase(),r=s.filter((e=>e.make.toLowerCase().includes(t)||e.model.toLowerCase().includes(t)||e.location.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)));i(r)}};return j.jsx(Ys.Provider,{value:f,children:s})},Js=[{id:"driver-1",userId:"user-3",name:"Michael Johnson",age:32,experience:10,profileImage:"https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"Downtown Tech District",pricePerHour:25,rating:4.8,reviews:124,specialties:["City Driving","Long Distance","Airport Transfers"],availabilityNotes:"Available weekdays from 8 AM to 6 PM",isAvailable:!0,createdAt:"2024-02-01T00:00:00.000Z"},{id:"driver-2",userId:"user-4",name:"Sarah Williams",age:29,experience:7,profileImage:"https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"Westside Shopping Center",pricePerHour:22,rating:4.9,reviews:98,specialties:["City Driving","Event Transportation","Tourist Guide"],availabilityNotes:"Available all days from 10 AM to 8 PM",isAvailable:!0,createdAt:"2024-02-05T00:00:00.000Z"},{id:"driver-3",userId:"user-5",name:"David Brown",age:35,experience:12,profileImage:"https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"North Tech Park",pricePerHour:28,rating:4.7,reviews:156,specialties:["Luxury Vehicles","Corporate Events","Airport Transfers"],availabilityNotes:"Available weekends and evenings after 6 PM",isAvailable:!0,createdAt:"2024-02-10T00:00:00.000Z"}],Ks=e.createContext(void 0),Zs=()=>{const s=e.useContext(Ks);if(!s)throw new Error("useDrivers must be used within a DriverProvider");return s},Xs=({children:s})=>{const[t,r]=e.useState([]),[a,i]=e.useState([]),[l,n]=e.useState(null),[c,o]=e.useState(!1),[d,m]=e.useState(null),[x,u]=e.useState(!1),h=e.useCallback((async()=>{o(!0),m(null);try{const e=await As.get(Ds);if(e.data&&Array.isArray(e.data)){const s=e.data.map((e=>({id:e.id.toString(),userId:e.user_id.toString(),name:e.name,age:e.age,experience:e.experience,profileImage:e.profile_image||"",licenseNumber:e.license_number,licenseVerificationStatus:e.license_verification_status,location:e.location,pricePerHour:e.price_per_hour,rating:Number(e.rating||0),reviews:Number(e.reviews||0),specialties:e.specialties||[],availabilityNotes:e.availability_notes||"",isAvailable:e.is_available,createdAt:e.created_at})));r(s),i(s),u(!0)}else r([]),i([]),m("API did not return expected data format")}catch(e){r([]),i([]),m("Failed to fetch drivers from server")}finally{o(!1),u(!0)}}),[]),p=async(e,s)=>{o(!0),m(null);try{const a=t.findIndex((s=>s.id===e));if(-1===a)throw new Error("Driver not found");const c={...t[a],...s},o=[...t];return o[a]=c,r(o),i(o),(null==l?void 0:l.id)===e&&n(c),c}catch(a){throw m("Failed to update driver"),a}finally{o(!1)}},g=e.useCallback((e=>{if(!e.trim())return void i(t);const s=e.toLowerCase(),r=t.filter((e=>e.name.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||e.specialties.some((e=>e.toLowerCase().includes(s)))));i(r)}),[t]);e.useEffect((()=>{x||h()}),[x,h]);const f={drivers:t,filteredDrivers:a,selectedDriver:l,isLoading:c,error:d,fetchDrivers:h,getDriverById:async e=>{o(!0),m(null);try{let r=t.find((s=>s.id===e))||null;if(!r)try{const s=await As.get(`/drivers/${e}`);s.data&&(r={id:s.data.id.toString(),userId:s.data.user_id.toString(),name:s.data.name,age:s.data.age,experience:s.data.experience,profileImage:s.data.profile_image||"",licenseNumber:s.data.license_number,licenseVerificationStatus:s.data.license_verification_status,location:s.data.location,pricePerHour:s.data.price_per_hour,rating:Number(s.data.rating||0),reviews:Number(s.data.reviews||0),specialties:s.data.specialties||[],availabilityNotes:s.data.availability_notes||"",isAvailable:s.data.is_available,isActive:s.data.is_active,createdAt:s.data.created_at})}catch(s){r=Js.find((s=>s.id===e))||null}return n(r),r}catch(r){return m("Failed to fetch driver"),null}finally{o(!1)}},setSelectedDriver:n,addDriver:async(e,s)=>{o(!0),m(null);try{const t=new FormData;t.append("name",e.name),t.append("age",e.age.toString()),t.append("experience",e.experience.toString()),t.append("license_number",e.licenseNumber),t.append("location",e.location),t.append("price_per_hour",e.pricePerHour.toString()),t.append("specialties",JSON.stringify(e.specialties)),e.availabilityNotes&&t.append("availability_notes",e.availabilityNotes),s&&t.append("profile_image",s);const a=localStorage.getItem("auth_token"),l=await As.post(Ds,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${a}`}}),n={id:l.data.driver.id.toString(),userId:l.data.driver.user_id.toString(),name:l.data.driver.name,age:l.data.driver.age,experience:l.data.driver.experience,profileImage:l.data.driver.profile_image||"",licenseNumber:l.data.driver.license_number,licenseVerificationStatus:l.data.driver.license_verification_status,location:l.data.driver.location,pricePerHour:l.data.driver.price_per_hour,rating:Number(l.data.driver.rating||0),reviews:Number(l.data.driver.reviews||0),specialties:l.data.driver.specialties||[],availabilityNotes:l.data.driver.availability_notes||"",isAvailable:l.data.driver.is_available,createdAt:l.data.driver.created_at};return r((e=>[...e,n])),i((e=>[...e,n])),n}catch(t){if(Cs.isAxiosError(t)&&t.response)if(t.response.data&&t.response.data.errors){const e=Object.values(t.response.data.errors).flat().join(", ");m(e)}else t.response.data&&t.response.data.message?m(t.response.data.message):m(`Failed to add driver: ${t.response.statusText}`);else m(t instanceof Error?t.message:"Failed to add driver");throw t}finally{o(!1)}},updateDriver:p,deleteDriver:async e=>{o(!0),m(null);try{const s=t.filter((s=>s.id!==e));r(s),i(s),(null==l?void 0:l.id)===e&&n(null)}catch(s){throw m("Failed to delete driver"),s}finally{o(!1)}},filterDrivers:g,toggleDriverAvailability:async e=>{const s=t.find((s=>s.id===e));if(!s)throw new Error("Driver not found");return p(e,{isAvailable:!s.isAvailable})}};return j.jsx(Ks.Provider,{value:f,children:s})},Qs=e.createContext(void 0),et=()=>{const s=e.useContext(Qs);if(!s)throw new Error("useBookings must be used within a BookingProvider");return s},st=({children:s})=>{const[t,r]=e.useState([]),[a,i]=e.useState([]),[l,n]=e.useState(!0),[c,o]=e.useState(null),d=async()=>{n(!0),o(null);try{const e=await As.get(Is),s=(e.data||[]).map((e=>({id:e.id,userId:e.user_id,itemType:e.item_type,itemId:e.item_id,startTime:e.start_time,endTime:e.end_time,totalPrice:e.total_price,status:e.status,notes:e.notes,createdAt:e.created_at})));r(s)}catch(e){o("Failed to fetch bookings")}finally{n(!1)}},m=async(e,s)=>{n(!0),o(null);try{const a=t.findIndex((s=>s.id===e));if(-1===a)throw new Error("Booking not found");const l={...t[a],status:s},n=[...t];return n[a]=l,r(n),i((s=>{const t=s.findIndex((s=>s.id===e));if(-1!==t){const e=[...s];return e[t]=l,e}return s})),l}catch(a){throw o("Failed to update booking status"),a}finally{n(!1)}};e.useEffect((()=>{d()}),[]);const x={bookings:t,userBookings:a,isLoading:l,error:c,fetchBookings:d,getBookingById:async e=>{n(!0),o(null);try{const s=await As.get(`${Ls}/${e}`),t=s.data.booking||s.data;return{id:t.id,userId:t.user_id,itemType:t.item_type,itemId:t.item_id,startTime:t.start_time,endTime:t.end_time,totalPrice:t.total_price,status:t.status,notes:t.notes,createdAt:t.created_at}}catch(s){return o("Failed to fetch booking"),null}finally{n(!1)}},createBooking:async e=>{var s,t;n(!0),o(null);try{const s={item_type:e.itemType,item_id:e.itemId,start_time:e.startTime,end_time:e.endTime,notes:e.notes||""},t=await As.post(Ls,s),a=t.data.booking||t.data,i={id:a.id,userId:a.user_id,itemType:a.item_type,itemId:a.item_id,startTime:a.start_time,endTime:a.end_time,totalPrice:a.total_price,status:a.status,notes:a.notes,createdAt:a.created_at};return r((e=>[...e,i])),i}catch(a){const e=(null==(t=null==(s=a.response)?void 0:s.data)?void 0:t.message)||"Failed to create booking";throw o(e),new Error(e)}finally{n(!1)}},updateBookingStatus:m,cancelBooking:async e=>m(e,"cancelled"),getUserBookings:e=>t.filter((s=>s.userId===e))};return j.jsx(Qs.Provider,{value:x,children:s})},tt=e.createContext(void 0),rt=({children:s})=>{const[t,r]=e.useState(null),[a,i]=e.useState([]),[l,n]=e.useState([]),[c,o]=e.useState([]),[d,m]=e.useState([]),[x,u]=e.useState([]),[h,p]=e.useState(!1),[g,f]=e.useState(null),y=async()=>{var e;p(!0),f(null);try{const e=await As.get(Ms.USERS);i(e.data)}catch(s){f(`Failed to fetch users: ${(null==(e=s.response)?void 0:e.status)||s.message}`)}finally{p(!1)}},b=async()=>{p(!0),f(null);try{const e=(await As.get(Ms.DRIVERS)).data.map((e=>({id:e.id.toString(),userId:e.user_id.toString(),name:e.name,age:e.age,experience:e.experience,profileImage:e.profile_image||"",licenseNumber:e.license_number,licenseVerificationStatus:e.license_verification_status,location:e.location,pricePerHour:e.price_per_hour,rating:Number(e.rating||0),reviews:Number(e.reviews||0),specialties:e.specialties||[],availabilityNotes:e.availability_notes||"",isAvailable:e.is_available,isActive:e.is_active,createdAt:e.created_at})));o(e)}catch(e){f("Failed to fetch drivers")}finally{p(!1)}},v=async()=>{try{const e=await As.get("/admin/gps-requests");u(e.data)}catch(e){throw new Error("Failed to fetch GPS requests")}},N={stats:t,users:a,cars:l,drivers:c,bookings:d,gpsRequests:x,isLoading:h,error:g,fetchDashboardStats:async()=>{var e;p(!0),f(null);try{const e=await As.get(Ms.DASHBOARD);r(e.data)}catch(s){f(`Failed to fetch dashboard statistics: ${(null==(e=s.response)?void 0:e.status)||s.message}`)}finally{p(!1)}},fetchUsers:y,fetchCars:async()=>{p(!0),f(null);try{const e=(await As.get(Ms.CARS)).data.map((e=>({...e,isActive:e.is_active,pricePerHour:e.price_per_hour,ownerId:e.owner_id,createdAt:e.created_at,updatedAt:e.updated_at})));n(e)}catch(e){f("Failed to fetch cars")}finally{p(!1)}},fetchDrivers:b,fetchBookings:async()=>{p(!0),f(null);try{const e=await As.get(Ms.BOOKINGS);m(e.data)}catch(e){f("Failed to fetch bookings")}finally{p(!1)}},fetchGpsRequests:v,updateUserRole:async(e,s)=>{try{await As.post(`/admin/users/${e}/update-role`,{role:s}),await y()}catch(t){throw new Error("Failed to update user role")}},verifyDriverLicense:async(e,s)=>{try{await As.post(`/admin/drivers/${e}/verify-license`,{status:s}),await b()}catch(t){throw new Error("Failed to verify driver license")}},updateGpsRequestStatus:async(e,s,t)=>{try{await As.post(`/admin/gps-requests/${e}/update-status`,{status:s,admin_notes:t}),await v()}catch(r){throw new Error("Failed to update GPS request status")}}};return j.jsx(tt.Provider,{value:N,children:s})};
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var at={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=(s,t)=>{const r=e.forwardRef((({color:r="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:n="",children:c,...o},d)=>{return e.createElement("svg",{ref:d,...at,width:a,height:a,stroke:r,strokeWidth:l?24*Number(i)/Number(a):i,className:["lucide",`lucide-${m=s,m.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,n].join(" "),...o},[...t.map((([s,t])=>e.createElement(s,t))),...Array.isArray(c)?c:[c]]);var m}));return r.displayName=`${s}`,r},lt=it("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),nt=it("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ct=it("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),ot=it("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),dt=it("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]),mt=it("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),xt=it("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),ut=it("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),ht=it("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),pt=it("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),gt=it("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),ft=it("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),yt=it("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),jt=it("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),bt=it("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),vt=it("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),Nt=it("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),wt=it("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),kt=it("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),St=it("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),Ct=it("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),At=it("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),Pt=it("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),Et=it("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),_t=it("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),zt=it("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Rt=it("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Tt=it("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),Dt=it("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Lt=it("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),It=it("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Ot=it("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),Ft=it("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]),qt=it("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),Ut=it("ToggleLeft",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"8",cy:"12",r:"2",key:"1nvbw3"}]]),Bt=it("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Mt=it("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),Ht=it("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),$t=it("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]]),Vt=it("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Yt=it("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Wt=it("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Gt=it("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Jt=({variant:e="primary",size:s="md",className:t="",onClick:r,disabled:a=!1,type:i="button",children:l,fullWidth:n=!1})=>{const c=`inline-flex items-center justify-center font-medium transition-colors duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500",accent:"bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500",outline:"bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[e]} ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[s]} ${n?"w-full":""} ${a?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${t}`;return j.jsx("button",{type:i,className:c,onClick:r,disabled:a,children:l})},Kt=()=>{const{user:s,isAuthenticated:a,logout:i}=$s(),l=t(),[n,c]=e.useState(!1),o=()=>{i(),l("/"),c(!1)},d=[{name:"Home",path:"/",icon:j.jsx(Nt,{size:18})},{name:"Browse Cars",path:"/cars",icon:j.jsx(xt,{size:18})},{name:"Hire a Driver",path:"/drivers",icon:j.jsx($t,{size:18})}],m=()=>{if(!s)return{name:"My Account",path:"/account",icon:j.jsx(Vt,{size:18})};switch(s.role){case"admin":return{name:"Admin Dashboard",path:"/admin",icon:j.jsx(Vt,{size:18})};case"owner":return{name:"Owner Dashboard",path:"/owner/dashboard",icon:j.jsx(xt,{size:18})};case"driver":return{name:"Driver Dashboard",path:"/driver/dashboard",icon:j.jsx($t,{size:18})};default:return{name:"My Account",path:"/account",icon:j.jsx(Vt,{size:18})}}};null==s||s.role;const x=a?[m()]:[{name:"Log In",path:"/login",icon:j.jsx(Vt,{size:18})},{name:"Sign Up",path:"/signup",icon:j.jsx(Vt,{size:18})}];return j.jsxs("header",{className:"bg-white shadow-md sticky top-0 z-50",children:[j.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"flex justify-between items-center py-4",children:[j.jsxs(r,{to:"/",className:"flex items-center space-x-2",children:[j.jsx(xt,{className:"h-8 w-8 text-primary-600"}),j.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),j.jsxs("nav",{className:"hidden md:flex items-center space-x-1",children:[d.map((e=>j.jsx(r,{to:e.path,className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors",children:e.name},e.path))),a?j.jsxs(j.Fragment,{children:[j.jsx(r,{to:m().path,className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors",children:m().name}),j.jsxs(Jt,{variant:"outline",size:"sm",onClick:o,className:"ml-2",children:[j.jsx(St,{size:16,className:"mr-1"}),"Log Out"]})]}):j.jsxs(j.Fragment,{children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{variant:"outline",size:"sm",className:"ml-2",children:"Log In"})}),j.jsx(r,{to:"/signup",children:j.jsx(Jt,{size:"sm",className:"ml-2",children:"Sign Up"})})]})]}),j.jsx("button",{className:"md:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100 focus:outline-none",onClick:()=>{c(!n)},children:n?j.jsx(Gt,{size:24}):j.jsx(Pt,{size:24})})]})}),n&&j.jsx("div",{className:"md:hidden animate-fade-in",children:j.jsxs("div",{className:"px-2 pt-2 pb-4 space-y-1 sm:px-3 border-t border-gray-200",children:[d.map((e=>j.jsxs(r,{to:e.path,className:"flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:()=>c(!1),children:[e.icon,j.jsx("span",{className:"ml-2",children:e.name})]},e.path))),x.map((e=>j.jsxs(r,{to:e.path,className:"flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:()=>c(!1),children:[e.icon,j.jsx("span",{className:"ml-2",children:e.name})]},e.path))),a&&j.jsxs("button",{className:"flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:o,children:[j.jsx(St,{size:18}),j.jsx("span",{className:"ml-2",children:"Log Out"})]})]})})]})},Zt=()=>j.jsx("footer",{className:"bg-gray-900 text-gray-300",children:j.jsxs("div",{className:"container mx-auto px-4 py-12",children:[j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[j.jsxs("div",{children:[j.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[j.jsx(xt,{className:"h-6 w-6 text-primary-500"}),j.jsx("span",{className:"text-lg font-bold text-white",children:"Park & Rent"})]}),j.jsx("p",{className:"mb-4 text-sm",children:"Connecting car owners with people who need short-term rentals. Our platform facilitates discovery and contact, while letting you handle the details directly."}),j.jsxs("div",{className:"flex space-x-4",children:[j.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Facebook",children:j.jsx(bt,{size:20})}),j.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Twitter",children:j.jsx(Mt,{size:20})}),j.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Instagram",children:j.jsx(kt,{size:20})})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Quick Links"}),j.jsxs("ul",{className:"space-y-2",children:[j.jsx("li",{children:j.jsx(r,{to:"/",className:"text-gray-400 hover:text-white transition-colors",children:"Home"})}),j.jsx("li",{children:j.jsx(r,{to:"/cars",className:"text-gray-400 hover:text-white transition-colors",children:"Browse Cars"})}),j.jsx("li",{children:j.jsx(r,{to:"/login",className:"text-gray-400 hover:text-white transition-colors",children:"Log In"})}),j.jsx("li",{children:j.jsx(r,{to:"/signup",className:"text-gray-400 hover:text-white transition-colors",children:"Sign Up"})})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Legal"}),j.jsxs("ul",{className:"space-y-2",children:[j.jsx("li",{children:j.jsx(r,{to:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})}),j.jsx("li",{children:j.jsx(r,{to:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),j.jsx("li",{children:j.jsx(r,{to:"/faq",className:"text-gray-400 hover:text-white transition-colors",children:"FAQ"})}),j.jsx("li",{children:j.jsx(r,{to:"/help",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact Us"}),j.jsxs("ul",{className:"space-y-3",children:[j.jsxs("li",{className:"flex items-start",children:[j.jsx(_t,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"**********"})]}),j.jsxs("li",{className:"flex items-start",children:[j.jsx(Ct,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"<EMAIL>"})]}),j.jsxs("li",{className:"flex items-start",children:[j.jsx(wt,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"Park & Rent facilitates connections only. All bookings, payments, and arrangements are handled directly between users."})]})]})]})]}),j.jsxs("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["© ",(new Date).getFullYear()," Park & Rent. All rights reserved."]}),j.jsxs("div",{className:"flex items-center mt-4 md:mt-0",children:[j.jsx(Ot,{size:16,className:"text-gray-400 mr-1"}),j.jsx("span",{className:"text-sm text-gray-500",children:"Secure Connection"})]})]})]})}),Xt=({children:e})=>j.jsxs("div",{className:"flex flex-col min-h-screen",children:[j.jsx(Kt,{}),j.jsx("main",{className:"flex-grow",children:e}),j.jsx(Zt,{})]}),Qt=Object.assign((({className:e="",children:s,onClick:t,hover:r=!0})=>{const a=`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 ${r?"hover:shadow-lg transform hover:-translate-y-1":""} ${t?"cursor-pointer":""} ${e}`;return j.jsx("div",{className:a,onClick:t,children:s})}),{Image:({src:e,alt:s,className:t=""})=>j.jsx("div",{className:`aspect-video w-full overflow-hidden ${t}`,children:j.jsx("img",{src:e,alt:s,className:"w-full h-full object-cover",onError:e=>{const s=e.target;s.style.display="none";const t=s.parentElement;if(t&&!t.querySelector(".image-fallback")){const e=document.createElement("div");e.className="image-fallback w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm",e.textContent="Image Not Available",t.appendChild(e)}}})}),Content:({className:e="",children:s})=>j.jsx("div",{className:`p-4 ${e}`,children:s}),Title:({className:e="",children:s})=>j.jsx("h3",{className:`font-bold text-lg mb-2 ${e}`,children:s}),Footer:({className:e="",children:s})=>j.jsx("div",{className:`px-4 py-3 bg-gray-50 border-t border-gray-100 ${e}`,children:s})}),er=(e,s=!0)=>e.toLocaleString("en-RW",{maximumFractionDigits:0,minimumFractionDigits:0}),sr=(e,s)=>`${er(e)}/${s.toLowerCase()}`,tr=({size:e=16,className:s=""})=>j.jsx("div",{className:`inline-flex items-center justify-center ${s}`,children:j.jsx("span",{className:"font-medium",style:{fontSize:.8*e+"px",lineHeight:1},children:"₣"})}),rr=({car:e})=>j.jsxs(Qt,{className:"h-full flex flex-col",children:[j.jsx(Qt.Image,{src:e.images[0],alt:`${e.make} ${e.model}`}),j.jsxs(Qt.Content,{className:"flex-grow",children:[j.jsxs(Qt.Title,{children:[e.year," ",e.make," ",e.model]}),j.jsxs("div",{className:"mb-4 space-y-2",children:[j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(At,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{className:"text-sm truncate",children:e.location})]}),j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(ft,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{className:"text-sm truncate",children:e.availabilityNotes})]}),j.jsxs("div",{className:"flex items-center font-medium text-primary-700",children:[j.jsx(tr,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{children:sr(e.pricePerHour,"hour")})]})]}),j.jsx("div",{className:"text-sm text-gray-600 line-clamp-2 mb-4",children:e.description})]}),j.jsx(Qt.Footer,{className:"mt-auto",children:j.jsx(r,{to:`/cars/${e.id}`,className:"w-full",children:j.jsx(Jt,{variant:"primary",fullWidth:!0,children:"Book Now"})})})]}),ar=({driver:e})=>j.jsxs(Qt,{className:"h-full flex flex-col",children:[j.jsxs("div",{className:"relative",children:[j.jsx(Qt.Image,{src:e.profileImage,alt:e.name,className:"h-64 object-cover"}),j.jsxs("div",{className:"absolute bottom-2 right-2 bg-white px-2 py-1 rounded-full flex items-center shadow-md",children:[j.jsx(qt,{size:16,className:"text-yellow-500 mr-1"}),j.jsx("span",{className:"font-medium",children:Number(e.rating||0).toFixed(1)}),j.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(",e.reviews||0,")"]})]})]}),j.jsxs(Qt.Content,{className:"flex-grow",children:[j.jsx(Qt.Title,{children:e.name}),j.jsxs("div",{className:"flex items-center mb-2",children:[j.jsx(ot,{size:16,className:"text-primary-600 mr-1"}),j.jsxs("span",{className:"text-sm",children:[e.experience," years experience"]})]}),j.jsxs("div",{className:"mb-4 space-y-2",children:[j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(At,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{className:"text-sm truncate",children:e.location})]}),j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(ft,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{className:"text-sm truncate",children:e.availabilityNotes})]}),j.jsxs("div",{className:"flex items-center font-medium text-primary-700",children:[j.jsx(tr,{size:16,className:"mr-1 flex-shrink-0"}),j.jsx("span",{children:sr(e.pricePerHour,"hour")})]})]}),j.jsx("div",{className:"flex flex-wrap gap-1 mb-4",children:e.specialties.map(((e,s)=>j.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-2 py-1 text-xs font-medium text-gray-700",children:e},s)))})]}),j.jsx(Qt.Footer,{className:"mt-auto",children:j.jsx(r,{to:`/drivers/${e.id}`,className:"w-full",children:j.jsx(Jt,{variant:"primary",fullWidth:!0,children:"Book Now"})})})]}),ir=()=>{const{activeCars:e}=Ws(),{filteredDrivers:s}=Zs(),t=e.slice(0,3),a=s.slice(0,3);return j.jsxs(Xt,{children:[j.jsxs("section",{className:"relative bg-gradient-to-r from-primary-900 to-primary-700 text-white",children:[j.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[j.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50"}),j.jsx("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"url('https://images.pexels.com/photos/170811/pexels-photo-170811.jpeg')",backgroundSize:"cover",backgroundPosition:"center"}})]}),j.jsx("div",{className:"container mx-auto px-4 py-16 md:py-24 relative z-10",children:j.jsxs("div",{className:"max-w-3xl",children:[j.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4 leading-tight",children:"Rent Cars Directly From Local Owners"}),j.jsx("p",{className:"text-xl mb-8 text-gray-100",children:"Find affordable rentals from car owners in your area. No middlemen, no hidden fees — just direct peer-to-peer car rentals."}),j.jsxs("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4",children:[j.jsx(r,{to:"/cars",children:j.jsx(Jt,{size:"lg",className:"w-full sm:w-auto",children:"Browse Available Cars"})}),j.jsx(r,{to:"/signup",children:j.jsx(Jt,{variant:"outline",size:"lg",className:"w-full sm:w-auto !bg-white !bg-opacity-10 hover:!bg-opacity-20 !border-white !text-white hover:!text-gray-900",children:"List Your Car"})})]})]})})]}),j.jsx("section",{className:"py-16 bg-white",children:j.jsxs("div",{className:"container mx-auto px-4",children:[j.jsxs("div",{className:"text-center mb-12",children:[j.jsx("h2",{className:"text-3xl font-bold mb-3",children:"How It Works"}),j.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Park & Rent connects car owners with people who need short-term rentals in a simple, transparent way."})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[j.jsxs("div",{className:"text-center p-6 rounded-lg",children:[j.jsx(Dt,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),j.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Find a Car"}),j.jsx("p",{className:"text-gray-600",children:"Browse our selection of available cars in your area. Filter by location, price, and features to find your perfect match."})]}),j.jsxs("div",{className:"text-center p-6 rounded-lg",children:[j.jsx(Yt,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),j.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Connect Directly"}),j.jsx("p",{className:"text-gray-600",children:"Once verified, contact car owners directly through our platform. Arrange pickup times and locations that work for both of you."})]}),j.jsxs("div",{className:"text-center p-6 rounded-lg",children:[j.jsx(Ot,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),j.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Safe & Simple"}),j.jsx("p",{className:"text-gray-600",children:"Handle payments directly with the owner. We verify drivers and owners to ensure a safe and trustworthy experience."})]})]})]})}),j.jsx("section",{className:"py-16 bg-gray-50",children:j.jsxs("div",{className:"container mx-auto px-4",children:[j.jsxs("div",{className:"flex justify-between items-center mb-8",children:[j.jsx("h2",{className:"text-3xl font-bold",children:"Featured Cars"}),j.jsx(r,{to:"/cars",children:j.jsx(Jt,{variant:"outline",children:"View All Cars"})})]}),t.length>0?j.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map((e=>j.jsx(rr,{car:e},e.id)))}):j.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[j.jsx(xt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars available right now"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"Check back soon or be the first to list your car!"}),j.jsx(r,{to:"/signup",children:j.jsx(Jt,{variant:"primary",children:"List Your Car"})})]})]})}),j.jsx("section",{className:"py-16 bg-secondary-700 text-white",children:j.jsx("div",{className:"container mx-auto px-4",children:j.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[j.jsx(xt,{className:"h-16 w-16 mx-auto mb-6 text-white opacity-80"}),j.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Have a Car Sitting Idle?"}),j.jsx("p",{className:"text-xl mb-8",children:"Turn your parked car into extra income. List your vehicle and connect with people who need short-term rentals in your area."}),j.jsx(r,{to:"/signup",children:j.jsx(Jt,{size:"lg",className:"!bg-white !text-gray-900 hover:!bg-gray-100 hover:!text-gray-800 !font-semibold border border-gray-300",children:"Start Listing Today"})})]})})}),j.jsx("section",{className:"py-16 bg-white",children:j.jsxs("div",{className:"container mx-auto px-4",children:[j.jsxs("div",{className:"text-center mb-12",children:[j.jsx("h2",{className:"text-3xl font-bold mb-3",children:"Available Across Rwanda"}),j.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Find cars in these popular locations and many more across Rwanda."})]}),j.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4",children:["Kigali","Butare","Gisenyi","Ruhengeri","Cyangugu","Kibuye","Byumba","Kibungo","Gitarama","Nyanza"].map((e=>j.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-center",children:[j.jsx(At,{className:"h-5 w-5 text-primary-600 mx-auto mb-2"}),j.jsx("span",{className:"font-medium",children:e})]},e)))})]})}),j.jsx("section",{className:"py-12 bg-gray-50",children:j.jsxs("div",{className:"container mx-auto px-4",children:[j.jsxs("div",{className:"flex justify-between items-center mb-8",children:[j.jsxs("div",{children:[j.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Featured Drivers"}),j.jsx("p",{className:"text-gray-600",children:"Professional drivers ready to help you get around"})]}),j.jsx(r,{to:"/drivers",children:j.jsx(Jt,{variant:"outline",children:"View All Drivers"})})]}),a.length>0?j.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map((e=>j.jsx(ar,{driver:e},e.id)))}):j.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[j.jsx($t,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers available right now"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"Check back soon or register as a driver yourself!"}),j.jsx(r,{to:"/driver/register",children:j.jsx(Jt,{variant:"primary",children:"Register as Driver"})})]})]})})]})},lr=({onSearch:s})=>{const[t,r]=e.useState(""),[a,i]=e.useState(!1);return j.jsxs("div",{className:"mb-6",children:[j.jsxs("form",{onSubmit:e=>{e.preventDefault(),s(t)},className:"flex items-center mb-4",children:[j.jsxs("div",{className:"relative flex-grow",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(Dt,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"text",placeholder:"Search cars by make, model, or location...",value:t,onChange:e=>r(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"})]}),j.jsx(Jt,{type:"submit",className:"rounded-l-none",children:"Search"}),j.jsxs(Jt,{type:"button",variant:"outline",className:"ml-2 flex items-center md:hidden",onClick:()=>{i(!a)},children:[j.jsx(vt,{size:18,className:"mr-1"}),"Filters"]})]}),j.jsxs("div",{className:`md:block ${a?"block":"hidden"} bg-white p-4 rounded-lg shadow-sm mb-4 border border-gray-200`,children:[j.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Filter Cars"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"price-range",className:"block text-sm font-medium text-gray-700 mb-1",children:"Price Range (per hour)"}),j.jsxs("select",{id:"price-range",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"Any Price"}),j.jsx("option",{value:"0-5000",children:"₣ 0 - 5,000"}),j.jsx("option",{value:"5000-10000",children:"₣ 5,000 - 10,000"}),j.jsx("option",{value:"10000-20000",children:"₣ 10,000 - 20,000"}),j.jsx("option",{value:"20000+",children:"₣ 20,000+"})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"car-type",className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Type"}),j.jsxs("select",{id:"car-type",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"All Types"}),j.jsx("option",{value:"sedan",children:"Sedan"}),j.jsx("option",{value:"suv",children:"SUV"}),j.jsx("option",{value:"truck",children:"Truck"}),j.jsx("option",{value:"luxury",children:"Luxury"}),j.jsx("option",{value:"electric",children:"Electric"})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"availability",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability"}),j.jsxs("select",{id:"availability",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"Any Time"}),j.jsx("option",{value:"weekdays",children:"Weekdays"}),j.jsx("option",{value:"weekends",children:"Weekends"}),j.jsx("option",{value:"all-day",children:"All Day"})]})]})]}),j.jsxs("div",{className:"mt-4 flex justify-end",children:[j.jsx(Jt,{type:"button",variant:"outline",size:"sm",className:"mr-2",onClick:()=>{i(!1)},children:"Reset"}),j.jsx(Jt,{type:"button",size:"sm",onClick:()=>{i(!1)},children:"Apply Filters"})]})]})]})},nr=({size:e="md",className:s="",color:t="text-primary-600"})=>j.jsx("div",{className:`${{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e]} ${t} ${s}`,children:j.jsxs("svg",{className:"animate-spin -ml-1 mr-3 w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[j.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),j.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),cr=()=>{const{filteredCars:s,isLoading:t,error:r,fetchCars:a,filterCars:i}=Ws();e.useEffect((()=>{window.scrollTo(0,0)}),[]);return t?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"flex justify-center items-center h-64",children:j.jsx(nr,{size:"lg"})})})}):r?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error Loading Cars"}),j.jsx("p",{className:"text-error-600",children:r})]})})}):j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"mb-6",children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Available Cars"}),j.jsx("p",{className:"text-gray-600",children:"Browse our selection of available cars for rent from local owners."})]}),j.jsx(lr,{onSearch:e=>{i(e)}}),s.length>0?j.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map((e=>j.jsx(rr,{car:e},e.id)))}):j.jsxs("div",{className:"text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200",children:[j.jsx(xt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars match your search"}),j.jsx("p",{className:"text-gray-600",children:"Try adjusting your filters or search term to find available cars."})]})]})})},or=({images:s,alt:t})=>{const[r,a]=e.useState(0);return 0===s.length?j.jsx("div",{className:"w-full h-96 bg-gray-200 flex items-center justify-center rounded-lg",children:j.jsx("span",{className:"text-gray-500",children:"No images available"})}):1===s.length?j.jsx("div",{className:"w-full rounded-lg overflow-hidden",children:j.jsx("img",{src:s[0],alt:t,className:"w-full h-96 object-cover",onError:e=>{const s=e.target;s.style.display="none";const t=s.parentElement;if(t&&!t.querySelector(".image-fallback")){const e=document.createElement("div");e.className="image-fallback w-full h-96 bg-gray-200 flex items-center justify-center text-gray-500 text-lg",e.textContent="Image Not Available",t.appendChild(e)}}})}):j.jsxs("div",{className:"relative w-full h-96",children:[j.jsx("div",{className:"absolute top-0 left-0 right-0 bottom-0 w-full h-full rounded-lg overflow-hidden",children:j.jsx("img",{src:s[r],alt:`${t} - Image ${r+1}`,className:"absolute w-full h-full object-cover transition-opacity duration-300",onError:e=>{const s=e.target;s.style.display="none";const t=s.parentElement;if(t&&!t.querySelector(".image-fallback")){const e=document.createElement("div");e.className="image-fallback w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-lg",e.textContent="Image Not Available",t.appendChild(e)}}})}),j.jsx("div",{className:"absolute top-1/2 left-4 -translate-y-1/2",children:j.jsx("button",{onClick:()=>{const e=0===r?s.length-1:r-1;a(e)},className:"p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all","aria-label":"Previous image",children:j.jsx(pt,{size:24})})}),j.jsx("div",{className:"absolute top-1/2 right-4 -translate-y-1/2",children:j.jsx("button",{onClick:()=>{const e=r===s.length-1;a(e?0:r+1)},className:"p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all","aria-label":"Next image",children:j.jsx(gt,{size:24})})}),j.jsx("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2",children:s.map(((e,s)=>j.jsx("button",{onClick:()=>(e=>{a(e)})(s),className:"h-2.5 w-2.5 rounded-full transition-all "+(s===r?"bg-white w-5":"bg-white bg-opacity-50"),"aria-label":`Go to image ${s+1}`},s)))})]})},dr=({hourlyRate:e})=>{const s=8*e,t=5*s,r=4*t,a=[{period:"Hour",rate:e,icon:j.jsx(ft,{size:18})},{period:"Day",rate:s,icon:j.jsx(mt,{size:18})},{period:"Week",rate:t,icon:j.jsx(mt,{size:18})},{period:"Month",rate:r,icon:j.jsx(mt,{size:18})}];return j.jsxs("div",{className:"mt-4",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Pricing"}),j.jsx("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-4",children:a.map((e=>j.jsxs("div",{className:"bg-white p-3 rounded-lg border border-gray-200 shadow-sm",children:[j.jsxs("div",{className:"flex items-center text-sm text-gray-500 mb-1",children:[e.icon,j.jsxs("span",{className:"ml-1",children:["Per ",e.period]})]}),j.jsxs("div",{className:"flex items-center",children:[j.jsx(tr,{size:18,className:"text-primary-600"}),j.jsx("span",{className:"text-xl font-semibold text-gray-900",children:er(e.rate)})]})]},e.period)))}),j.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Actual rates may vary. Discuss with the owner for exact pricing."})]})},mr=({ownerPhone:s="**********",ownerId:t,buttonStyle:r="full"})=>{const{user:a,isAuthenticated:i}=$s(),[l,n]=e.useState(!1),c=()=>{i&&"client"===(null==a?void 0:a.role)&&"verified"===(null==a?void 0:a.licenseVerificationStatus)&&n(!0)};return i?"client"!==(null==a?void 0:a.role)?j.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:j.jsxs("div",{className:"flex items-start",children:[j.jsx(lt,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),j.jsxs("div",{className:"ml-3",children:[j.jsx("h3",{className:"font-medium text-yellow-800",children:"Owner account detected"}),j.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"You need a client account to contact car owners."})]})]})}):"verified"!==(null==a?void 0:a.licenseVerificationStatus)?j.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:j.jsxs("div",{className:"flex items-start",children:[j.jsx(lt,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),j.jsxs("div",{className:"ml-3",children:[j.jsx("h3",{className:"font-medium text-yellow-800",children:"License verification required"}),j.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Your driving license needs to be verified before you can contact car owners."}),j.jsx("div",{className:"mt-3",children:j.jsx(Jt,{variant:"primary",onClick:()=>window.location.href="/account/verification",children:"Verify License"})})]})]})}):"compact"===r?j.jsxs(Jt,{variant:"outline",onClick:c,className:"flex-1 flex items-center justify-center",disabled:l,children:[j.jsx(_t,{size:18,className:"mr-2"}),l?s:"Show Contact"]}):j.jsxs("div",{className:"mt-4",children:[l?j.jsx("div",{className:"p-4 bg-success-50 border border-success-200 rounded-lg",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx(_t,{size:20,className:"text-success-500 flex-shrink-0"}),j.jsxs("div",{className:"ml-3",children:[j.jsx("h3",{className:"font-medium text-success-800",children:"Contact the owner"}),j.jsx("p",{className:"text-lg font-medium text-success-700 mt-1",children:s}),j.jsx("p",{className:"text-sm text-success-600 mt-2",children:"Call or text the owner to arrange rental details directly."})]})]})}):j.jsxs(Jt,{variant:"primary",onClick:c,className:"w-full flex items-center justify-center",children:[j.jsx(_t,{size:18,className:"mr-2"}),"Show Phone Number"]}),j.jsxs("p",{className:"text-sm text-gray-500 mt-2",children:[j.jsx(lt,{size:14,className:"inline mr-1"}),"We only connect you with the owner. All arrangements and payments are handled directly between you and the owner."]})]}):j.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:j.jsxs("div",{className:"flex items-start",children:[j.jsx(lt,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),j.jsxs("div",{className:"ml-3",children:[j.jsx("h3",{className:"font-medium text-yellow-800",children:"Authentication required"}),j.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Please log in to contact the car owner."}),j.jsx("div",{className:"mt-3",children:j.jsx(Jt,{variant:"primary",onClick:()=>window.location.href="/login",children:"Log In"})})]})]})})},xr=({recipientId:s,recipientName:t,itemType:r,itemId:a})=>{const{user:i,isAuthenticated:l}=$s(),[n,c]=e.useState([]),[o,d]=e.useState(""),[m,x]=e.useState(!1);e.useEffect((()=>{c([])}),[s,t,null==i?void 0:i.id]);return j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden border border-gray-200",children:[j.jsxs("div",{className:"bg-primary-50 p-4 border-b border-gray-200",children:[j.jsxs("h3",{className:"font-medium text-primary-800",children:["Chat with ",t]}),j.jsx("p",{className:"text-sm text-gray-600",children:"Ask questions before booking"})]}),j.jsxs("div",{className:"h-64 overflow-y-auto p-4 space-y-3",children:[n.map((e=>j.jsx("div",{className:"flex "+(e.senderId===(null==i?void 0:i.id)?"justify-end":"justify-start"),children:j.jsxs("div",{className:"max-w-[80%] rounded-lg px-3 py-2 "+(e.senderId===(null==i?void 0:i.id)?"bg-primary-100 text-primary-800":"bg-gray-100 text-gray-800"),children:[j.jsx("div",{className:"text-sm",children:e.text}),j.jsx("div",{className:"text-xs text-gray-500 mt-1",children:new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},e.id))),0===n.length&&j.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No messages yet. Start the conversation!"})]}),j.jsx("form",{onSubmit:async e=>{var t;if(e.preventDefault(),o.trim()&&l&&i){x(!0);try{const e=await As.post(Os,{recipient_id:s,related_to_type:r,related_to_id:a,initial_message:o.trim()});if(e.data){const r={id:(null==(t=e.data.new_message)?void 0:t.id)||`msg-${Date.now()}`,senderId:i.id,receiverId:s,text:o,timestamp:(new Date).toISOString(),isRead:!1};c((e=>[...e,r])),d("")}}catch(n){}finally{x(!1)}}},className:"p-3 border-t border-gray-200",children:l?j.jsxs("div",{className:"flex",children:[j.jsx("input",{type:"text",placeholder:"Type your message...",className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:o,onChange:e=>d(e.target.value),disabled:m}),j.jsx(Jt,{type:"submit",className:"rounded-l-none",disabled:m||!o.trim(),children:j.jsx(Lt,{size:18})})]}):j.jsxs("div",{className:"text-center py-2 bg-gray-50 rounded-md",children:[j.jsx(Vt,{size:18,className:"inline-block mr-2 text-gray-500"}),j.jsx("span",{className:"text-sm text-gray-600",children:"Please log in to send messages"})]})})]})},ur=()=>{const{id:s}=a(),r=t(),{getCarById:i,selectedCar:l,isLoading:n,error:c}=Ws(),{createBooking:o}=et(),{user:d,isAuthenticated:m}=$s(),[x,u]=e.useState(!1),[h,p]=e.useState(1),[g,f]=e.useState(""),[y,b]=e.useState(""),[v,N]=e.useState(!1),[w,k]=e.useState(null),[S,C]=e.useState(!1),A=()=>{r(-1)};return e.useEffect((()=>{window.scrollTo(0,0),s&&i(s)}),[s,i]),m?n?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"flex justify-center items-center h-64",children:j.jsx(nr,{size:"lg"})})})}):c||!l?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:A,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to listings"]}),j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Car Not Found"}),j.jsx("p",{className:"text-error-600",children:"The car you're looking for doesn't exist or has been removed."})]})]})}):j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:A,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to listings"]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[j.jsxs("div",{className:"md:flex",children:[j.jsx("div",{className:"md:w-2/3",children:j.jsx(or,{images:l.images,alt:`${l.year} ${l.make} ${l.model}`})}),j.jsxs("div",{className:"md:w-1/3 p-6 border-l border-gray-200",children:[j.jsxs("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:[l.year," ",l.make," ",l.model]}),j.jsxs("div",{className:"flex items-center text-gray-600 mb-4",children:[j.jsx(At,{size:18,className:"flex-shrink-0 mr-2"}),j.jsx("span",{children:l.location})]}),j.jsx("div",{className:"mb-4 pb-4 border-b border-gray-200",children:j.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[j.jsx(mt,{size:18,className:"mr-2 flex-shrink-0"}),j.jsxs("span",{children:["Availability: ",l.availabilityNotes]})]})}),j.jsx(dr,{hourlyRate:l.pricePerHour}),j.jsx("div",{className:"flex space-x-2 mt-4",children:j.jsx(mr,{ownerId:l.ownerId})})]})]}),j.jsxs("div",{className:"p-6 border-t border-gray-200",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"About This Car"}),j.jsx("p",{className:"text-gray-700 mb-6",children:l.description}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Features"}),j.jsx("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 mb-6",children:l.features.map(((e,s)=>j.jsxs("li",{className:"flex items-center text-gray-700",children:[j.jsx(wt,{size:16,className:"text-primary-600 mr-2 flex-shrink-0"}),e]},s)))}),j.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-2",children:"Important Information"}),j.jsx("p",{className:"text-yellow-700",children:"Park & Rent only facilitates the connection between car owners and renters. All arrangements, payments, and transactions are handled directly between users. We recommend chatting with the owner first, verifying insurance coverage, and discussing details thoroughly before booking."})]}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Chat with Owner"}),j.jsx(Jt,{variant:"outline",size:"sm",onClick:()=>u(!x),children:x?"Hide Chat":"Show Chat"})]}),x?j.jsx(xr,{recipientId:l.ownerId,recipientName:"Car Owner",itemType:"car",itemId:l.id}):j.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6 text-center",children:[j.jsx(Et,{size:32,className:"mx-auto text-gray-400 mb-2"}),j.jsx("p",{className:"text-gray-600",children:"Chat with the car owner to ask questions or discuss details before booking."})]})]}),j.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Book this Car"}),S?j.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 text-center",children:[j.jsx("h3",{className:"text-lg font-medium text-success-800 mb-2",children:"Booking Request Sent!"}),j.jsx("p",{className:"text-success-600 mb-4",children:"Your booking request has been submitted. The car owner will contact you soon to arrange payment and details."}),j.jsx(Jt,{onClick:()=>r("/account"),variant:"primary",children:"View My Bookings"})]}):j.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),m&&d){N(!0),k(null);try{const e=new Date(`${g}T${y}`),s=new Date(e.getTime()+60*h*60*1e3),t=l.pricePerHour*h;o({userId:d.id,itemType:"car",itemId:l.id,startTime:e.toISOString(),endTime:s.toISOString(),totalPrice:t,status:"pending"}).then((()=>{C(!0),N(!1)}))}catch(s){k("Failed to create booking. Please try again."),N(!1)}}else r("/login")},className:"space-y-4",children:[j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),j.jsx("input",{type:"date",id:"booking-date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,min:(new Date).toISOString().split("T")[0],value:g,onChange:e=>f(e.target.value)})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-time",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time"}),j.jsx("input",{type:"time",id:"booking-time",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,value:y,onChange:e=>b(e.target.value)})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-hours",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),j.jsx("select",{id:"booking-hours",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:h,onChange:e=>p(Number(e.target.value)),children:[1,2,3,4,5,6,7,8].map((e=>j.jsxs("option",{value:e,children:[e," ",1===e?"hour":"hours"]},e)))})]})]}),j.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[j.jsxs("div",{className:"flex justify-between mb-2",children:[j.jsx("span",{children:"Rate per hour"}),j.jsx("span",{children:er(l.pricePerHour)})]}),j.jsxs("div",{className:"flex justify-between mb-2",children:[j.jsx("span",{children:"Hours"}),j.jsx("span",{children:h})]}),j.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[j.jsx("span",{children:"Total"}),j.jsx("span",{children:er(l.pricePerHour*h)})]})]}),w&&j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm",children:w}),j.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4",children:j.jsxs("p",{children:[j.jsx("strong",{children:"Note:"})," This is just a booking request. No payment is required on the platform. You'll arrange payment directly with the car owner."]})}),j.jsxs(Jt,{type:"submit",fullWidth:!0,disabled:v||!g||!y,children:[v?j.jsx(nr,{size:"sm",className:"mr-2"}):null,"Request Booking"]}),!m&&j.jsx("div",{className:"text-sm text-center text-gray-500",children:"You'll need to log in to complete your booking."})]})]})]})]})]})}):j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:A,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to listings"]}),j.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-yellow-800 mb-2",children:"Login Required"}),j.jsx("p",{className:"text-yellow-700 mb-6",children:"You must be logged in to view car details and make bookings."}),j.jsxs("div",{className:"flex justify-center space-x-4",children:[j.jsx(Jt,{onClick:()=>r("/login"),children:"Log In"}),j.jsx(Jt,{variant:"outline",onClick:()=>r("/signup"),children:"Sign Up"})]})]})]})})},hr=({onSearch:s})=>{const[t,r]=e.useState(""),[a,i]=e.useState(!1);return j.jsx("div",{className:"bg-white rounded-lg shadow-md p-4 mb-6",children:j.jsxs("form",{onSubmit:e=>{e.preventDefault(),s(t)},children:[j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsxs("div",{className:"relative flex-grow",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(Dt,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"text",placeholder:"Search by name, location, or specialty...",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:t,onChange:e=>r(e.target.value)})]}),j.jsx(Jt,{type:"submit",className:"ml-3",children:"Search"}),j.jsxs(Jt,{type:"button",variant:"outline",className:"ml-2 flex items-center",onClick:()=>{i(!a)},children:[j.jsx(vt,{size:18,className:"mr-1"}),"Filters"]})]}),a&&j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"min-experience",className:"block text-sm font-medium text-gray-700 mb-1",children:"Min. Experience (years)"}),j.jsxs("select",{id:"min-experience",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"Any"}),j.jsx("option",{value:"1",children:"1+ years"}),j.jsx("option",{value:"3",children:"3+ years"}),j.jsx("option",{value:"5",children:"5+ years"}),j.jsx("option",{value:"10",children:"10+ years"})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"specialty",className:"block text-sm font-medium text-gray-700 mb-1",children:"Specialty"}),j.jsxs("select",{id:"specialty",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"All Specialties"}),j.jsx("option",{value:"city-driving",children:"City Driving"}),j.jsx("option",{value:"long-distance",children:"Long Distance"}),j.jsx("option",{value:"airport-transfers",children:"Airport Transfers"}),j.jsx("option",{value:"event-transportation",children:"Event Transportation"}),j.jsx("option",{value:"luxury-vehicles",children:"Luxury Vehicles"})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"rating",className:"block text-sm font-medium text-gray-700 mb-1",children:"Min. Rating"}),j.jsxs("select",{id:"rating",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[j.jsx("option",{value:"",children:"Any Rating"}),j.jsx("option",{value:"3",children:"3+ Stars"}),j.jsx("option",{value:"4",children:"4+ Stars"}),j.jsx("option",{value:"4.5",children:"4.5+ Stars"})]})]})]})]})})},pr=()=>{const{filteredDrivers:s,isLoading:t,error:r,fetchDrivers:a,filterDrivers:i}=Zs();e.useEffect((()=>{window.scrollTo(0,0)}),[]);const l=()=>{a()};return t?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"flex justify-center items-center h-64",children:j.jsx(nr,{size:"lg"})})})}):r?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-6 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error Loading Drivers"}),j.jsx("p",{className:"text-error-600 mb-4",children:r}),j.jsxs(Jt,{onClick:l,disabled:t,className:"inline-flex items-center gap-2",children:[j.jsx(Rt,{className:"h-4 w-4 "+(t?"animate-spin":"")}),t?"Retrying...":"Try Again"]})]})})}):j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"mb-6 flex justify-between items-start",children:[j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Hire a Driver"}),j.jsx("p",{className:"text-gray-600",children:"Find professional drivers to help you get around safely and comfortably."})]}),j.jsxs(Jt,{onClick:l,disabled:t,variant:"outline",className:"inline-flex items-center gap-2",children:[j.jsx(Rt,{className:"h-4 w-4 "+(t?"animate-spin":"")}),"Refresh"]})]}),j.jsx(hr,{onSearch:e=>{i(e)}}),s.length>0?j.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map((e=>j.jsx(ar,{driver:e},e.id)))}):j.jsxs("div",{className:"text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200",children:[j.jsx($t,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers match your search"}),j.jsx("p",{className:"text-gray-600",children:"Try adjusting your filters or search term to find available drivers."})]})]})})},gr=()=>{const{id:s}=a(),r=t(),{getDriverById:i,selectedDriver:l,isLoading:n,error:c}=Zs(),{createBooking:o}=et(),{user:d,isAuthenticated:m}=$s(),[x,u]=e.useState(!1),[h,p]=e.useState(!1),[g,f]=e.useState(1),[y,b]=e.useState(""),[v,N]=e.useState(""),[w,k]=e.useState(!1),[S,C]=e.useState(null),[A,P]=e.useState(!1),E=()=>{r(-1)};if(e.useEffect((()=>{window.scrollTo(0,0),s&&i(s)}),[s,i]),!m)return j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:E,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to drivers"]}),j.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-yellow-800 mb-2",children:"Login Required"}),j.jsx("p",{className:"text-yellow-700 mb-6",children:"You must be logged in to view driver details and make bookings."}),j.jsxs("div",{className:"flex justify-center space-x-4",children:[j.jsx(Jt,{onClick:()=>r("/login"),children:"Log In"}),j.jsx(Jt,{variant:"outline",onClick:()=>r("/signup"),children:"Sign Up"})]})]})]})});return n?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"flex justify-center items-center h-64",children:j.jsx(nr,{size:"lg"})})})}):c||!l?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:E,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to drivers"]}),j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Driver Not Found"}),j.jsx("p",{className:"text-error-600",children:"The driver you're looking for doesn't exist or has been removed."})]})]})}):j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs(Jt,{variant:"outline",className:"mb-4 flex items-center",onClick:E,children:[j.jsx(ct,{size:18,className:"mr-2"}),"Back to drivers"]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[j.jsxs("div",{className:"md:flex",children:[j.jsxs("div",{className:"md:w-2/3",children:[j.jsxs("div",{className:"relative h-80",children:[j.jsx("img",{src:l.profileImage,alt:l.name,className:"w-full h-full object-cover"}),j.jsxs("div",{className:"absolute bottom-4 right-4 bg-white px-3 py-1 rounded-full flex items-center shadow-md",children:[j.jsx(qt,{size:18,className:"text-yellow-500 mr-1"}),j.jsx("span",{className:"font-medium",children:Number(l.rating||0).toFixed(1)}),j.jsxs("span",{className:"text-sm text-gray-500 ml-1",children:["(",l.reviews||0," reviews)"]})]})]}),j.jsxs("div",{className:"p-6",children:[j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:l.name}),j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsx(ot,{size:18,className:"text-primary-600 mr-2"}),j.jsxs("span",{children:[l.experience," years of professional driving experience"]})]}),j.jsxs("div",{className:"mb-4 space-y-3",children:[j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(At,{size:18,className:"flex-shrink-0 mr-2"}),j.jsx("span",{children:l.location})]}),j.jsxs("div",{className:"flex items-center text-gray-600",children:[j.jsx(ft,{size:18,className:"flex-shrink-0 mr-2"}),j.jsx("span",{children:l.availabilityNotes})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Specialties"}),j.jsx("div",{className:"flex flex-wrap gap-2",children:l.specialties.map(((e,s)=>j.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700",children:e},s)))})]})]})]}),j.jsxs("div",{className:"md:w-1/3 p-6 border-l border-gray-200",children:[j.jsxs("div",{className:"mb-4 pb-4 border-b border-gray-200",children:[j.jsx("div",{className:"text-2xl font-bold text-primary-600 mb-1",children:sr(l.pricePerHour,"hour")}),j.jsx("div",{className:"text-sm text-gray-500",children:"Book this driver by the hour"})]}),j.jsxs("div",{className:"flex space-x-2 mb-4",children:[j.jsxs(Jt,{variant:"primary",className:"flex-1 flex items-center justify-center",onClick:()=>u(!x),children:[j.jsx(Et,{size:18,className:"mr-2"}),x?"Hide Chat":"Chat with Driver"]}),h?j.jsxs(Jt,{variant:"outline",className:"flex-1 flex items-center justify-center",disabled:!0,children:[j.jsx(_t,{size:18,className:"mr-2"}),(null==d?void 0:d.isPhoneVerified)?"**********":"Verify account first"]}):j.jsxs(Jt,{variant:"outline",className:"flex-1 flex items-center justify-center",onClick:()=>p(!0),children:[j.jsx(_t,{size:18,className:"mr-2"}),"Show Contact"]})]}),x&&j.jsx("div",{className:"mb-4",children:j.jsx(xr,{recipientId:l.userId,recipientName:l.name,itemType:"driver",itemId:l.id})})]})]}),j.jsxs("div",{className:"mt-6 p-6 border-t border-gray-200",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Book this Driver"}),A?j.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 text-center mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-success-800 mb-2",children:"Booking Request Sent!"}),j.jsx("p",{className:"text-success-600 mb-4",children:"Your booking request has been submitted. The driver will contact you soon to arrange payment and details."}),j.jsx(Jt,{onClick:()=>r("/account"),fullWidth:!0,children:"View My Bookings"})]}):j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),m&&d){if(l){k(!0),C(null);try{const e=new Date(`${y}T${v}`),s=new Date(e.getTime()+60*g*60*1e3),t=l.pricePerHour*g;await o({userId:d.id,itemType:"driver",itemId:l.id,startTime:e.toISOString(),endTime:s.toISOString(),totalPrice:t,status:"pending"}),P(!0)}catch(s){C("Failed to create booking. Please try again.")}finally{k(!1)}}}else r("/login")},className:"space-y-4",children:[j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),j.jsx("input",{type:"date",id:"booking-date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,min:(new Date).toISOString().split("T")[0],value:y,onChange:e=>b(e.target.value)})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-time",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time"}),j.jsx("input",{type:"time",id:"booking-time",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,value:v,onChange:e=>N(e.target.value)})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"booking-hours",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),j.jsx("select",{id:"booking-hours",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:g,onChange:e=>f(Number(e.target.value)),children:[1,2,3,4,5,6,7,8].map((e=>j.jsxs("option",{value:e,children:[e," ",1===e?"hour":"hours"]},e)))})]})]}),j.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[j.jsxs("div",{className:"flex justify-between mb-2",children:[j.jsx("span",{children:"Rate per hour"}),j.jsx("span",{children:er(l.pricePerHour)})]}),j.jsxs("div",{className:"flex justify-between mb-2",children:[j.jsx("span",{children:"Hours"}),j.jsx("span",{children:g})]}),j.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[j.jsx("span",{children:"Total"}),j.jsx("span",{children:er(l.pricePerHour*g)})]})]}),S&&j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm",children:S}),j.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4",children:j.jsxs("p",{children:[j.jsx("strong",{children:"Note:"})," This is just a booking request. No payment is required on the platform. You'll arrange payment directly with the driver."]})}),j.jsxs(Jt,{type:"submit",fullWidth:!0,disabled:w||!y||!v,children:[w?j.jsx(nr,{size:"sm",className:"mr-2"}):null,"Request Booking"]}),!m&&j.jsx("div",{className:"text-sm text-center text-gray-500",children:"You'll need to log in to complete your booking."})]})]})]})]})})},fr=()=>{const s=t(),{addDriver:r}=Zs(),{user:a,isAuthenticated:i}=$s(),[l,n]=e.useState({name:(null==a?void 0:a.name)||"",age:"",experience:"",licenseNumber:"",location:"",pricePerHour:"",specialties:[],availabilityNotes:""}),[c,o]=e.useState(""),[d,m]=e.useState(null),[x,u]=e.useState(!1),[h,p]=e.useState(null),[g,f]=e.useState(!1),y=e=>{const{name:s,value:t}=e.target;n((e=>({...e,[s]:t})))};return j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-2xl mx-auto",children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Register as a Driver"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"Fill out the form below to register as a driver on our platform."}),g?j.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-6 text-center",children:[j.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 rounded-full bg-success-100 text-success-600 mb-4",children:j.jsx(ht,{size:24})}),j.jsx("h2",{className:"text-xl font-medium text-success-800 mb-2",children:"Registration Successful!"}),j.jsx("p",{className:"text-success-600 mb-6",children:"Your driver profile has been created. Your license verification is pending."}),j.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[j.jsx(Jt,{onClick:()=>s("/drivers"),children:"Browse Drivers"}),j.jsx(Jt,{variant:"outline",onClick:()=>s("/account"),children:"Go to My Account"})]})]}):j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),i&&a){u(!0),p(null);try{if(0===l.specialties.length)throw new Error("Please select at least one specialty");await r({userId:a.id,name:l.name,age:parseInt(l.age),experience:parseInt(l.experience),profileImage:"",licenseNumber:l.licenseNumber,licenseVerificationStatus:"pending",location:l.location,pricePerHour:parseFloat(l.pricePerHour),rating:0,reviews:0,specialties:l.specialties,availabilityNotes:l.availabilityNotes,isAvailable:!0},d||void 0),f(!0),n({name:"",age:"",experience:"",licenseNumber:"",location:"",pricePerHour:"",specialties:[],availabilityNotes:""})}catch(t){p(t instanceof Error?t.message:"Failed to register as a driver")}finally{u(!1)}}else s("/login")},className:"bg-white rounded-lg shadow-md p-6",children:[h&&j.jsxs("div",{className:"mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start",children:[j.jsx(lt,{size:20,className:"text-error-600 mr-2 flex-shrink-0 mt-0.5"}),j.jsx("div",{className:"text-error-700",children:h})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Profile Image"}),j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"w-20 h-20 rounded-full overflow-hidden mr-4 bg-gray-100",children:c?j.jsx("img",{src:c,alt:"Profile",className:"w-full h-full object-cover"}):j.jsx("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:j.jsx(Ht,{size:24})})}),j.jsxs("div",{children:[j.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Upload a professional photo of yourself."}),j.jsxs(Jt,{type:"button",variant:"outline",size:"sm",className:"relative overflow-hidden",children:["Upload Photo",j.jsx("input",{type:"file",className:"absolute inset-0 opacity-0 cursor-pointer",accept:"image/*",onChange:e=>{var s;const t=null==(s=e.target.files)?void 0:s[0];if(t){m(t);const e=new FileReader;e.onload=e=>{var s;o(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}}})]})]})]})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),j.jsx("input",{type:"text",id:"name",name:"name",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.name,onChange:y})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"age",className:"block text-sm font-medium text-gray-700 mb-1",children:"Age"}),j.jsx("input",{type:"number",id:"age",name:"age",min:"18",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.age,onChange:y})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"experience",className:"block text-sm font-medium text-gray-700 mb-1",children:"Driving Experience (years)"}),j.jsx("input",{type:"number",id:"experience",name:"experience",min:"1",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.experience,onChange:y})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"licenseNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Driver's License Number"}),j.jsx("input",{type:"text",id:"licenseNumber",name:"licenseNumber",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.licenseNumber,onChange:y})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),j.jsx("input",{type:"text",id:"location",name:"location",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.location,onChange:y})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hourly Rate ($)"}),j.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",min:"10",step:"0.01",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:l.pricePerHour,onChange:y})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Specialties"}),j.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:["City Driving","Long Distance","Airport Transfers","Event Transportation","Tourist Guide","Luxury Vehicles","Corporate Events"].map((e=>j.jsxs("div",{className:"flex items-center",children:[j.jsx("input",{type:"checkbox",id:`specialty-${e}`,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:l.specialties.includes(e),onChange:()=>(e=>{n((s=>{const t=[...s.specialties];return t.includes(e)?{...s,specialties:t.filter((s=>s!==e))}:{...s,specialties:[...t,e]}}))})(e)}),j.jsx("label",{htmlFor:`specialty-${e}`,className:"ml-2 text-sm text-gray-700",children:e})]},e)))})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),j.jsx("textarea",{id:"availabilityNotes",name:"availabilityNotes",rows:3,required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., Available weekdays from 9 AM to 5 PM",value:l.availabilityNotes,onChange:y})]}),j.jsx("div",{className:"flex justify-end",children:j.jsxs(Jt,{type:"submit",disabled:x,children:[x?j.jsx(nr,{size:"sm",className:"mr-2"}):null,"Register as Driver"]})})]})]})})})},yr=()=>{const s=t(),{user:a}=$s(),{drivers:i,toggleDriverAvailability:l,updateDriver:n}=Zs(),{bookings:c,updateBookingStatus:o}=et(),[d,m]=e.useState(!1),[x,u]=e.useState("bookings"),h=i.find((e=>e.userId===(null==a?void 0:a.id))),p=c.filter((e=>"driver"===e.itemType&&e.itemId===(null==h?void 0:h.id))),g=async(e,s)=>{m(!0);try{await o(e,s)}catch(t){}finally{m(!1)}};return a&&"driver"===a.role?h?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Driver Dashboard"}),j.jsx("p",{className:"text-gray-600",children:"Manage your driver profile and booking requests."})]}),j.jsxs("div",{className:"mt-4 md:mt-0 flex items-center",children:[j.jsxs("div",{className:"mr-4 px-4 py-2 rounded-full flex items-center "+(h.isAvailable?"bg-success-100 text-success-800":"bg-gray-100 text-gray-800"),children:[j.jsx("span",{className:"mr-2",children:"Status:"}),j.jsx("span",{className:"font-medium",children:h.isAvailable?"Available":"Unavailable"})]}),j.jsxs(Jt,{variant:h.isAvailable?"outline":"primary",onClick:async()=>{if(h){m(!0);try{await l(h.id)}catch(e){}finally{m(!1)}}},disabled:d,children:[d?j.jsx(nr,{size:"sm",className:"mr-2"}):j.jsx(Ut,{size:18,className:"mr-2"}),h.isAvailable?"Go Offline":"Go Online"]})]})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[j.jsxs("div",{className:"flex border-b border-gray-200",children:[j.jsx("button",{className:"px-6 py-3 font-medium text-sm focus:outline-none "+("bookings"===x?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"),onClick:()=>u("bookings"),children:"Booking Requests"}),j.jsx("button",{className:"px-6 py-3 font-medium text-sm focus:outline-none "+("profile"===x?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"),onClick:()=>u("profile"),children:"Driver Profile"})]}),"bookings"===x&&j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Booking Requests"}),0===p.length?j.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[j.jsx(mt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No booking requests yet"}),j.jsx("p",{className:"text-gray-600",children:"When clients book your services, their requests will appear here."})]}):j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Client"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map((e=>{const s=new Date(e.startTime),t=(new Date(e.endTime).getTime()-s.getTime())/36e5;return j.jsxs("tr",{children:[j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx($t,{size:24,className:"text-gray-400 mr-2"}),j.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["Client #",e.userId.slice(-4)]})]})}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[j.jsx("div",{className:"text-sm text-gray-900",children:s.toLocaleDateString()}),j.jsx("div",{className:"text-sm text-gray-500",children:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"text-sm text-gray-900",children:[t," ",1===t?"hour":"hours"]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["$",e.totalPrice.toFixed(2)]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+("pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-blue-100 text-blue-800":"completed"===e.status?"bg-success-100 text-success-800":"bg-error-100 text-error-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:["pending"===e.status&&j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("button",{className:"text-success-600 hover:text-success-800",onClick:()=>g(e.id,"confirmed"),children:j.jsx(ut,{size:18})}),j.jsx("button",{className:"text-error-600 hover:text-error-800",onClick:()=>g(e.id,"cancelled"),children:j.jsx(Wt,{size:18})})]}),"confirmed"===e.status&&j.jsx("button",{className:"text-success-600 hover:text-success-800",onClick:()=>g(e.id,"completed"),children:"Complete"})]})]},e.id)}))})]})})]}),"profile"===x&&j.jsx("div",{className:"p-6",children:j.jsxs("div",{className:"flex flex-col md:flex-row",children:[j.jsx("div",{className:"md:w-1/3 mb-6 md:mb-0 md:pr-6",children:j.jsxs("div",{className:"bg-gray-100 rounded-lg p-6 text-center",children:[j.jsx("div",{className:"w-32 h-32 mx-auto rounded-full overflow-hidden mb-4",children:j.jsx("img",{src:h.profileImage,alt:h.name,className:"w-full h-full object-cover"})}),j.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:h.name}),j.jsxs("div",{className:"flex items-center justify-center mb-4",children:[j.jsx(qt,{size:16,className:"text-yellow-500 mr-1"}),j.jsx("span",{className:"font-medium",children:Number(h.rating||0).toFixed(1)}),j.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(",h.reviews||0," reviews)"]})]}),j.jsx(r,{to:"/driver/edit-profile",children:j.jsxs(Jt,{variant:"outline",className:"w-full flex items-center justify-center",children:[j.jsx(Ft,{size:16,className:"mr-2"}),"Edit Profile"]})})]})}),j.jsxs("div",{className:"md:w-2/3",children:[j.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Driver Information"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Age"}),j.jsxs("div",{className:"font-medium",children:[h.age," years"]})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Experience"}),j.jsxs("div",{className:"font-medium",children:[h.experience," years"]})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Number"}),j.jsx("div",{className:"font-medium",children:h.licenseNumber})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Status"}),j.jsx("div",{className:"font-medium "+("verified"===h.licenseVerificationStatus?"text-success-600":"pending"===h.licenseVerificationStatus?"text-yellow-600":"text-error-600"),children:h.licenseVerificationStatus.charAt(0).toUpperCase()+h.licenseVerificationStatus.slice(1)})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Location"}),j.jsxs("div",{className:"flex items-center text-gray-700",children:[j.jsx(At,{size:18,className:"mr-2 text-gray-500"}),h.location]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Availability"}),j.jsxs("div",{className:"flex items-center text-gray-700",children:[j.jsx(ft,{size:18,className:"mr-2 text-gray-500"}),h.availabilityNotes]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Hourly Rate"}),j.jsxs("div",{className:"flex items-center text-gray-700",children:[j.jsx(yt,{size:18,className:"mr-2 text-gray-500"}),"$",h.pricePerHour,"/hour"]})]}),j.jsxs("div",{children:[j.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Specialties"}),j.jsx("div",{className:"flex flex-wrap gap-2",children:h.specialties.map(((e,s)=>j.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700",children:e},s)))})]})]})]})})]})]})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-warning-50 border border-warning-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-warning-800 mb-2",children:"Driver Profile Not Found"}),j.jsx("p",{className:"text-warning-600",children:"You need to complete your driver registration first."}),j.jsx(Jt,{className:"mt-4",onClick:()=>s("/driver/register"),children:"Complete Registration"})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),j.jsx("p",{className:"text-error-600",children:"You must be logged in as a driver to access this page."}),j.jsx(Jt,{className:"mt-4",onClick:()=>s("/login"),children:"Log In"})]})})})},jr=()=>{var s,a,i,l,n,c;const o=t(),{user:d,isAuthenticated:m}=$s(),{cars:x}=Ws(),{drivers:u}=Zs(),[h,p]=e.useState([]),[g,f]=e.useState([]),[y,b]=e.useState(null),[v,N]=e.useState([]),[w,k]=e.useState(""),[S,C]=e.useState(0),[A,P]=e.useState(!1),[E,_]=e.useState(!0),[z,R]=e.useState(!1),[T,D]=e.useState(!1),[L,I]=e.useState(!1),[O,F]=e.useState("bookings"),q=e.useRef(null),U=async()=>{if(m&&d)try{R(!0);const e=await As.get(Fs);f(e.data||[])}catch(e){}finally{R(!1)}else R(!1)},B=h;e.useEffect((()=>{(async()=>{if(m&&d)try{_(!0);const e=((await As.get(Is)).data||[]).map((e=>({id:e.id,userId:e.user_id,itemType:e.item_type,itemId:e.item_id,startTime:e.start_time,endTime:e.end_time,totalPrice:parseFloat(e.total_price)||0,status:e.status,notes:e.notes,createdAt:e.created_at})));p(e)}catch(e){}finally{_(!1)}else _(!1)})()}),[m]),e.useEffect((()=>{"messages"===O&&(U(),H())}),[O,m]),e.useEffect((()=>{H()}),[m]);const M=async e=>{P(!0);try{await(async e=>{try{await As.patch(`${Ls}/${e}`,{status:"cancelled"}),p((s=>s.map((s=>s.id===e?{...s,status:"cancelled"}:s))))}catch(s){throw s}})(e)}catch(s){}finally{P(!1)}},H=async()=>{if(m&&d)try{const e=await As.get(qs);C(e.data.count||0)}catch(e){}},$=async()=>{if(w.trim()&&y)try{I(!0);const e=await As.post(Us,{chat_id:y.id,content:w.trim()});N((s=>[...s,e.data])),k(""),setTimeout((()=>{var e;null==(e=q.current)||e.scrollIntoView({behavior:"smooth"})}),100),U(),H()}catch(e){}finally{I(!1)}},V=e=>{b(e),(async e=>{try{D(!0);const s=await As.get(`${Os}/${e}`);N(s.data.messages||[]),setTimeout((()=>{var e;null==(e=q.current)||e.scrollIntoView({behavior:"smooth"})}),100)}catch(s){}finally{D(!1)}})(e.id),setTimeout((()=>{H()}),500)};return d&&"client"===d.role?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Account"}),j.jsx("p",{className:"text-gray-600",children:"Manage your bookings and account information."})]}),j.jsxs("div",{className:"mt-4 md:mt-0 flex space-x-3",children:[j.jsx(r,{to:"/cars",children:j.jsxs(Jt,{variant:"outline",children:[j.jsx(xt,{size:18,className:"mr-2"}),"Browse Cars"]})}),j.jsx(r,{to:"/drivers",children:j.jsxs(Jt,{variant:"outline",children:[j.jsx($t,{size:18,className:"mr-2"}),"Hire Drivers"]})})]})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[j.jsxs("div",{className:"flex border-b border-gray-200",children:[j.jsx("button",{className:"px-6 py-3 font-medium text-sm focus:outline-none "+("bookings"===O?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"),onClick:()=>F("bookings"),children:"My Bookings"}),j.jsxs("button",{className:"px-6 py-3 font-medium text-sm focus:outline-none relative "+("messages"===O?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"),onClick:()=>F("messages"),children:["Messages",S>0&&j.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:S>99?"99+":S})]}),j.jsx("button",{className:"px-6 py-3 font-medium text-sm focus:outline-none "+("profile"===O?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"),onClick:()=>F("profile"),children:"Profile"})]}),"bookings"===O&&j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Bookings"}),E?j.jsxs("div",{className:"text-center py-8",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),j.jsx("p",{className:"mt-4 text-gray-600",children:"Loading your bookings..."})]}):0===B.length?j.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[j.jsx(mt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No bookings yet"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You haven't made any bookings yet. Browse our cars and drivers to get started."}),j.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-3",children:[j.jsx(r,{to:"/cars",children:j.jsx(Jt,{children:"Browse Cars"})}),j.jsx(r,{to:"/drivers",children:j.jsx(Jt,{variant:"outline",children:"Hire Drivers"})})]})]}):j.jsx("div",{className:"grid grid-cols-1 gap-6",children:B.map((e=>{const s=(e=>{if("car"===e.itemType){const s=x.find((s=>s.id===String(e.itemId)));return s?{name:`${s.year} ${s.make} ${s.model}`,image:s.images[0],location:s.location,price:s.pricePerHour}:null}{const s=u.find((s=>s.id===String(e.itemId)));return s?{name:s.name,image:s.profileImage,location:s.location,price:s.pricePerHour}:null}})(e),t=new Date(e.startTime),r=new Date(e.endTime),a=(r.getTime()-t.getTime())/36e5;return s?j.jsx("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:j.jsxs("div",{className:"flex flex-col md:flex-row",children:[j.jsx("div",{className:"md:w-1/4 h-48 md:h-auto",children:j.jsx("img",{src:s.image,alt:s.name,className:"w-full h-full object-cover"})}),j.jsxs("div",{className:"p-6 md:w-3/4",children:[j.jsxs("div",{className:"flex flex-col md:flex-row justify-between mb-4",children:[j.jsxs("div",{children:[j.jsxs("h3",{className:"text-lg font-bold text-gray-900 mb-1",children:["car"===e.itemType?"Car Rental: ":"Driver Hire: ",s.name]}),j.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[j.jsx(At,{size:16,className:"mr-1"}),j.jsx("span",{className:"text-sm",children:s.location})]})]}),j.jsx("div",{className:"mt-2 md:mt-0",children:j.jsx("span",{className:"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full "+("pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-blue-100 text-blue-800":"completed"===e.status?"bg-success-100 text-success-800":"bg-error-100 text-error-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[j.jsxs("div",{children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Date"}),j.jsx("div",{className:"font-medium",children:t.toLocaleDateString()})]}),j.jsxs("div",{children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Time"}),j.jsxs("div",{className:"font-medium",children:[t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})," -",r.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),j.jsxs("div",{children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Duration"}),j.jsxs("div",{className:"font-medium",children:[a," ",1===a?"hour":"hours"]})]})]}),j.jsxs("div",{className:"flex justify-between items-center",children:[j.jsxs("div",{className:"font-bold text-lg text-primary-600",children:["$",e.totalPrice.toFixed(2)]}),("pending"===e.status||"confirmed"===e.status)&&j.jsxs(Jt,{variant:"outline",className:"text-error-600 border-error-600 hover:bg-error-50",onClick:()=>M(e.id),disabled:A,children:[A?j.jsx(nr,{size:"sm",className:"mr-2"}):j.jsx(Wt,{size:16,className:"mr-2"}),"Cancel Booking"]})]})]})]})},e.id):null}))})]}),"messages"===O&&j.jsx("div",{className:"p-6",children:y?j.jsxs(j.Fragment,{children:[j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsx("button",{onClick:()=>b(null),className:"mr-4 p-2 text-gray-600 hover:text-gray-900",children:j.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),j.jsxs("div",{children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:(null==(s=y.user)?void 0:s.id)===(null==d?void 0:d.id)?null==(a=y.recipient)?void 0:a.name:null==(i=y.user)?void 0:i.name}),j.jsx("p",{className:"text-sm text-gray-500",children:(null==(l=y.user)?void 0:l.id)===(null==d?void 0:d.id)?null==(n=y.recipient)?void 0:n.email:null==(c=y.user)?void 0:c.email})]})]}),j.jsx("div",{className:"bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto mb-4",style:{scrollBehavior:"smooth"},children:T?j.jsxs("div",{className:"text-center py-8",children:[j.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),j.jsx("p",{className:"text-gray-600 mt-2",children:"Loading messages..."})]}):j.jsxs("div",{className:"space-y-4",children:[v.map((e=>{var s,t,r;const a=String(e.sender_id)===String(null==d?void 0:d.id),i=a?"You":(null==(s=y.user)?void 0:s.id)===(null==d?void 0:d.id)?null==(t=y.recipient)?void 0:t.name:null==(r=y.user)?void 0:r.name;return j.jsxs("div",{className:"flex "+(a?"justify-end":"justify-start"),children:[j.jsxs("div",{className:"max-w-xs lg:max-w-md "+(a?"order-2":"order-1"),children:[j.jsxs("div",{className:"text-xs mb-1 "+(a?"text-right text-blue-600":"text-left text-gray-600"),children:[i," ",a?"(Client)":"(Owner)"]}),j.jsxs("div",{className:"px-4 py-3 rounded-lg shadow-sm "+(a?"bg-blue-600 text-white rounded-br-sm":"bg-green-100 text-gray-900 border border-green-200 rounded-bl-sm"),children:[j.jsx("p",{className:"text-sm leading-relaxed",children:e.content}),j.jsxs("p",{className:"text-xs mt-2 "+(a?"text-blue-100":"text-green-600"),children:[new Date(e.created_at).toLocaleDateString()," at ",new Date(e.created_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]})]}),j.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold "+(a?"bg-blue-100 text-blue-600 ml-2 order-3":"bg-green-100 text-green-600 mr-2 order-0"),children:a?"C":"O"})]},e.id)})),j.jsx("div",{ref:q})]})}),j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("input",{type:"text",value:w,onChange:e=>k(e.target.value),onKeyPress:e=>"Enter"===e.key&&$(),placeholder:"Type your message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:L}),j.jsx("button",{onClick:$,disabled:!w.trim()||L,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:L?"Sending...":"Send"})]})]}):j.jsxs(j.Fragment,{children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Messages"}),z?j.jsxs("div",{className:"text-center py-8",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),j.jsx("p",{className:"text-gray-600 mt-4",children:"Loading messages..."})]}):0===g.length?j.jsxs("div",{className:"text-center py-12",children:[j.jsx("div",{className:"text-gray-400 mb-4",children:j.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"})})}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No messages yet"}),j.jsx("p",{className:"text-gray-600 mb-4",children:"Start a conversation by messaging a car owner when you're interested in booking their vehicle."}),j.jsx(r,{to:"/cars",children:j.jsx(Jt,{children:"Browse Cars"})})]}):j.jsx("div",{className:"space-y-4",children:g.map((e=>{var s,t,r,a;const i=(null==(s=e.user)?void 0:s.id)===(null==d?void 0:d.id)?e.recipient:e.user,l=null==(t=e.messages)?void 0:t[0];return j.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer",onClick:()=>V(e),children:[j.jsxs("div",{className:"flex items-start justify-between",children:[j.jsxs("div",{className:"flex-1",children:[j.jsxs("div",{className:"flex items-center mb-2",children:[j.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3",children:j.jsx("span",{className:"text-gray-600 font-semibold",children:(null==(a=null==(r=null==i?void 0:i.name)?void 0:r.charAt(0))?void 0:a.toUpperCase())||"?"})}),j.jsxs("div",{children:[j.jsx("h3",{className:"font-medium text-gray-900",children:(null==i?void 0:i.name)||"Unknown User"}),j.jsx("p",{className:"text-sm text-gray-500",children:null==i?void 0:i.email})]})]}),l&&j.jsxs("div",{className:"ml-13",children:[j.jsx("p",{className:"text-gray-700 text-sm mb-1",children:l.content}),j.jsxs("p",{className:"text-xs text-gray-500",children:[new Date(l.created_at).toLocaleDateString()," at ",new Date(l.created_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]})]}),j.jsxs("div",{className:"flex items-center space-x-2",children:[j.jsxs("div",{className:"text-xs text-gray-400",children:["Chat #",String(e.id).slice(0,8)]}),e.unread_count>0&&j.jsx("span",{className:"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:e.unread_count>99?"99+":e.unread_count})]})]}),e.related_to_type&&j.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:j.jsxs("p",{className:"text-xs text-gray-500",children:["Related to: ","car"===e.related_to_type?"Car Booking":"Driver Booking"]})})]},e.id)}))})]})}),"profile"===O&&j.jsx("div",{className:"p-6",children:j.jsxs("div",{className:"flex flex-col md:flex-row",children:[j.jsx("div",{className:"md:w-1/3 mb-6 md:mb-0 md:pr-6",children:j.jsxs("div",{className:"bg-gray-100 rounded-lg p-6 text-center",children:[j.jsx("div",{className:"w-32 h-32 mx-auto rounded-full overflow-hidden mb-4 bg-gray-300 flex items-center justify-center",children:d.licenseImageUrl?j.jsx("img",{src:d.licenseImageUrl,alt:d.name,className:"w-full h-full object-cover"}):j.jsx($t,{size:64,className:"text-gray-400"})}),j.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:d.name}),j.jsx("p",{className:"text-gray-500 mb-4",children:d.email}),j.jsx(r,{to:"/account/edit-profile",children:j.jsxs(Jt,{variant:"outline",className:"w-full flex items-center justify-center",children:[j.jsx(Ft,{size:16,className:"mr-2"}),"Edit Profile"]})})]})}),j.jsxs("div",{className:"md:w-2/3",children:[j.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Account Information"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Account Type"}),j.jsx("div",{className:"font-medium",children:"Client"})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Member Since"}),j.jsx("div",{className:"font-medium",children:new Date(d.createdAt).toLocaleDateString()})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Phone Number"}),j.jsxs("div",{className:"font-medium",children:[d.phoneNumber||"Not provided",d.phoneNumber&&d.isPhoneVerified&&j.jsx("span",{className:"ml-2 text-xs bg-success-100 text-success-800 px-2 py-0.5 rounded-full",children:"Verified"})]})]}),j.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[j.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Verification"}),j.jsx("div",{className:"font-medium",children:d.licenseVerificationStatus?j.jsx("span",{className:""+("verified"===d.licenseVerificationStatus?"text-success-600":"pending"===d.licenseVerificationStatus?"text-yellow-600":"text-error-600"),children:d.licenseVerificationStatus.charAt(0).toUpperCase()+d.licenseVerificationStatus.slice(1)}):"Not submitted"})]})]}),!d.licenseVerificationStatus&&j.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:j.jsxs("div",{className:"flex",children:[j.jsx(lt,{size:20,className:"text-yellow-600 mr-2 flex-shrink-0"}),j.jsxs("div",{children:[j.jsx("h4",{className:"font-medium text-yellow-800 mb-1",children:"Verify Your License"}),j.jsx("p",{className:"text-yellow-700 text-sm mb-3",children:"To rent cars, you need to verify your driver's license. This helps ensure safety and trust in our community."}),j.jsx(r,{to:"/account/verification",children:j.jsx(Jt,{size:"sm",children:"Verify License"})})]})]})}),j.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[j.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Payment Methods"}),j.jsx("p",{className:"text-gray-600 mb-3",children:"Park & Rent doesn't handle payments directly. All payments are arranged between you and the car owner or driver."}),j.jsx("p",{className:"text-sm text-gray-500",children:"We recommend discussing payment methods and terms before confirming any booking."})]})]})]})})]})]})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),j.jsx("p",{className:"text-error-600",children:"You must be logged in as a client to access this page."}),j.jsx(Jt,{className:"mt-4",onClick:()=>o("/login"),children:"Log In"})]})})})},br=({id:e,label:s,type:t="text",placeholder:r,value:a,onChange:i,error:l,className:n="",required:c=!1,disabled:o=!1})=>j.jsxs("div",{className:`mb-4 ${n}`,children:[s&&j.jsxs("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 mb-1",children:[s," ",c&&j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{id:e,type:t,placeholder:r,value:a,onChange:i,disabled:o,required:c,className:`w-full px-3 py-2 border ${l?"border-error-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-2 ${l?"focus:ring-error-500 focus:border-error-500":"focus:ring-primary-500 focus:border-primary-500"} disabled:bg-gray-100 disabled:text-gray-500`}),l&&j.jsx("p",{className:"mt-1 text-sm text-error-600",children:l})]}),vr=e=>"admin"===(null==e?void 0:e.role),Nr=()=>{const s=t(),{user:r}=$s(),{cars:a,updateCar:i}=Ws(),{drivers:l,updateDriver:n}=Zs(),{bookings:c,updateBookingStatus:o}=et(),{stats:d,users:m,cars:x,drivers:u,bookings:h,gpsRequests:p,isLoading:g,error:f,fetchDashboardStats:y,fetchUsers:b,fetchCars:v,fetchDrivers:N,fetchBookings:w,fetchGpsRequests:k,updateUserRole:S,verifyDriverLicense:C,updateGpsRequestStatus:A}=(()=>{const s=e.useContext(tt);if(!s)throw new Error("useAdmin must be used within an AdminProvider");return s})(),[P,E]=e.useState("overview"),[_,z]=e.useState(!1),[R,T]=e.useState(""),[D,L]=e.useState("all"),[I,O]=e.useState(!1),[F,q]=e.useState(!1),[U,B]=e.useState(!1),[M,H]=e.useState(!1),[$,V]=e.useState(!1);e.useState(!1);const[Y,W]=e.useState(!1),[G,J]=e.useState(null),[K,Z]=e.useState(null),[X,Q]=e.useState(null),[ee,se]=e.useState(!1),[te,re]=e.useState(""),[ae,ie]=e.useState(""),[le,ne]=e.useState(""),[ce,oe]=e.useState(""),[de,me]=e.useState({name:"",email:"",role:"client",password:""}),[xe,ue]=e.useState({make:"",model:"",year:"",pricePerHour:"",location:"",description:"",ownerId:"",images:[]});e.useState({name:"",age:"",experience:"",location:"",pricePerHour:"",specialties:""});const he=m.filter((e=>"owner"===e.role));e.useEffect((()=>{vr(r)&&(y(),b(),v(),N(),w(),k())}),[r]);const pe=async(e,s)=>{try{z(!0),await C(e,s),await N()}catch(t){}finally{z(!1)}},ge=async e=>{if(window.confirm("Are you sure you want to delete this car?"))try{z(!0),await As.delete(`/admin/cars/${e}`),await v()}catch(s){}finally{z(!1)}},fe=(null==d?void 0:d.users_count)||0,ye=(null==d?void 0:d.cars_count)||0,je=(null==d?void 0:d.drivers_count)||0,be=(null==d?void 0:d.bookings_count)||0,ve=(null==d?void 0:d.pending_driver_verifications)||0,Ne=m.filter((e=>e.name.toLowerCase().includes(R.toLowerCase())||e.email.toLowerCase().includes(R.toLowerCase())));return a.filter((e=>(e.make.toLowerCase().includes(R.toLowerCase())||e.model.toLowerCase().includes(R.toLowerCase())||e.location.toLowerCase().includes(R.toLowerCase()))&&("all"===D||"active"===D&&e.isActive||"inactive"===D&&!e.isActive))),l.filter((e=>(e.name.toLowerCase().includes(R.toLowerCase())||e.location.toLowerCase().includes(R.toLowerCase()))&&("all"===D||"active"===D&&e.isAvailable||"inactive"===D&&!e.isAvailable||"pending"===D&&"pending"===e.licenseVerificationStatus))),c.filter((e=>"all"===D||e.status===D)),r&&vr(r)?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),j.jsx("p",{className:"text-gray-600",children:"Manage users, cars, drivers, and bookings."})]})}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[j.jsxs("div",{className:"flex flex-wrap border-b border-gray-200 overflow-x-auto",children:[j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("overview"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("overview"),children:[j.jsx(dt,{size:16,className:"inline-block mr-1"}),"Overview"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("users"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("users"),children:[j.jsx(Yt,{size:16,className:"inline-block mr-1"}),"Users"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("cars"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("cars"),children:[j.jsx(xt,{size:16,className:"inline-block mr-1"}),"Cars"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("drivers"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("drivers"),children:[j.jsx($t,{size:16,className:"inline-block mr-1"}),"Drivers"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("bookings"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("bookings"),children:[j.jsx(mt,{size:16,className:"inline-block mr-1"}),"Bookings"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("gps-requests"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("gps-requests"),children:[j.jsx(At,{size:16,className:"inline-block mr-1"}),"GPS Requests"]}),j.jsxs("button",{className:"px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap "+("settings"===P?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),onClick:()=>E("settings"),children:[j.jsx(It,{size:16,className:"inline-block mr-1"}),"Settings"]})]}),"overview"===P&&j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Platform Overview"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[j.jsxs("div",{className:"bg-blue-50 rounded-lg p-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-blue-800",children:"Total Users"}),j.jsx(Yt,{size:24,className:"text-blue-500"})]}),j.jsx("p",{className:"text-3xl font-bold text-blue-600",children:fe}),j.jsxs("div",{className:"mt-2 text-sm text-blue-600",children:[j.jsx("span",{className:"font-medium",children:m.filter((e=>"client"===e.role)).length})," Clients,",j.jsx("span",{className:"font-medium ml-1",children:m.filter((e=>"owner"===e.role)).length})," Owners,",j.jsx("span",{className:"font-medium ml-1",children:m.filter((e=>"driver"===e.role)).length})," Drivers"]})]}),j.jsxs("div",{className:"bg-green-50 rounded-lg p-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-green-800",children:"Total Cars"}),j.jsx(xt,{size:24,className:"text-green-500"})]}),j.jsx("p",{className:"text-3xl font-bold text-green-600",children:ye}),j.jsxs("div",{className:"mt-2 text-sm text-green-600",children:[j.jsx("span",{className:"font-medium",children:x.filter((e=>e.isActive)).length})," Active,",j.jsx("span",{className:"font-medium ml-1",children:x.filter((e=>!e.isActive)).length})," Inactive"]})]}),j.jsxs("div",{className:"bg-purple-50 rounded-lg p-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-purple-800",children:"Total Drivers"}),j.jsx($t,{size:24,className:"text-purple-500"})]}),j.jsx("p",{className:"text-3xl font-bold text-purple-600",children:je}),j.jsxs("div",{className:"mt-2 text-sm text-purple-600",children:[j.jsx("span",{className:"font-medium",children:u.filter((e=>e.isAvailable)).length})," Available,",j.jsx("span",{className:"font-medium ml-1",children:u.filter((e=>!e.isAvailable)).length})," Unavailable"]})]}),j.jsxs("div",{className:"bg-yellow-50 rounded-lg p-6",children:[j.jsxs("div",{className:"flex items-center justify-between mb-4",children:[j.jsx("h3",{className:"text-lg font-medium text-yellow-800",children:"Total Bookings"}),j.jsx(mt,{size:24,className:"text-yellow-500"})]}),j.jsx("p",{className:"text-3xl font-bold text-yellow-600",children:be}),j.jsxs("div",{className:"mt-2 text-sm text-yellow-600",children:[j.jsx("span",{className:"font-medium",children:(null==d?void 0:d.completed_bookings)||0})," Completed,",j.jsx("span",{className:"font-medium ml-1",children:(null==d?void 0:d.pending_bookings)||0})," Pending"]})]})]}),j.jsxs("div",{className:"bg-orange-50 rounded-lg p-6 mb-8",children:[j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsx(nt,{size:24,className:"text-orange-500 mr-2"}),j.jsx("h3",{className:"text-lg font-medium text-orange-800",children:"Pending Verifications"})]}),j.jsx("p",{className:"text-3xl font-bold text-orange-600 mb-2",children:ve}),j.jsxs("p",{className:"text-sm text-orange-700",children:[ve," users are waiting for license verification. Review these to maintain platform safety."]}),j.jsx(Jt,{variant:"outline",className:"mt-4 border-orange-500 text-orange-700 hover:bg-orange-100",onClick:()=>E("users"),children:"Review Verifications"})]}),j.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"Recent Activity"}),j.jsx("div",{className:"space-y-4",children:((null==d?void 0:d.recent_bookings)||[]).slice(0,5).map(((e,s)=>j.jsxs("div",{className:"flex items-start",children:[j.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 "+("completed"===e.status?"bg-success-100":"pending"===e.status?"bg-yellow-100":"confirmed"===e.status?"bg-blue-100":"bg-error-100"),children:j.jsx(mt,{size:16,className:""+("completed"===e.status?"text-success-600":"pending"===e.status?"text-yellow-600":"confirmed"===e.status?"text-blue-600":"text-error-600")})}),j.jsxs("div",{className:"ml-3",children:[j.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["New ",e.itemType," booking (",e.status,")"]}),j.jsx("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()})]})]},s)))})]})]}),"users"===P&&j.jsxs("div",{className:"p-6",children:[j.jsxs("div",{className:"flex justify-between items-center mb-6",children:[j.jsxs("div",{className:"flex items-center gap-4",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Users Management"}),j.jsxs("div",{className:"text-sm text-gray-600",children:["Total: ",m.length]})]}),j.jsxs("div",{className:"flex items-center gap-4",children:[j.jsxs(Jt,{onClick:()=>V(!0),size:"sm",className:"flex items-center gap-2",children:[j.jsx(Yt,{size:16}),"Create User"]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(Dt,{size:16,className:"text-gray-400"})}),j.jsx("input",{type:"text",placeholder:"Search users...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:R,onChange:e=>T(e.target.value)})]})]})]}),j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"License Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:Ne.map((e=>j.jsxs("tr",{children:[j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.licenseImageUrl?j.jsx("img",{src:e.licenseImageUrl,alt:e.name,className:"h-10 w-10 rounded-full object-cover"}):j.jsx($t,{size:20,className:"text-gray-500"})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),j.jsx("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+("client"===e.role?"bg-blue-100 text-blue-800":"owner"===e.role?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"),children:e.role.charAt(0).toUpperCase()+e.role.slice(1)})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.licenseVerificationStatus?j.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+("verified"===e.licenseVerificationStatus?"bg-success-100 text-success-800":"pending"===e.licenseVerificationStatus?"bg-yellow-100 text-yellow-800":"bg-error-100 text-error-800"),children:e.licenseVerificationStatus.charAt(0).toUpperCase()+e.licenseVerificationStatus.slice(1)}):j.jsx("span",{className:"text-gray-500 text-sm",children:"N/A"})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{J(e),q(!0)},title:"View Details",children:j.jsx(jt,{size:16})}),j.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{J(e),me({name:e.name,email:e.email,role:e.role,password:""}),se(!0),q(!0)},title:"Edit User",children:j.jsx(Ft,{size:16})}),j.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this user?"))try{z(!0),await As.delete(`/admin/users/${e}`),await b()}catch(s){}finally{z(!1)}})(e.id),disabled:_,title:"Delete User",children:j.jsx(Bt,{size:16})}),j.jsxs("select",{value:e.role,onChange:s=>(async(e,s)=>{try{z(!0),await S(e,s),await b()}catch(t){}finally{z(!1)}})(e.id,s.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",disabled:_,children:[j.jsx("option",{value:"client",children:"Client"}),j.jsx("option",{value:"owner",children:"Owner"}),j.jsx("option",{value:"driver",children:"Driver"}),j.jsx("option",{value:"admin",children:"Admin"})]})]})})]},e.id)))})]})})]}),"cars"===P&&j.jsxs("div",{className:"p-6",children:[j.jsxs("div",{className:"flex justify-between items-center mb-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Cars Management"}),j.jsxs("div",{className:"flex items-center gap-4",children:[j.jsxs("div",{className:"text-sm text-gray-600",children:["Total Cars: ",x.length]}),j.jsxs(Jt,{onClick:()=>W(!0),size:"sm",className:"flex items-center gap-2",children:[j.jsx(xt,{size:16}),"Create Car"]})]})]}),g?j.jsx("div",{className:"flex justify-center py-8",children:j.jsx(nr,{size:"lg"})}):f?j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:j.jsx("p",{className:"text-error-600",children:f})}):x.length>0?j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),j.jsx("th",{className:"hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Owner"}),j.jsx("th",{className:"hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),j.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),j.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),j.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map((e=>j.jsxs("tr",{children:[j.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:j.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:j.jsx(xt,{className:"h-5 w-5 text-gray-600"})})}),j.jsxs("div",{className:"ml-4",children:[j.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.make," ",e.model]}),j.jsx("div",{className:"text-sm text-gray-500",children:e.year}),j.jsxs("div",{className:"md:hidden text-xs text-gray-500 mt-1",children:["Owner #",e.ownerId," • ",e.location]})]})]})}),j.jsxs("td",{className:"hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["Owner #",e.ownerId]}),j.jsx("td",{className:"hidden lg:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.location}),j.jsxs("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.pricePerHour,"/hr"]}),j.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),j.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{Q(e),H(!0)},title:"View Details",children:j.jsx(jt,{size:16})}),j.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{Q(e),ue({make:e.make,model:e.model,year:e.year.toString(),pricePerHour:e.pricePerHour.toString(),location:e.location,description:e.description||""}),se(!0),H(!0)},title:"Edit Car",children:j.jsx(Ft,{size:16})}),j.jsx("button",{className:""+(e.isActive?"text-orange-600 hover:text-orange-800":"text-green-600 hover:text-green-800"),onClick:()=>(async(e,s)=>{try{z(!0),await As.post(`/admin/cars/${e}/toggle-status`,{is_active:!s}),await v()}catch(t){}finally{z(!1)}})(e.id,e.isActive),disabled:_,title:e.isActive?"Deactivate Car":"Activate Car",children:e.isActive?j.jsx(Wt,{size:16}):j.jsx(ut,{size:16})}),j.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>ge(e.id),disabled:_,title:"Delete Car",children:j.jsx(Bt,{size:16})})]})})]},e.id)))})]})}):j.jsxs("div",{className:"text-center py-8",children:[j.jsx(xt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("p",{className:"text-gray-600",children:"No cars found"})]})]}),"drivers"===P&&j.jsxs("div",{className:"p-6",children:[j.jsxs("div",{className:"flex justify-between items-center mb-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Drivers Management"}),j.jsxs("div",{className:"text-sm text-gray-600",children:["Total Drivers: ",u.length]})]}),g?j.jsx("div",{className:"flex justify-center py-8",children:j.jsx(nr,{size:"lg"})}):f?j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:j.jsx("p",{className:"text-error-600",children:f})}):u.length>0?j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Driver"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Experience"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"License Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map((e=>j.jsxs("tr",{children:[j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:j.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:j.jsx($t,{className:"h-5 w-5 text-gray-600"})})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),j.jsxs("div",{className:"text-sm text-gray-500",children:["Age: ",e.age]})]})]})}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.experience," years"]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.location}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.pricePerHour,"/hr"]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("verified"===e.licenseVerificationStatus?"bg-green-100 text-green-800":"pending"===e.licenseVerificationStatus?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.licenseVerificationStatus})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{Z(e),B(!0)},title:"View Details",children:j.jsx(jt,{size:16})}),"pending"===e.licenseVerificationStatus&&j.jsxs(j.Fragment,{children:[j.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>pe(e.id,"verified"),disabled:_,title:"Approve Driver",children:j.jsx(ut,{size:16})}),j.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>pe(e.id,"rejected"),disabled:_,title:"Reject Driver",children:j.jsx(Wt,{size:16})})]}),j.jsx("button",{className:""+(e.isActive?"text-orange-600 hover:text-orange-800":"text-green-600 hover:text-green-800"),onClick:()=>(async(e,s)=>{try{z(!0),await As.post(`/admin/drivers/${e}/toggle-status`,{is_active:!s}),await N()}catch(t){}finally{z(!1)}})(e.id,e.isActive),disabled:_,title:e.isActive?"Deactivate Driver":"Activate Driver",children:e.isActive?j.jsx(Wt,{size:16}):j.jsx(ut,{size:16})})]})})]},e.id)))})]})}):j.jsxs("div",{className:"text-center py-8",children:[j.jsx($t,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("p",{className:"text-gray-600",children:"No drivers found"})]})]}),"bookings"===P&&j.jsxs("div",{className:"p-6",children:[j.jsxs("div",{className:"flex justify-between items-center mb-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Bookings Management"}),j.jsxs("div",{className:"text-sm text-gray-600",children:["Total Bookings: ",h.length]})]}),g?j.jsx("div",{className:"flex justify-center py-8",children:j.jsx(nr,{size:"lg"})}):f?j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:j.jsx("p",{className:"text-error-600",children:f})}):h.length>0?j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Booking ID"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Price"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map((e=>j.jsxs("tr",{children:[j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["User #",e.userId]}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.itemType," #",e.itemId]}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[new Date(e.startTime).toLocaleDateString()," - ",new Date(e.endTime).toLocaleDateString()]}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.totalPrice]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"cancelled"===e.status?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.status})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{Q(car),H(!0)},title:"View Details",children:j.jsx(jt,{size:16})}),j.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{Q(car),se(!0),H(!0)},title:"Edit Car",children:j.jsx(Ft,{size:16})}),j.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>ge(car.id),disabled:_,title:"Delete Car",children:j.jsx(Bt,{size:16})})]})})]},e.id)))})]})}):j.jsxs("div",{className:"text-center py-8",children:[j.jsx(mt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("p",{className:"text-gray-600",children:"No bookings found"})]})]}),"gps-requests"===P&&j.jsxs("div",{className:"p-6",children:[j.jsxs("div",{className:"flex justify-between items-center mb-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"GPS Installation Requests"}),j.jsxs("div",{className:"text-sm text-gray-600",children:["Pending Requests: ",p.filter((e=>"pending"===e.status)).length]})]}),g?j.jsx("div",{className:"flex justify-center py-8",children:j.jsx(nr,{size:"lg"})}):f?j.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:j.jsx("p",{className:"text-error-600",children:f})}):p.length>0?j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request ID"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vehicle"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map((e=>j.jsxs("tr",{children:[j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.user.name}),j.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.car_make," ",e.car_model," (",e.car_year,")",j.jsx("br",{}),j.jsx("span",{className:"text-xs text-gray-500",children:e.license_plate})]}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.contact_phone}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full "+("pending"===e.status?"bg-yellow-100 text-yellow-800":"approved"===e.status?"bg-blue-100 text-blue-800":"completed"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.created_at).toLocaleDateString()}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:["pending"===e.status&&j.jsxs(j.Fragment,{children:[j.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>A(e.id,"approved"),disabled:_,title:"Approve Request",children:j.jsx(ht,{size:16})}),j.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>A(e.id,"rejected"),disabled:_,title:"Reject Request",children:j.jsx(Gt,{size:16})})]}),"approved"===e.status&&j.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>A(e.id,"completed"),disabled:_,title:"Mark as Completed",children:j.jsx(ht,{size:16})}),j.jsx("button",{className:"text-gray-600 hover:text-gray-800",onClick:()=>{alert(`Reason: ${e.reason}\n\nPreferred Date: ${e.preferred_installation_date||"Not specified"}\n\nAdmin Notes: ${e.admin_notes||"None"}`)},title:"View Details",children:j.jsx(jt,{size:16})})]})})]},e.id)))})]})}):j.jsxs("div",{className:"text-center py-8",children:[j.jsx(At,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("p",{className:"text-gray-600",children:"No GPS installation requests found"}),j.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"GPS installation requests will appear here when users request GPS tracking for their vehicles."})]})]}),"settings"===P&&j.jsxs("div",{className:"p-6",children:[j.jsx("div",{className:"flex justify-between items-center mb-6",children:j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Admin Settings"})}),j.jsx("div",{className:"max-w-md",children:j.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),j.jsx(Jt,{onClick:()=>O(!0),className:"w-full",children:"Change Admin Password"})]})})]})]}),I&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),j.jsx(br,{type:"password",value:te,onChange:e=>re(e.target.value),placeholder:"Enter current password"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),j.jsx(br,{type:"password",value:ae,onChange:e=>ie(e.target.value),placeholder:"Enter new password"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),j.jsx(br,{type:"password",value:le,onChange:e=>ne(e.target.value),placeholder:"Confirm new password"})]}),ce&&j.jsx("div",{className:"text-red-600 text-sm",children:ce})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[j.jsx(Jt,{variant:"outline",onClick:()=>{O(!1),re(""),ie(""),ne(""),oe("")},children:"Cancel"}),j.jsx(Jt,{onClick:async()=>{var e,s;if(oe(""),ae===le)if(ae.length<6)oe("Password must be at least 6 characters");else try{z(!0),await As.post("/users/update-password",{current_password:te,new_password:ae,new_password_confirmation:le}),re(""),ie(""),ne(""),O(!1),alert("Password changed successfully!")}catch(t){oe((null==(s=null==(e=t.response)?void 0:e.data)?void 0:s.message)||"Failed to change password")}finally{z(!1)}else oe("Passwords do not match")},disabled:_||!te||!ae||!le,children:_?"Changing...":"Change Password"})]})]})}),F&&G&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Details"}),j.jsxs("div",{className:"space-y-3",children:[j.jsxs("div",{children:[j.jsx("strong",{children:"Name:"})," ",G.name]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Email:"})," ",G.email]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Role:"})," ",G.role]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Created:"})," ",new Date(G.createdAt).toLocaleDateString()]}),G.licenseVerificationStatus&&j.jsxs("div",{children:[j.jsx("strong",{children:"License Status:"})," ",G.licenseVerificationStatus]})]}),j.jsx("div",{className:"flex justify-end mt-6",children:j.jsx(Jt,{variant:"outline",onClick:()=>{q(!1),J(null)},children:"Close"})})]})}),U&&K&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Driver Details"}),j.jsxs("div",{className:"space-y-3",children:[j.jsxs("div",{children:[j.jsx("strong",{children:"Name:"})," ",K.name]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Age:"})," ",K.age]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Experience:"})," ",K.experience," years"]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Location:"})," ",K.location]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Price/Hour:"})," $",K.pricePerHour]}),j.jsxs("div",{children:[j.jsx("strong",{children:"License Status:"})," ",K.licenseVerificationStatus]}),j.jsxs("div",{children:[j.jsx("strong",{children:"Specialties:"})," ",K.specialties.join(", ")]})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:["pending"===K.licenseVerificationStatus&&j.jsxs(j.Fragment,{children:[j.jsx(Jt,{variant:"outline",onClick:()=>{pe(K.id,"rejected"),B(!1),Z(null)},disabled:_,children:"Reject"}),j.jsx(Jt,{onClick:()=>{pe(K.id,"verified"),B(!1),Z(null)},disabled:_,children:"Approve"})]}),j.jsx(Jt,{variant:"outline",onClick:()=>{B(!1),Z(null)},children:"Close"})]})]})}),$&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New User"}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),j.jsx(br,{value:de.name,onChange:e=>me({...de,name:e.target.value}),placeholder:"Enter user name"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),j.jsx(br,{type:"email",value:de.email,onChange:e=>me({...de,email:e.target.value}),placeholder:"Enter email address"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),j.jsxs("select",{value:de.role,onChange:e=>me({...de,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[j.jsx("option",{value:"client",children:"Client"}),j.jsx("option",{value:"owner",children:"Owner"}),j.jsx("option",{value:"driver",children:"Driver"}),j.jsx("option",{value:"admin",children:"Admin"})]})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),j.jsx(br,{type:"password",value:de.password,onChange:e=>me({...de,password:e.target.value}),placeholder:"Enter password"})]})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[j.jsx(Jt,{variant:"outline",onClick:()=>{V(!1),me({name:"",email:"",role:"client",password:""})},children:"Cancel"}),j.jsx(Jt,{onClick:async()=>{try{z(!0),await As.post("/admin/users",de),await b(),V(!1),me({name:"",email:"",role:"client",password:""})}catch(e){}finally{z(!1)}},disabled:_||!de.name||!de.email||!de.password,children:_?"Creating...":"Create User"})]})]})}),F&&ee&&G&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Edit User"}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),j.jsx(br,{value:de.name,onChange:e=>me({...de,name:e.target.value}),placeholder:"Enter user name"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),j.jsx(br,{type:"email",value:de.email,onChange:e=>me({...de,email:e.target.value}),placeholder:"Enter email address"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),j.jsxs("select",{value:de.role,onChange:e=>me({...de,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[j.jsx("option",{value:"client",children:"Client"}),j.jsx("option",{value:"owner",children:"Owner"}),j.jsx("option",{value:"driver",children:"Driver"}),j.jsx("option",{value:"admin",children:"Admin"})]})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password (optional)"}),j.jsx(br,{type:"password",value:de.password,onChange:e=>me({...de,password:e.target.value}),placeholder:"Leave blank to keep current password"})]})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[j.jsx(Jt,{variant:"outline",onClick:()=>{q(!1),se(!1),J(null),me({name:"",email:"",role:"client",password:""})},children:"Cancel"}),j.jsx(Jt,{onClick:async()=>{if(G)try{z(!0),await As.put(`/admin/users/${G.id}`,de),await b(),q(!1),se(!1),J(null),me({name:"",email:"",role:"client",password:""})}catch(e){}finally{z(!1)}},disabled:_||!de.name||!de.email,children:_?"Updating...":"Update User"})]})]})}),Y&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Car"}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),j.jsx(br,{value:xe.make,onChange:e=>ue({...xe,make:e.target.value}),placeholder:"Enter car make (e.g., Toyota)"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model"}),j.jsx(br,{value:xe.model,onChange:e=>ue({...xe,model:e.target.value}),placeholder:"Enter car model (e.g., Camry)"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),j.jsx(br,{type:"number",value:xe.year,onChange:e=>ue({...xe,year:e.target.value}),placeholder:"Enter year (e.g., 2022)"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price per Hour ($)"}),j.jsx(br,{type:"number",value:xe.pricePerHour,onChange:e=>ue({...xe,pricePerHour:e.target.value}),placeholder:"Enter price per hour"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),j.jsx(br,{value:xe.location,onChange:e=>ue({...xe,location:e.target.value}),placeholder:"Enter location"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign to Owner"}),j.jsxs("select",{value:xe.ownerId,onChange:e=>ue({...xe,ownerId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:[j.jsx("option",{value:"",children:"Select an owner"}),he.map((e=>j.jsxs("option",{value:e.id,children:[e.name," (",e.email,")"]},e.id)))]})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Images"}),j.jsx("input",{type:"file",multiple:!0,accept:"image/*",onChange:e=>{const s=Array.from(e.target.files||[]);ue({...xe,images:s})},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"}),j.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Select multiple images for the car"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),j.jsx("textarea",{value:xe.description,onChange:e=>ue({...xe,description:e.target.value}),placeholder:"Enter car description",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3})]})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[j.jsx(Jt,{variant:"outline",onClick:()=>{W(!1),ue({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})},children:"Cancel"}),j.jsx(Jt,{onClick:async()=>{try{z(!0);const e=new FormData;e.append("make",xe.make),e.append("model",xe.model),e.append("year",xe.year),e.append("pricePerHour",xe.pricePerHour),e.append("location",xe.location),e.append("description",xe.description),e.append("ownerId",xe.ownerId),xe.images.forEach(((s,t)=>{e.append(`images[${t}]`,s)})),await As.post("/admin/cars",e,{headers:{"Content-Type":"multipart/form-data"}}),await v(),W(!1),ue({make:"",model:"",year:"",pricePerHour:"",location:"",description:"",ownerId:"",images:[]})}catch(e){}finally{z(!1)}},disabled:_||!xe.make||!xe.model||!xe.year||!xe.pricePerHour||!xe.ownerId,children:_?"Creating...":"Create Car"})]})]})}),M&&ee&&X&&j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:j.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Edit Car"}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),j.jsx(br,{value:xe.make,onChange:e=>ue({...xe,make:e.target.value}),placeholder:"Enter car make"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model"}),j.jsx(br,{value:xe.model,onChange:e=>ue({...xe,model:e.target.value}),placeholder:"Enter car model"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),j.jsx(br,{type:"number",value:xe.year,onChange:e=>ue({...xe,year:e.target.value}),placeholder:"Enter year"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price per Hour ($)"}),j.jsx(br,{type:"number",value:xe.pricePerHour,onChange:e=>ue({...xe,pricePerHour:e.target.value}),placeholder:"Enter price per hour"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),j.jsx(br,{value:xe.location,onChange:e=>ue({...xe,location:e.target.value}),placeholder:"Enter location"})]}),j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),j.jsx("textarea",{value:xe.description,onChange:e=>ue({...xe,description:e.target.value}),placeholder:"Enter car description",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3})]})]}),j.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[j.jsx(Jt,{variant:"outline",onClick:()=>{H(!1),se(!1),Q(null),ue({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})},children:"Cancel"}),j.jsx(Jt,{onClick:async()=>{if(X)try{z(!0),await As.put(`/admin/cars/${X.id}`,xe),await v(),H(!1),se(!1),Q(null),ue({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})}catch(e){}finally{z(!1)}},disabled:_||!xe.make||!xe.model||!xe.year||!xe.pricePerHour,children:_?"Updating...":"Update Car"})]})]})})]})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),j.jsx("p",{className:"text-error-600",children:"You must be logged in as an administrator to access this page."}),j.jsxs("div",{className:"mt-4 text-sm text-gray-600",children:[j.jsxs("p",{children:["Current user: ",(null==r?void 0:r.name)||"Not logged in"]}),j.jsxs("p",{children:["Role: ",(null==r?void 0:r.role)||"No role"]}),j.jsx("p",{className:"mt-2",children:"Please login with admin credentials:"}),j.jsx("p",{children:"Email: <EMAIL>"}),j.jsx("p",{children:"Password: password"})]}),j.jsx(Jt,{className:"mt-4",onClick:()=>s("/login"),children:"Log In"})]})})})},wr=()=>{const{login:s,isLoading:a}=$s(),i=t(),[l,n]=e.useState(""),[c,o]=e.useState(""),[d,m]=e.useState(null);e.useEffect((()=>{const e=localStorage.getItem("auth_token"),s=localStorage.getItem("user");(e||s)&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"))}),[]);return j.jsx(Xt,{children:j.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"max-w-md w-full",children:[j.jsxs("div",{className:"text-center mb-8",children:[j.jsx(xt,{className:"h-12 w-12 text-primary-600 mx-auto"}),j.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Log in to Park & Rent"}),j.jsx("p",{className:"mt-2 text-gray-600",children:"Access your account and manage your car rentals."})]}),j.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[d&&j.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:j.jsxs("div",{className:"flex",children:[j.jsx(lt,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),j.jsx("p",{className:"text-sm text-error-600",children:d})]})}),j.jsxs("form",{className:"space-y-6",onSubmit:async e=>{e.preventDefault(),m(null);try{await s(l,c);const e=localStorage.getItem("user");if(e){switch(JSON.parse(e).role){case"admin":i("/admin");break;case"owner":i("/owner");break;case"driver":i("/driver");break;default:i("/")}}else i("/")}catch(t){m(t instanceof Error?t.message:"An error occurred during login")}},children:[j.jsx(br,{id:"email",type:"email",label:"Email address",value:l,onChange:e=>n(e.target.value),required:!0}),j.jsx(br,{id:"password",type:"password",label:"Password",value:c,onChange:e=>o(e.target.value),required:!0}),j.jsxs("div",{className:"flex items-center justify-between",children:[j.jsxs("div",{className:"flex items-center",children:[j.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),j.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),j.jsx("div",{className:"text-sm",children:j.jsx("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})]}),j.jsx(Jt,{type:"submit",fullWidth:!0,disabled:a,children:a?"Logging in...":"Log in"})]}),j.jsxs("div",{className:"mt-6",children:[j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-0 flex items-center",children:j.jsx("div",{className:"w-full border-t border-gray-300"})}),j.jsx("div",{className:"relative flex justify-center text-sm",children:j.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),j.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[j.jsx("div",{children:j.jsx(Jt,{fullWidth:!0,variant:"outline",className:"flex justify-center",children:"Google"})}),j.jsx("div",{children:j.jsx(Jt,{fullWidth:!0,variant:"outline",className:"flex justify-center",children:"Facebook"})})]})]}),j.jsx("div",{className:"mt-6 text-center",children:j.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",j.jsx(r,{to:"/signup",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign up"})]})})]})]})})})},kr=()=>{const{register:s,isLoading:a}=$s(),i=t(),[l,n]=e.useState(""),[c,o]=e.useState(""),[d,m]=e.useState(""),[x,u]=e.useState(""),[h,p]=e.useState("client"),[g,f]=e.useState(""),[y,b]=e.useState(null);e.useEffect((()=>{const e=localStorage.getItem("auth_token"),s=localStorage.getItem("user");(e||s)&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"))}),[]);return j.jsx(Xt,{children:j.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"max-w-md w-full",children:[j.jsxs("div",{className:"text-center mb-8",children:[j.jsxs("div",{className:"flex justify-center space-x-2",children:[j.jsx(xt,{className:"h-12 w-12 text-primary-600"}),j.jsx($t,{className:"h-12 w-12 text-primary-600"})]}),j.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Create your account"}),j.jsx("p",{className:"mt-2 text-gray-600",children:"Join Park & Rent to rent cars, list your car, or register as a driver."})]}),j.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[y&&j.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:j.jsxs("div",{className:"flex",children:[j.jsx(lt,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),j.jsx("p",{className:"text-sm text-error-600",children:y})]})}),j.jsxs("form",{className:"space-y-6",onSubmit:async e=>{if(e.preventDefault(),b(null),d===x)if("owner"!==h&&"driver"!==h||g)try{await s({name:l,email:c,role:h,phoneNumber:"owner"===h||"driver"===h?g:void 0},d),"client"===h?i("/account/verification"):"owner"===h?i("/owner/dashboard"):"driver"===h&&i("/driver/register")}catch(t){b(t instanceof Error?t.message:"An error occurred during registration")}else b("Phone number is required for car owners and drivers");else b("Passwords do not match")},children:[j.jsxs("div",{className:"mb-4",children:[j.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"I want to:"}),j.jsxs("div",{className:"flex flex-wrap gap-4",children:[j.jsxs("label",{className:"flex items-center",children:[j.jsx("input",{type:"radio",name:"userType",value:"client",checked:"client"===h,onChange:()=>p("client"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),j.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"Rent a car"})]}),j.jsxs("label",{className:"flex items-center",children:[j.jsx("input",{type:"radio",name:"userType",value:"owner",checked:"owner"===h,onChange:()=>p("owner"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),j.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"List my car"})]}),j.jsxs("label",{className:"flex items-center",children:[j.jsx("input",{type:"radio",name:"userType",value:"driver",checked:"driver"===h,onChange:()=>p("driver"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),j.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"Register as driver"})]})]})]}),j.jsx(br,{id:"name",label:"Full Name",value:l,onChange:e=>n(e.target.value),required:!0}),j.jsx(br,{id:"email",type:"email",label:"Email address",value:c,onChange:e=>o(e.target.value),required:!0}),("owner"===h||"driver"===h)&&j.jsx(br,{id:"phone",label:"Phone Number",value:g,onChange:e=>f(e.target.value),required:!0}),j.jsx(br,{id:"password",type:"password",label:"Password",value:d,onChange:e=>m(e.target.value),required:!0}),j.jsx(br,{id:"confirmPassword",type:"password",label:"Confirm Password",value:x,onChange:e=>u(e.target.value),required:!0}),j.jsxs("div",{className:"flex items-center",children:[j.jsx("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),j.jsxs("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-700",children:["I agree to the"," ",j.jsx(r,{to:"/terms",className:"font-medium text-primary-600 hover:text-primary-500",children:"Terms of Service"})," ","and"," ",j.jsx(r,{to:"/privacy",className:"font-medium text-primary-600 hover:text-primary-500",children:"Privacy Policy"})]})]}),j.jsx(Jt,{type:"submit",fullWidth:!0,disabled:a,children:a?"Creating account...":"Sign up"})]}),j.jsx("div",{className:"mt-6 text-center",children:j.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",j.jsx(r,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"Log in"})]})})]})]})})})},Sr=({id:s,label:t,onChange:r,error:a,className:i="",required:l=!1,accept:n="image/*",previewUrl:c})=>{const[o,d]=e.useState(c||null);return j.jsxs("div",{className:`mb-4 ${i}`,children:[t&&j.jsxs("label",{htmlFor:s,className:"block text-sm font-medium text-gray-700 mb-1",children:[t," ",l&&j.jsx("span",{className:"text-error-600",children:"*"})]}),o?j.jsxs("div",{className:"relative",children:[j.jsx("img",{src:o,alt:"Preview",className:"w-full h-48 object-cover rounded-md"}),j.jsx("button",{type:"button",onClick:()=>{d(null),r(null)},className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100",children:j.jsx(Gt,{size:16,className:"text-gray-700"})})]}):j.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center",children:[j.jsx(Ht,{size:24,className:"text-gray-400 mb-2"}),j.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Click to upload an image"}),j.jsx("input",{id:s,type:"file",accept:n,onChange:e=>{var s;const t=(null==(s=e.target.files)?void 0:s[0])||null;if(t){const e=new FileReader;e.onload=()=>{d(e.result)},e.readAsDataURL(t),r(t)}else d(null),r(null)},className:"hidden"}),j.jsx(Jt,{variant:"outline",size:"sm",onClick:()=>{var e;return null==(e=document.getElementById(s))?void 0:e.click()},children:"Select Image"})]}),a&&j.jsx("p",{className:"mt-1 text-sm text-error-600",children:a})]})},Cr=()=>{const{user:s,updateUser:r,isLoading:a}=$s(),i=t(),[l,n]=e.useState(null),[c,o]=e.useState(!1),[d,m]=e.useState(null);return"client"!==(null==s?void 0:s.role)?(i("/"),null):"verified"===(null==s?void 0:s.licenseVerificationStatus)?j.jsx(Xt,{children:j.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"max-w-md w-full text-center",children:[j.jsx(ut,{className:"h-16 w-16 text-success-500 mx-auto mb-4"}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"License Verified"}),j.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license has been successfully verified. You can now contact car owners and arrange rentals."}),j.jsx(Jt,{onClick:()=>i("/cars"),children:"Browse Available Cars"})]})})}):"pending"===(null==s?void 0:s.licenseVerificationStatus)?j.jsx(Xt,{children:j.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:j.jsxs("div",{className:"max-w-md w-full text-center",children:[j.jsx("div",{className:"h-16 w-16 mx-auto mb-4 rounded-full bg-yellow-100 flex items-center justify-center",children:j.jsx(lt,{className:"h-10 w-10 text-yellow-500"})}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Verification Pending"}),j.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license is currently being verified. This process usually takes 1-2 business days."}),j.jsx(Jt,{onClick:()=>i("/cars"),children:"Browse Available Cars"})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:j.jsx("div",{className:"max-w-md w-full",children:c?j.jsxs("div",{className:"text-center",children:[j.jsx(ut,{className:"h-16 w-16 text-success-500 mx-auto mb-4"}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"License Uploaded!"}),j.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license has been submitted for verification. We'll review it shortly."}),j.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 mb-6",children:j.jsx("p",{className:"text-success-700",children:"You'll be redirected in a few seconds..."})})]}):j.jsxs(j.Fragment,{children:[j.jsxs("div",{className:"text-center mb-8",children:[j.jsx(Ot,{className:"h-12 w-12 text-primary-600 mx-auto"}),j.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Verify Your Account"}),j.jsx("p",{className:"mt-2 text-gray-600",children:"Upload your driving license to verify your account and gain access to contact car owners."})]}),j.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[d&&j.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:j.jsxs("div",{className:"flex",children:[j.jsx(lt,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),j.jsx("p",{className:"text-sm text-error-600",children:d})]})}),j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),m(null),l)try{const e=URL.createObjectURL(l);await r({licenseImageUrl:e,licenseVerificationStatus:"pending"}),o(!0),setTimeout((()=>{i("/")}),3e3)}catch(s){m(s instanceof Error?s.message:"An error occurred during verification")}else m("Please upload your driving license")},children:[j.jsxs("div",{className:"mb-6",children:[j.jsx(Sr,{id:"license",label:"Driving License",onChange:e=>{n(e),m(null)},required:!0}),j.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Please upload a clear image of your valid driving license. We'll verify it to ensure the safety of our community."})]}),j.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:j.jsxs("div",{className:"flex",children:[j.jsx(lt,{size:18,className:"text-yellow-600 mr-2 flex-shrink-0 mt-0.5"}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Important Information"}),j.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Your license will only be used for verification purposes and will be securely stored. You won't be able to contact car owners until your license is verified."})]})]})}),j.jsx(Jt,{type:"submit",fullWidth:!0,disabled:a||!l,children:a?"Uploading...":"Submit for Verification"})]})]})]})})})})},Ar=()=>{const{ownerCars:s,fetchOwnerCars:t,updateCar:a,deleteCar:i}=Ws(),{user:l,isAuthenticated:n}=$s(),[c,o]=e.useState(!1),[d,m]=e.useState(null),[x,u]=e.useState(null),[h,p]=e.useState([]),[g,f]=e.useState(!0);e.useEffect((()=>{n&&(t(),(async()=>{if(n)try{f(!0);const e=await As.get(Bs.DASHBOARD);e.data.data&&(u(e.data.data.stats),p(e.data.data.recent_chats||[]))}catch(e){e.response}finally{f(!1)}else f(!1)})())}),[n]);return n?j.jsxs(Xt,{children:[j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Owner Dashboard"}),j.jsx("p",{className:"text-gray-600",children:"Manage your car listings and track rental requests."})]}),j.jsxs("div",{className:"mt-4 md:mt-0 flex gap-3",children:[j.jsx(r,{to:"/owner/messages",children:j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(Et,{size:18,className:"mr-2"}),"Messages",x&&x.unread_messages>0&&j.jsx("span",{className:"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1",children:x.unread_messages})]})}),j.jsx(r,{to:"/owner/add-car",children:j.jsxs(Jt,{className:"flex items-center",children:[j.jsx(zt,{size:18,className:"mr-2"}),"Add New Car"]})})]})]}),g&&j.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx(lt,{className:"h-5 w-5 text-blue-600 mr-2"}),j.jsxs("div",{className:"flex-1",children:[j.jsx("p",{className:"text-blue-800 font-medium",children:"Loading dashboard data..."}),j.jsx("p",{className:"text-blue-600 text-sm mt-1",children:"If this takes too long, make sure the Laravel API server is running on port 8000."})]})]})}),!g&&x&&j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[j.jsx(Qt,{className:"p-6 cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{const e=document.querySelector('[data-section="cars"]');e&&e.scrollIntoView({behavior:"smooth"})},children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:j.jsx(xt,{className:"h-6 w-6 text-blue-600"})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Total Cars"}),j.jsx("p",{className:"text-2xl font-bold text-gray-900",children:x.total_cars})]})]})}),j.jsx(r,{to:"/owner/bookings",className:"block",children:j.jsx(Qt,{className:"p-6 cursor-pointer hover:shadow-lg transition-shadow",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:j.jsx(mt,{className:"h-6 w-6 text-green-600"})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Total Bookings"}),j.jsx("p",{className:"text-2xl font-bold text-gray-900",children:x.total_bookings})]})]})})}),j.jsx(r,{to:"/owner/bookings",className:"block",children:j.jsx(Qt,{className:"p-6 cursor-pointer hover:shadow-lg transition-shadow",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:j.jsx(ft,{className:"h-6 w-6 text-yellow-600"})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Pending Bookings"}),j.jsx("p",{className:"text-2xl font-bold text-gray-900",children:x.pending_bookings})]})]})})}),j.jsx(r,{to:"/owner/messages",className:"block",children:j.jsx(Qt,{className:"p-6 cursor-pointer hover:shadow-lg transition-shadow",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:j.jsx(Et,{className:"h-6 w-6 text-purple-600"})}),j.jsxs("div",{className:"ml-4",children:[j.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Unread Messages"}),j.jsx("p",{className:"text-2xl font-bold text-gray-900",children:x.unread_messages})]})]})})})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8","data-section":"cars",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Listed Cars"}),0===s.length?j.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[j.jsx(xt,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No cars listed yet"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"Start earning by adding your first car listing."}),j.jsx(r,{to:"/owner/add-car",children:j.jsx(Jt,{children:"Add Your Car"})})]}):j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[j.jsx("thead",{className:"bg-gray-50",children:j.jsxs("tr",{children:[j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),j.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),j.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((e=>j.jsxs("tr",{children:[j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"h-10 w-16 flex-shrink-0 bg-gray-200 rounded overflow-hidden",children:e.images.length>0&&j.jsx("img",{src:e.images[0],alt:`${e.make} ${e.model}`,className:"h-full w-full object-cover"})}),j.jsx("div",{className:"ml-4",children:j.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[e.year," ",e.make," ",e.model]})})]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[j.jsx(At,{size:16,className:"mr-1"}),e.location]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("div",{className:"flex items-center text-sm font-medium text-gray-900",children:[j.jsx(yt,{size:16,className:"text-primary-600"}),e.pricePerHour,"/hour"]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:j.jsxs("button",{onClick:()=>(async(e,s)=>{await a(e,{isActive:!s})})(e.id,e.isActive),className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium "+(e.isActive?"bg-success-100 text-success-800":"bg-gray-100 text-gray-800"),children:[j.jsx(Ut,{size:14,className:"mr-1 "+(e.isActive?"text-success-600":"text-gray-500")}),e.isActive?"Active":"Inactive"]})}),j.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx(r,{to:`/owner/edit-car/${e.id}`,children:j.jsx("button",{className:"text-primary-600 hover:text-primary-800",children:j.jsx(Ft,{size:18})})}),j.jsx("button",{className:"text-error-600 hover:text-error-800",onClick:()=>{return s=e.id,m(s),void o(!0);var s},children:j.jsx(Bt,{size:18})})]})})]},e.id)))})]})})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[j.jsxs("div",{className:"flex justify-between items-center mb-4",children:[j.jsxs("h2",{className:"text-xl font-bold text-gray-900 flex items-center",children:[j.jsx(Et,{size:20,className:"mr-2 text-blue-500"}),"Messages"]}),j.jsx(r,{to:"/owner/messages",children:j.jsxs(Jt,{variant:"secondary",size:"sm",className:"flex items-center",children:[j.jsx(Et,{size:16,className:"mr-2"}),"Open Messages",x&&x.unread_messages>0&&j.jsx("span",{className:"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1",children:x.unread_messages})]})})]}),j.jsx("div",{className:"text-center py-6",children:j.jsxs("div",{className:"bg-blue-50 rounded-lg p-6",children:[j.jsx(Et,{size:48,className:"mx-auto text-blue-500 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Message Center"}),j.jsx("p",{className:"text-gray-600 mb-4",children:"Communicate with customers interested in your cars. Manage all your conversations in one place."}),j.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[j.jsx(r,{to:"/owner/messages",children:j.jsxs(Jt,{className:"flex items-center",children:[j.jsx(Et,{size:16,className:"mr-2"}),"View All Messages"]})}),j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(Yt,{size:16,className:"mr-2"}),"Customer Support"]})]})]})})]}),!g&&h.length>0&&j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[j.jsxs("div",{className:"flex justify-between items-center mb-4",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Recent Messages"}),j.jsx(r,{to:"/owner/messages",children:j.jsx(Jt,{variant:"secondary",size:"sm",children:"View All Messages"})})]}),h.length>0?j.jsx("div",{className:"space-y-4",children:h.slice(0,5).map((e=>{const s=e.user.id===(null==l?void 0:l.id)?e.recipient:e.user,t=e.messages[0],r=t&&t.sender_id!==(null==l?void 0:l.id);return j.jsxs("div",{className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50",children:[j.jsx("div",{className:"flex-shrink-0",children:j.jsx("div",{className:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center",children:j.jsx(Yt,{className:"h-5 w-5 text-gray-600"})})}),j.jsx("div",{className:"ml-4 flex-1",children:j.jsxs("div",{className:"flex justify-between items-start",children:[j.jsxs("div",{children:[j.jsx("h4",{className:"text-sm font-medium "+(r?"text-gray-900 font-semibold":"text-gray-700"),children:s.name}),j.jsx("p",{className:`text-sm ${r?"text-gray-900":"text-gray-500"} truncate max-w-xs`,children:t?t.content:"No messages yet"})]}),j.jsx("div",{className:"text-xs text-gray-400",children:t&&new Date(t.created_at).toLocaleDateString()})]})}),r&&j.jsx("div",{className:"flex-shrink-0",children:j.jsx("div",{className:"w-2 h-2 bg-blue-600 rounded-full"})})]},e.id)}))}):j.jsxs("div",{className:"text-center py-8",children:[j.jsx(Et,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No messages yet"}),j.jsx("p",{className:"text-gray-600",children:"Messages from customers interested in your cars will appear here."})]})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Apply for GPS Installation"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"We offer GPS installation services for car owners who want to track their vehicles. Apply below to request this service."}),j.jsx(r,{to:"/owner/gps-request",children:j.jsx(Jt,{variant:"secondary",children:"Request GPS Installation"})})]})]}),c&&j.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:j.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[j.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:j.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),j.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),j.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[j.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:j.jsxs("div",{className:"sm:flex sm:items-start",children:[j.jsx("div",{className:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-error-100 sm:mx-0 sm:h-10 sm:w-10",children:j.jsx(lt,{className:"h-6 w-6 text-error-600"})}),j.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[j.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Car Listing"}),j.jsx("div",{className:"mt-2",children:j.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this car listing? This action cannot be undone."})})]})]})}),j.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[j.jsx("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-error-600 text-base font-medium text-white hover:bg-error-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-error-500 sm:ml-3 sm:w-auto sm:text-sm",onClick:async()=>{d&&(await i(d),o(!1),m(null))},children:"Delete"}),j.jsx("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:()=>o(!1),children:"Cancel"})]})]})]})})]}):j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(lt,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in as an owner to access this dashboard."}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["Test credentials: ",j.jsx("br",{}),j.jsx("strong",{children:"Email:"})," <EMAIL> ",j.jsx("br",{}),j.jsx("strong",{children:"Password:"})," password"]}),j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"mx-auto",children:"Go to Login"})})]})]})})})},Pr=()=>{const{user:s,isAuthenticated:t}=$s(),[a,i]=e.useState([]),[l,n]=e.useState(null),[c,o]=e.useState(""),[d,m]=e.useState(!0),[x,u]=e.useState(!1),h=e.useRef(null),p=async()=>{if(l&&c.trim())try{u(!0);const e=await As.post(Bs.MESSAGES,{chat_id:l.id,content:c.trim()});e.data.data&&(n((s=>s?{...s,messages:[...s.messages,e.data.data]}:null)),i((s=>s.map((s=>s.id===l.id?{...s,messages:[e.data.data],last_message_at:e.data.data.created_at}:s)))),o(""),setTimeout((()=>{var e;null==(e=h.current)||e.scrollIntoView({behavior:"smooth"})}),100))}catch(e){}finally{u(!1)}};e.useEffect((()=>{t&&(async()=>{try{m(!0);const e=await As.get(Bs.MESSAGES);e.data.data&&i(e.data.data.data||[])}catch(e){}finally{m(!1)}})()}),[t]);const g=e=>{const s=new Date(e),t=((new Date).getTime()-s.getTime())/36e5;return t<24?s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):t<168?s.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):s.toLocaleDateString([],{month:"short",day:"numeric"})};return t?j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-4 max-w-7xl",children:[j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsx(r,{to:"/owner/dashboard",className:"mr-4",children:j.jsxs(Jt,{variant:"secondary",size:"sm",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Dashboard"]})}),j.jsxs("div",{children:[j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-1",children:"Messages"}),j.jsx("p",{className:"text-gray-600 text-sm",children:"Communicate with customers interested in your cars."})]})]}),j.jsx("div",{className:"bg-white rounded-lg shadow-sm border",style:{height:"500px"},children:j.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 h-full",children:[j.jsxs("div",{className:"lg:col-span-1 border-r border-gray-200 h-full flex flex-col",children:[j.jsx("div",{className:"p-4 border-b border-gray-200 bg-gray-50",children:j.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Conversations"})}),j.jsx("div",{className:"flex-1 overflow-y-auto",style:{maxHeight:"calc(100% - 60px)"},children:d?j.jsx("div",{className:"p-4 text-center text-gray-500",children:"Loading conversations..."}):0===a.length?j.jsxs("div",{className:"p-4 text-center",children:[j.jsx(Et,{size:48,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No conversations yet"}),j.jsx("p",{className:"text-gray-600 text-sm",children:"Messages from customers will appear here when they contact you about your cars."})]}):j.jsx("div",{className:"divide-y divide-gray-200",children:a.map((e=>{const t=e.user.id===(null==s?void 0:s.id)?e.recipient:e.user,r=e.messages[0],a=r&&r.sender_id!==(null==s?void 0:s.id)&&!r.is_read;return j.jsx("div",{onClick:()=>{n(e),(async e=>{try{const s=await As.get(`${Bs.CHATS}/${e}`);s.data.data&&(n(s.data.data),i((t=>t.map((t=>t.id===e?s.data.data:t)))),setTimeout((()=>{var e;null==(e=h.current)||e.scrollIntoView({behavior:"smooth"})}),100))}catch(s){}})(e.id)},className:"p-4 cursor-pointer hover:bg-gray-50 "+((null==l?void 0:l.id)===e.id?"bg-blue-50 border-r-2 border-blue-500":""),children:j.jsxs("div",{className:"flex items-start space-x-3",children:[j.jsx("div",{className:"flex-shrink-0",children:j.jsx("div",{className:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center",children:j.jsx(Yt,{className:"h-5 w-5 text-gray-600"})})}),j.jsxs("div",{className:"flex-1 min-w-0",children:[j.jsxs("div",{className:"flex justify-between items-start",children:[j.jsx("h4",{className:"text-sm font-medium truncate "+(a?"text-gray-900 font-semibold":"text-gray-700"),children:t.name}),j.jsxs("div",{className:"flex items-center space-x-1",children:[r&&j.jsx("span",{className:"text-xs text-gray-400",children:g(r.created_at)}),a&&j.jsx("div",{className:"w-2 h-2 bg-blue-600 rounded-full"})]})]}),j.jsx("p",{className:"text-sm truncate "+(a?"text-gray-900":"text-gray-500"),children:r?r.content:"No messages yet"})]})]})},e.id)}))})})]}),j.jsx("div",{className:"lg:col-span-2 h-full flex flex-col",children:l?j.jsxs(j.Fragment,{children:[j.jsx("div",{className:"p-4 border-b border-gray-200",children:j.jsxs("div",{className:"flex items-center space-x-3",children:[j.jsx("div",{className:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center",children:j.jsx(Yt,{className:"h-5 w-5 text-gray-600"})}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:l.user.id===(null==s?void 0:s.id)?l.recipient.name:l.user.name}),j.jsx("p",{className:"text-sm text-gray-500",children:l.user.id===(null==s?void 0:s.id)?l.recipient.email:l.user.email})]})]})}),j.jsxs("div",{className:"flex-1 flex flex-col",style:{height:"380px"},children:[j.jsxs("div",{className:"p-4 space-y-3 bg-gray-50",style:{height:"320px",overflowY:"scroll",border:"1px solid #e5e7eb",scrollbarWidth:"thin"},children:[l.messages&&l.messages.length>0?l.messages.map((e=>{const t=String(e.sender_id)===String(null==s?void 0:s.id),r=l.user.id===(null==s?void 0:s.id)?l.recipient:l.user,a=t?"You":r.name;return j.jsxs("div",{className:"flex "+(t?"justify-end":"justify-start"),children:[j.jsxs("div",{className:"max-w-xs lg:max-w-md "+(t?"order-2":"order-1"),children:[j.jsxs("div",{className:"text-xs mb-1 "+(t?"text-right text-green-600":"text-left text-gray-600"),children:[a," ",t?"(Owner)":"(Client)"]}),j.jsxs("div",{className:"px-4 py-3 rounded-lg shadow-sm "+(t?"bg-green-600 text-white rounded-br-sm":"bg-blue-100 text-gray-900 border border-blue-200 rounded-bl-sm"),children:[j.jsx("p",{className:"text-sm leading-relaxed",children:e.content}),j.jsxs("p",{className:"text-xs mt-2 "+(t?"text-green-100":"text-blue-600"),children:[new Date(e.created_at).toLocaleDateString()," at ",g(e.created_at)]})]})]}),j.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold "+(t?"bg-green-100 text-green-600 ml-2 order-3":"bg-blue-100 text-blue-600 mr-2 order-0"),children:t?"O":"C"})]},e.id)})):j.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:j.jsxs("div",{className:"text-center",children:[j.jsx(Et,{size:48,className:"mx-auto mb-4 text-gray-300"}),j.jsx("p",{children:"No messages yet"}),j.jsx("p",{className:"text-sm",children:"Start the conversation below"})]})}),j.jsx("div",{ref:h})]}),j.jsx("div",{className:"p-4 border-t border-gray-200 bg-white",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx("input",{type:"text",value:c,onChange:e=>o(e.target.value),onKeyPress:e=>"Enter"===e.key&&p(),placeholder:"Type your message...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:x}),j.jsx(Jt,{onClick:p,disabled:!c.trim()||x,className:"px-4",children:j.jsx(Lt,{size:16})})]})})]})]}):j.jsx("div",{className:"flex-1 flex items-center justify-center",children:j.jsxs("div",{className:"text-center",children:[j.jsx(Et,{size:64,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a conversation"}),j.jsx("p",{className:"text-gray-600",children:"Choose a conversation from the left to start messaging."})]})})})]})})]})}):j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(Et,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in as an owner to access your messages."}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["Test credentials: ",j.jsx("br",{}),j.jsx("strong",{children:"Email:"})," <EMAIL> ",j.jsx("br",{}),j.jsx("strong",{children:"Password:"})," password"]}),j.jsxs("div",{className:"flex gap-3 justify-center",children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"flex items-center",children:"Go to Login"})}),j.jsx(r,{to:"/owner/dashboard",children:j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Dashboard"]})})]})]})]})})})},Er=()=>{const{user:s,isAuthenticated:t}=$s(),[a,i]=e.useState([]),[l,n]=e.useState(!0),[c,o]=e.useState(1),[d,m]=e.useState(1),[x,u]=e.useState(""),[h,p]=e.useState(!1),g=async(e,s)=>{try{await As.patch(`${Bs.BOOKINGS}/${e}/status`,{status:s}),i((t=>t.map((t=>t.id===e?{...t,status:s}:t))))}catch(t){}};if(e.useEffect((()=>{(async(e=1,s="")=>{if(t)try{n(!0);const t=new URLSearchParams;s&&t.append("status",s),t.append("page",e.toString());const r=await As.get(`${Bs.BOOKINGS}?${t}`);if(r.data.data){const e=r.data.data;i(e.data||[]),o(e.current_page||1),m(e.last_page||1)}}catch(r){}finally{n(!1)}else n(!1)})(c,x)}),[t,c,x]),!t)return j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(mt,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in as an owner to access your bookings."}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["Test credentials: ",j.jsx("br",{}),j.jsx("strong",{children:"Email:"})," <EMAIL> ",j.jsx("br",{}),j.jsx("strong",{children:"Password:"})," password"]}),j.jsxs("div",{className:"flex gap-3 justify-center",children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"flex items-center",children:"Go to Login"})}),j.jsx(r,{to:"/owner/dashboard",children:j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Dashboard"]})})]})]})]})})});const f=e=>{switch(e){case"confirmed":return j.jsx(ut,{className:"h-5 w-5 text-green-500"});case"cancelled":return j.jsx(Wt,{className:"h-5 w-5 text-red-500"});case"completed":return j.jsx(ut,{className:"h-5 w-5 text-blue-500"});default:return j.jsx(lt,{className:"h-5 w-5 text-yellow-500"})}},y=e=>{switch(e){case"confirmed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-yellow-100 text-yellow-800"}},b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),v=(e,s)=>{const t=new Date(e),r=new Date(s);return Math.ceil((r.getTime()-t.getTime())/36e5)};return j.jsx(Xt,{children:j.jsxs("div",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"flex items-center justify-between mb-8",children:[j.jsxs("div",{className:"flex items-center",children:[j.jsx(r,{to:"/owner/dashboard",className:"mr-4",children:j.jsxs(Jt,{variant:"secondary",size:"sm",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Dashboard"]})}),j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Bookings"}),j.jsx("p",{className:"text-gray-600",children:"Manage bookings for your cars and communicate with customers."})]})]}),j.jsxs(Jt,{variant:"secondary",onClick:()=>p(!h),className:"flex items-center",children:[j.jsx(vt,{size:16,className:"mr-2"}),"Filters"]})]}),h&&j.jsx(Qt,{className:"p-6 mb-6",children:j.jsxs("div",{className:"flex flex-wrap gap-4",children:[j.jsxs("div",{children:[j.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),j.jsxs("select",{value:x,onChange:e=>u(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[j.jsx("option",{value:"",children:"All Statuses"}),j.jsx("option",{value:"pending",children:"Pending"}),j.jsx("option",{value:"confirmed",children:"Confirmed"}),j.jsx("option",{value:"completed",children:"Completed"}),j.jsx("option",{value:"cancelled",children:"Cancelled"})]})]}),j.jsx("div",{className:"flex items-end",children:j.jsx(Jt,{variant:"secondary",onClick:()=>{u(""),o(1)},children:"Clear Filters"})})]})}),l?j.jsxs("div",{className:"text-center py-8",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),j.jsx("p",{className:"mt-4 text-gray-600",children:"Loading bookings..."})]}):0===a.length?j.jsxs(Qt,{className:"p-8 text-center",children:[j.jsx(mt,{size:64,className:"mx-auto text-gray-400 mb-4"}),j.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No bookings found"}),j.jsx("p",{className:"text-gray-600",children:x?`No ${x} bookings found. Try adjusting your filters.`:"You haven't received any bookings yet. Make sure your cars are active and visible to customers."})]}):j.jsx("div",{className:"space-y-4",children:a.map((e=>j.jsx(Qt,{className:"p-6",children:j.jsxs("div",{className:"flex items-start justify-between",children:[j.jsxs("div",{className:"flex-1",children:[j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsxs("div",{className:"flex items-center mr-4",children:[f(e.status),j.jsx("span",{className:`ml-2 px-2 py-1 rounded-full text-xs font-medium ${y(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),j.jsxs("span",{className:"text-sm text-gray-500",children:["Booking #",String(e.id).slice(0,8)]})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[j.jsxs("div",{className:"flex items-center",children:[j.jsx(Vt,{className:"h-5 w-5 text-gray-400 mr-3"}),j.jsxs("div",{children:[j.jsx("p",{className:"font-medium text-gray-900",children:e.user.name}),j.jsx("p",{className:"text-sm text-gray-500",children:e.user.email}),e.user.phone&&j.jsx("p",{className:"text-sm text-gray-500",children:e.user.phone})]})]}),j.jsxs("div",{className:"flex items-center",children:[j.jsx(xt,{className:"h-5 w-5 text-gray-400 mr-3"}),j.jsxs("div",{children:[j.jsxs("p",{className:"font-medium text-gray-900",children:[e.car.year," ",e.car.make," ",e.car.model]}),j.jsx("p",{className:"text-sm text-gray-500",children:e.car.license_plate})]})]}),j.jsxs("div",{className:"flex items-center",children:[j.jsx(yt,{className:"h-5 w-5 text-gray-400 mr-3"}),j.jsxs("div",{children:[j.jsxs("p",{className:"font-medium text-gray-900",children:["$",e.total_price]}),j.jsxs("p",{className:"text-sm text-gray-500",children:[v(e.start_time,e.end_time)," hours"]})]})]})]}),j.jsxs("div",{className:"flex items-center mb-4",children:[j.jsx(ft,{className:"h-5 w-5 text-gray-400 mr-3"}),j.jsxs("div",{children:[j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("span",{className:"font-medium",children:"From:"})," ",b(e.start_time)]}),j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("span",{className:"font-medium",children:"To:"})," ",b(e.end_time)]})]})]}),e.notes&&j.jsx("div",{className:"mb-4",children:j.jsxs("p",{className:"text-sm text-gray-600",children:[j.jsx("span",{className:"font-medium",children:"Notes:"})," ",e.notes]})})]}),j.jsxs("div",{className:"flex flex-col gap-2 ml-4",children:["pending"===e.status&&j.jsxs(j.Fragment,{children:[j.jsxs(Jt,{size:"sm",onClick:()=>g(e.id,"confirmed"),className:"flex items-center",children:[j.jsx(ut,{size:14,className:"mr-1"}),"Confirm"]}),j.jsxs(Jt,{size:"sm",variant:"secondary",onClick:()=>g(e.id,"cancelled"),className:"flex items-center",children:[j.jsx(Wt,{size:14,className:"mr-1"}),"Cancel"]})]}),"confirmed"===e.status&&j.jsxs(Jt,{size:"sm",onClick:()=>g(e.id,"completed"),className:"flex items-center",children:[j.jsx(ut,{size:14,className:"mr-1"}),"Complete"]})]})]})},e.id)))}),d>1&&j.jsx("div",{className:"flex justify-center mt-8",children:j.jsxs("div",{className:"flex space-x-2",children:[j.jsx(Jt,{variant:"secondary",disabled:1===c,onClick:()=>o((e=>Math.max(1,e-1))),children:"Previous"}),j.jsxs("span",{className:"flex items-center px-4 py-2 text-sm text-gray-700",children:["Page ",c," of ",d]}),j.jsx(Jt,{variant:"secondary",disabled:c===d,onClick:()=>o((e=>Math.min(d,e+1))),children:"Next"})]})})]})})},_r=()=>{const s=t(),{addCar:a}=Ws(),{user:i,isAuthenticated:l}=$s(),[n,c]=e.useState(!1),[o,d]=e.useState(null),[m,x]=e.useState(!1),[u,h]=e.useState({make:"",model:"",year:(new Date).getFullYear(),description:"",location:"",pricePerHour:0,availabilityNotes:""}),[p,g]=e.useState([]),[f,y]=e.useState(""),[b,v]=e.useState([]),[N,w]=e.useState([]);e.useState(!1);const k=e=>{const{name:s,value:t}=e.target;h({...u,[s]:"year"===s||"pricePerHour"===s?Number(t):t})};return l?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-3xl mx-auto",children:[j.jsxs("div",{className:"flex items-center mb-6",children:[j.jsx("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4",children:"← Back to Dashboard"}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Add New Car"})]}),m?j.jsxs("div",{className:"bg-success-100 border border-success-200 text-success-800 px-4 py-3 rounded mb-6",children:[j.jsx("p",{className:"font-medium",children:"Car added successfully!"}),j.jsx("p",{children:"Redirecting to dashboard..."})]}):j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),u.make&&u.model&&u.description&&u.location&&!(u.pricePerHour<=0))if(0!==b.length)if(0!==p.length){c(!0),d(null);try{if(!i)throw new Error("You must be logged in to add a car");await a({ownerId:i.id,make:u.make,model:u.model,year:u.year,images:[],description:u.description,features:p,location:u.location,pricePerHour:u.pricePerHour,availabilityNotes:u.availabilityNotes,isActive:!0},N),x(!0),b.forEach((e=>URL.revokeObjectURL(e))),setTimeout((()=>{s("/owner/dashboard")}),2e3)}catch(t){d(t instanceof Error?t.message:"Failed to add car")}finally{c(!1)}}else d("Please add at least one feature");else d("Please add at least one image");else d("Please fill in all required fields")},children:[o&&j.jsx("div",{className:"bg-error-100 border border-error-200 text-error-800 px-4 py-3 rounded mb-6",children:o}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Car Details"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"make",className:"block text-sm font-medium text-gray-700 mb-1",children:["Make ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"make",name:"make",value:u.make,onChange:k,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-1",children:["Model ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"model",name:"model",value:u.model,onChange:k,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("label",{htmlFor:"year",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("select",{id:"year",name:"year",value:u.year,onChange:k,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},((e,s)=>(new Date).getFullYear()-s)).map((e=>j.jsx("option",{value:e,children:e},e)))})]}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["Description ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("textarea",{id:"description",name:"description",value:u.description,onChange:k,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:["Location ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(At,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"text",id:"location",name:"location",value:u.location,onChange:k,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"City, Country",required:!0})]})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:["Price per Hour (RWF) ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(yt,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",value:u.pricePerHour,onChange:k,min:"0",step:"1000",className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),j.jsx("textarea",{id:"availabilityNotes",name:"availabilityNotes",value:u.availabilityNotes,onChange:k,rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Available on weekends only"})]})]})}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Features"}),j.jsxs("div",{className:"flex mb-4",children:[j.jsx("input",{type:"text",value:f,onChange:e=>y(e.target.value),className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Add a feature (e.g., Air Conditioning)"}),j.jsxs(Jt,{type:"button",onClick:()=>{f.trim()&&!p.includes(f.trim())&&(g([...p,f.trim()]),y(""))},className:"rounded-l-none",children:[j.jsx(zt,{size:18,className:"mr-1"})," Add"]})]}),0===p.length?j.jsx("div",{className:"text-gray-500 italic mb-4",children:"No features added yet. Please add at least one feature."}):j.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:p.map(((e,s)=>j.jsxs("div",{className:"flex items-center bg-gray-100 px-3 py-1 rounded-full",children:[j.jsx("span",{className:"text-sm",children:e}),j.jsx("button",{type:"button",onClick:()=>(e=>{g(p.filter((s=>s!==e)))})(e),className:"ml-2 text-gray-500 hover:text-error-600",children:j.jsx(Gt,{size:14})})]},s)))})]})}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Images"}),j.jsx("div",{className:"mb-4",children:j.jsxs("label",{htmlFor:"car-images",className:"block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[j.jsx(Ht,{size:36,className:"mx-auto mb-2 text-gray-400"}),j.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Click to select images"}),j.jsx("span",{className:"text-xs text-gray-500",children:"JPG, PNG or WEBP (max. 5MB each)"}),j.jsx("input",{id:"car-images",type:"file",multiple:!0,accept:"image/jpeg,image/png,image/webp",onChange:e=>{if(e.target.files){const s=Array.from(e.target.files),t=s.map((e=>URL.createObjectURL(e)));w((e=>[...e,...s])),v((e=>[...e,...t]))}},className:"hidden"})]})}),0===b.length?j.jsx("div",{className:"text-gray-500 italic mb-4",children:"No images added yet. Please add at least one image."}):j.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:b.map(((e,s)=>j.jsxs("div",{className:"relative group",children:[j.jsx("img",{src:e,alt:`Car image ${s+1}`,className:"w-full h-40 object-cover rounded-md",onError:e=>{e.target.src="https://via.placeholder.com/300x200?text=Image+Error"}}),j.jsx("button",{type:"button",onClick:()=>((e,s)=>{v(b.filter((s=>s!==e))),w((e=>{const t=[...e];return t.splice(s,1),t})),URL.revokeObjectURL(e)})(e,s),className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600",children:j.jsx(Gt,{size:16})})]},s)))}),j.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[j.jsx(wt,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"Selected images will be uploaded when you submit the form."})]})]})}),j.jsxs("div",{className:"flex justify-end space-x-4",children:[j.jsx(Jt,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),j.jsx(Jt,{type:"submit",disabled:n,children:n?"Adding Car...":"Add Car"})]})]})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(xt,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in as an owner to add a car."}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["Test credentials: ",j.jsx("br",{}),j.jsx("strong",{children:"Email:"})," <EMAIL> ",j.jsx("br",{}),j.jsx("strong",{children:"Password:"})," password"]}),j.jsxs("div",{className:"flex gap-3 justify-center",children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"flex items-center",children:"Go to Login"})}),j.jsx(r,{to:"/owner/dashboard",children:j.jsx(Jt,{variant:"secondary",className:"flex items-center",children:"Back to Dashboard"})})]})]})]})})})},zr=()=>{const s=t(),{id:i}=a(),{cars:l,updateCar:n,getCarById:c}=Ws(),{user:o,isAuthenticated:d}=$s(),[m,x]=e.useState(!1),[u,h]=e.useState(null),[p,g]=e.useState(!1),[f,y]=e.useState(!0),b=l.find((e=>e.id===i)),[v,N]=e.useState({make:"",model:"",year:(new Date).getFullYear(),description:"",location:"",pricePerHour:0,availabilityNotes:"",isActive:!0}),[w,k]=e.useState([]),[S,C]=e.useState(""),[A,P]=e.useState([]),[E,_]=e.useState([]);e.useEffect((()=>{i?b?(N({make:b.make,model:b.model,year:b.year,description:b.description,location:b.location,pricePerHour:b.pricePerHour,availabilityNotes:b.availabilityNotes||"",isActive:b.isActive}),k(b.features||[]),P(b.images||[]),y(!1)):c(i).then((()=>{y(!1)})).catch((()=>{h("Car not found"),y(!1)})):s("/owner/dashboard")}),[i,b,s,c]),e.useEffect((()=>{b&&o&&b.ownerId!==o.id&&h("You can only edit your own cars")}),[b,o]);const z=e=>{const{name:s,value:t}=e.target;N({...v,[s]:"year"===s||"pricePerHour"===s?Number(t):t})};return d?f?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"flex items-center justify-center py-12",children:j.jsxs("div",{className:"text-center",children:[j.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),j.jsx("p",{className:"text-gray-600",children:"Loading car details..."})]})})})}):u&&!b?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-3xl mx-auto",children:[j.jsxs("div",{className:"flex items-center mb-6",children:[j.jsx("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4",children:"← Back to Dashboard"}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Car"})]}),j.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[j.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error"}),j.jsx("p",{className:"text-error-600",children:u})]})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-3xl mx-auto",children:[j.jsxs("div",{className:"flex items-center mb-6",children:[j.jsxs("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4 flex items-center",children:[j.jsx(ct,{size:20,className:"mr-1"}),"Back to Dashboard"]}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Car"})]}),p?j.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-8 text-center",children:[j.jsx("div",{className:"w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4",children:j.jsx(xt,{size:32,className:"text-success-600"})}),j.jsx("h2",{className:"text-2xl font-bold text-success-800 mb-4",children:"Car Updated Successfully!"}),j.jsx("p",{className:"text-success-700 mb-6",children:"Your car listing has been updated and is now live on the platform."}),j.jsx(Jt,{onClick:()=>s("/owner/dashboard"),children:"Return to Dashboard"})]}):j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),v.make&&v.model&&v.description&&v.location&&!(v.pricePerHour<=0))if(0!==A.length)if(0!==w.length){x(!0),h(null);try{if(!o||!b)throw new Error("Invalid user or car data");await n(b.id,{make:v.make,model:v.model,year:v.year,description:v.description,features:w,location:v.location,pricePerHour:v.pricePerHour,availabilityNotes:v.availabilityNotes,isActive:v.isActive,images:A}),g(!0),A.forEach((e=>{e.startsWith("blob:")&&URL.revokeObjectURL(e)})),setTimeout((()=>{s("/owner/dashboard")}),2e3)}catch(t){h(t instanceof Error?t.message:"Failed to update car")}finally{x(!1)}}else h("Please add at least one feature");else h("Please keep at least one image");else h("Please fill in all required fields")},children:[u&&j.jsxs("div",{className:"mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start",children:[j.jsx(lt,{size:20,className:"text-error-600 mr-2 flex-shrink-0 mt-0.5"}),j.jsx("div",{className:"text-error-700",children:u})]}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Car Details"}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"make",className:"block text-sm font-medium text-gray-700 mb-1",children:["Make ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"make",name:"make",value:v.make,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Toyota",required:!0})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-1",children:["Model ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"model",name:"model",value:v.model,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Camry",required:!0})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("label",{htmlFor:"year",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("select",{id:"year",name:"year",value:v.year,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},((e,s)=>(new Date).getFullYear()-s)).map((e=>j.jsx("option",{value:e,children:e},e)))})]}),j.jsxs("div",{className:"mb-6",children:[j.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["Description ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("textarea",{id:"description",name:"description",value:v.description,onChange:z,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Describe your car, its condition, and any special features...",required:!0})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:["Location ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(At,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"text",id:"location",name:"location",value:v.location,onChange:z,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"City, Country",required:!0})]})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:["Price per Hour (RWF) ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(yt,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",value:v.pricePerHour,onChange:z,min:"0",step:"1000",className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]})]}),j.jsxs("div",{className:"mb-6",children:[j.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),j.jsx("input",{type:"text",id:"availabilityNotes",name:"availabilityNotes",value:v.availabilityNotes,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Available weekdays 9 AM - 6 PM"})]}),j.jsx("div",{className:"mb-6",children:j.jsxs("label",{className:"flex items-center",children:[j.jsx("input",{type:"checkbox",checked:v.isActive,onChange:e=>N({...v,isActive:e.target.checked}),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),j.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Active (visible on the website)"})]})})]})}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Features"}),j.jsxs("div",{className:"flex mb-4",children:[j.jsx("input",{type:"text",value:S,onChange:e=>C(e.target.value),className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Add a feature (e.g., Air Conditioning)"}),j.jsxs(Jt,{type:"button",onClick:()=>{S.trim()&&!w.includes(S.trim())&&(k([...w,S.trim()]),C(""))},className:"rounded-l-none",children:[j.jsx(zt,{size:18,className:"mr-1"})," Add"]})]}),0===w.length?j.jsx("div",{className:"text-gray-500 italic mb-4",children:"No features added yet. Please add at least one feature."}):j.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:w.map(((e,s)=>j.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800",children:[e,j.jsx("button",{type:"button",onClick:()=>(e=>{k(w.filter(((s,t)=>t!==e)))})(s),className:"ml-2 text-primary-600 hover:text-primary-800",children:j.jsx(Gt,{size:14})})]},s)))}),j.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[j.jsx(wt,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"Add features that make your car attractive to renters (e.g., GPS, Bluetooth, etc.)"})]})]})}),j.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:j.jsxs("div",{className:"p-6",children:[j.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Images"}),j.jsx("div",{className:"mb-4",children:j.jsxs("label",{htmlFor:"car-images",className:"block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[j.jsx(Ht,{size:36,className:"mx-auto mb-2 text-gray-400"}),j.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Click to add more images"}),j.jsx("span",{className:"text-xs text-gray-500",children:"JPG, PNG or WEBP (max. 5MB each)"}),j.jsx("input",{id:"car-images",type:"file",multiple:!0,accept:"image/jpeg,image/png,image/webp",onChange:e=>{Array.from(e.target.files||[]).forEach((e=>{if(e.size>5242880)return void h("Each image must be less than 5MB");const s=new FileReader;s.onload=()=>{const t=s.result;P((e=>[...e,t])),_((s=>[...s,e]))},s.readAsDataURL(e)}))},className:"hidden"})]})}),0===A.length?j.jsx("div",{className:"text-gray-500 italic mb-4",children:"No images available. Please add at least one image."}):j.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:A.map(((e,s)=>j.jsxs("div",{className:"relative group",children:[j.jsx("img",{src:e,alt:`Car image ${s+1}`,className:"w-full h-40 object-cover rounded-md",onError:e=>{const s=e.target;s.style.display="none";const t=s.parentElement;if(t&&!t.querySelector(".image-fallback")){const e=document.createElement("div");e.className="image-fallback w-full h-40 bg-gray-200 flex items-center justify-center text-gray-500 text-sm rounded-md",e.textContent="Image Error",t.appendChild(e)}}}),j.jsx("button",{type:"button",onClick:()=>((e,s)=>{P(A.filter((s=>s!==e))),s<E.length&&_((e=>{const t=[...e];return t.splice(s,1),t})),e.startsWith("data:")&&URL.revokeObjectURL(e)})(e,s),className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600",children:j.jsx(Gt,{size:16})})]},s)))}),j.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[j.jsx(wt,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),j.jsx("span",{children:"Current images will be kept. New images will be added to your listing."})]})]})}),j.jsxs("div",{className:"flex justify-end space-x-4",children:[j.jsx(Jt,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),j.jsx(Jt,{type:"submit",disabled:m,children:m?"Updating Car...":"Update Car"})]})]})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(xt,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in as an owner to edit a car."}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("p",{className:"text-sm text-gray-500",children:["Test credentials: ",j.jsx("br",{}),j.jsx("strong",{children:"Email:"})," <EMAIL> ",j.jsx("br",{}),j.jsx("strong",{children:"Password:"})," password"]}),j.jsxs("div",{className:"flex gap-3 justify-center",children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"flex items-center",children:"Go to Login"})}),j.jsx(r,{to:"/owner/dashboard",children:j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Dashboard"]})})]})]})]})})})},Rr=()=>{const s=t(),{user:r}=$s(),[a,i]=e.useState(!1),[l,n]=e.useState(null),[c,o]=e.useState(!1),[d,m]=e.useState({carMake:"",carModel:"",carYear:(new Date).getFullYear(),carPlateNumber:"",ownerName:(null==r?void 0:r.name)||"",ownerPhone:"",ownerEmail:(null==r?void 0:r.email)||"",installationLocation:"",preferredDate:"",preferredTime:"",additionalNotes:""}),x=e=>{const{name:s,value:t}=e.target;m({...d,[s]:"carYear"===s?Number(t):t})};return c?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsx("div",{className:"max-w-2xl mx-auto",children:j.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-8 text-center",children:[j.jsx("div",{className:"w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4",children:j.jsx(At,{size:32,className:"text-success-600"})}),j.jsx("h2",{className:"text-2xl font-bold text-success-800 mb-4",children:"GPS Installation Request Submitted!"}),j.jsx("p",{className:"text-success-700 mb-6",children:"Thank you for your GPS installation request. Our team will contact you within 24-48 hours to schedule the installation and provide you with pricing details."}),j.jsxs("div",{className:"space-y-2 text-sm text-success-600 mb-6",children:[j.jsx("p",{children:j.jsx("strong",{children:"What's next?"})}),j.jsx("p",{children:"• Our technician will call you to confirm details"}),j.jsx("p",{children:"• We'll schedule a convenient installation time"}),j.jsx("p",{children:"• Professional GPS installation at your location"}),j.jsx("p",{children:"• Setup and training on the GPS tracking system"})]}),j.jsx(Jt,{onClick:()=>s("/owner/dashboard"),children:"Return to Dashboard"})]})})})}):j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-3xl mx-auto",children:[j.jsxs("div",{className:"flex items-center mb-6",children:[j.jsxs("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4 flex items-center",children:[j.jsx(ct,{size:20,className:"mr-1"}),"Back to Dashboard"]}),j.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"GPS Installation Request"})]}),j.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[j.jsxs("div",{className:"p-6 bg-primary-50 border-b border-primary-100",children:[j.jsx("h2",{className:"text-xl font-semibold text-primary-800 mb-2",children:"Professional GPS Tracking Installation"}),j.jsx("p",{className:"text-primary-700",children:"Enhance your car's security and tracking capabilities with our professional GPS installation service. Perfect for car owners who want to monitor their vehicle's location and usage."})]}),j.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),n(null),d.carMake&&d.carModel&&d.carPlateNumber&&d.ownerName&&d.ownerPhone&&d.installationLocation){i(!0);try{await new Promise((e=>setTimeout(e,2e3))),o(!0),setTimeout((()=>{s("/owner/dashboard")}),3e3)}catch(t){n("Failed to submit GPS installation request. Please try again.")}finally{i(!1)}}else n("Please fill in all required fields")},className:"p-6",children:[l&&j.jsx("div",{className:"mb-6 p-4 bg-error-50 border border-error-200 rounded-lg",children:j.jsx("p",{className:"text-error-700",children:l})}),j.jsxs("div",{className:"mb-8",children:[j.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[j.jsx(xt,{size:20,className:"mr-2 text-primary-600"}),"Vehicle Information"]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"carMake",className:"block text-sm font-medium text-gray-700 mb-1",children:["Car Make ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"carMake",name:"carMake",value:d.carMake,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Toyota",required:!0})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"carModel",className:"block text-sm font-medium text-gray-700 mb-1",children:["Car Model ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"carModel",name:"carModel",value:d.carModel,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Camry",required:!0})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"carYear",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("select",{id:"carYear",name:"carYear",value:d.carYear,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},((e,s)=>(new Date).getFullYear()-s)).map((e=>j.jsx("option",{value:e,children:e},e)))})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"carPlateNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["Plate Number ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"carPlateNumber",name:"carPlateNumber",value:d.carPlateNumber,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., RAD 123A",required:!0})]})]})]}),j.jsxs("div",{className:"mb-8",children:[j.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[j.jsx(Vt,{size:20,className:"mr-2 text-primary-600"}),"Contact Information"]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"ownerName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsx("input",{type:"text",id:"ownerName",name:"ownerName",value:d.ownerName,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),j.jsxs("div",{children:[j.jsxs("label",{htmlFor:"ownerPhone",className:"block text-sm font-medium text-gray-700 mb-1",children:["Phone Number ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(_t,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"tel",id:"ownerPhone",name:"ownerPhone",value:d.ownerPhone,onChange:x,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"**********",required:!0})]})]}),j.jsxs("div",{className:"md:col-span-2",children:[j.jsx("label",{htmlFor:"ownerEmail",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(Ct,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"email",id:"ownerEmail",name:"ownerEmail",value:d.ownerEmail,onChange:x,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"<EMAIL>"})]})]})]})]}),j.jsxs("div",{className:"mb-8",children:[j.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[j.jsx(mt,{size:20,className:"mr-2 text-primary-600"}),"Installation Details"]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[j.jsxs("div",{className:"md:col-span-2",children:[j.jsxs("label",{htmlFor:"installationLocation",className:"block text-sm font-medium text-gray-700 mb-1",children:["Installation Location ",j.jsx("span",{className:"text-error-600",children:"*"})]}),j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:j.jsx(At,{size:18,className:"text-gray-400"})}),j.jsx("input",{type:"text",id:"installationLocation",name:"installationLocation",value:d.installationLocation,onChange:x,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Address where installation should take place",required:!0})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"preferredDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Preferred Date"}),j.jsx("input",{type:"date",id:"preferredDate",name:"preferredDate",value:d.preferredDate,onChange:x,min:(new Date).toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"preferredTime",className:"block text-sm font-medium text-gray-700 mb-1",children:"Preferred Time"}),j.jsxs("select",{id:"preferredTime",name:"preferredTime",value:d.preferredTime,onChange:x,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[j.jsx("option",{value:"",children:"Select time"}),j.jsx("option",{value:"morning",children:"Morning (8:00 AM - 12:00 PM)"}),j.jsx("option",{value:"afternoon",children:"Afternoon (12:00 PM - 5:00 PM)"}),j.jsx("option",{value:"evening",children:"Evening (5:00 PM - 8:00 PM)"})]})]}),j.jsxs("div",{className:"md:col-span-2",children:[j.jsx("label",{htmlFor:"additionalNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Notes"}),j.jsx("textarea",{id:"additionalNotes",name:"additionalNotes",value:d.additionalNotes,onChange:x,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Any special requirements or additional information..."})]})]})]}),j.jsxs("div",{className:"mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[j.jsx("h4",{className:"font-semibold text-blue-800 mb-2",children:"GPS Installation Service Includes:"}),j.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[j.jsx("li",{children:"• Professional GPS device installation"}),j.jsx("li",{children:"• Real-time tracking setup"}),j.jsx("li",{children:"• Mobile app configuration"}),j.jsx("li",{children:"• Training on how to use the system"}),j.jsx("li",{children:"• 1-year warranty on installation"}),j.jsx("li",{children:"• 24/7 technical support"})]}),j.jsxs("p",{className:"text-sm text-blue-600 mt-3",children:[j.jsx("strong",{children:"Pricing:"})," Our team will provide a detailed quote based on your vehicle type and requirements."]})]}),j.jsxs("div",{className:"flex justify-end space-x-4",children:[j.jsx(Jt,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),j.jsx(Jt,{type:"submit",disabled:a,children:a?"Submitting Request...":"Submit GPS Installation Request"})]})]})]})]})})})},Tr=()=>{const s=t(),{user:a,updateUser:i,isAuthenticated:l}=$s(),[n,c]=e.useState(!1),[o,d]=e.useState(null),[m,x]=e.useState(!1),[u,h]=e.useState({name:(null==a?void 0:a.name)||"",email:(null==a?void 0:a.email)||"",phoneNumber:(null==a?void 0:a.phoneNumber)||""}),[p,g]=e.useState(null),[f,y]=e.useState((null==a?void 0:a.licenseImageUrl)||null),b=e=>{const{name:s,value:t}=e.target;h((e=>({...e,[s]:t})))};return l&&a?j.jsx(Xt,{children:j.jsx("div",{className:"container mx-auto px-4 py-8",children:j.jsxs("div",{className:"max-w-2xl mx-auto",children:[j.jsxs("div",{className:"flex items-center mb-6",children:[j.jsx(r,{to:"/account",className:"mr-4",children:j.jsxs(Jt,{variant:"secondary",size:"sm",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Account"]})}),j.jsxs("div",{children:[j.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Profile"}),j.jsx("p",{className:"text-gray-600",children:"Update your account information"})]})]}),m&&j.jsx(Qt,{className:"p-4 mb-6 bg-green-50 border-green-200",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"flex-shrink-0",children:j.jsx(Tt,{className:"h-5 w-5 text-green-400"})}),j.jsx("div",{className:"ml-3",children:j.jsx("p",{className:"text-sm font-medium text-green-800",children:"Profile updated successfully! Redirecting..."})})]})}),o&&j.jsx(Qt,{className:"p-4 mb-6 bg-red-50 border-red-200",children:j.jsxs("div",{className:"flex items-center",children:[j.jsx("div",{className:"flex-shrink-0",children:j.jsx(Gt,{className:"h-5 w-5 text-red-400"})}),j.jsx("div",{className:"ml-3",children:j.jsx("p",{className:"text-sm font-medium text-red-800",children:o})})]})}),j.jsx(Qt,{className:"p-6",children:j.jsxs("form",{onSubmit:async e=>{e.preventDefault(),d(null),c(!0);try{const e={name:u.name,email:u.email,phoneNumber:u.phoneNumber,licenseImageUrl:f};await i(e),x(!0),setTimeout((()=>{s("/account")}),2e3)}catch(t){d(t.message||"Failed to update profile")}finally{c(!1)}},className:"space-y-6",children:[j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Basic Information"}),j.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[j.jsxs("div",{children:[j.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),j.jsxs("div",{className:"relative",children:[j.jsx(Vt,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:18}),j.jsx("input",{type:"text",id:"name",name:"name",value:u.name,onChange:b,className:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),j.jsxs("div",{className:"relative",children:[j.jsx(Ct,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:18}),j.jsx("input",{type:"email",id:"email",name:"email",value:u.email,onChange:b,className:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),j.jsxs("div",{children:[j.jsx("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),j.jsxs("div",{className:"relative",children:[j.jsx(_t,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:18}),j.jsx("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:u.phoneNumber,onChange:b,className:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., +**********"})]})]})]})]}),j.jsxs("div",{children:[j.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Driver's License (Optional)"}),j.jsxs("div",{className:"space-y-4",children:[f?j.jsxs("div",{className:"relative",children:[j.jsx("img",{src:f,alt:"License preview",className:"w-full max-w-md h-48 object-cover rounded-lg border border-gray-300"}),j.jsx("button",{type:"button",onClick:()=>{g(null),y(null)},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600",children:j.jsx(Gt,{size:16})})]}):j.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[j.jsx(Ht,{className:"mx-auto h-12 w-12 text-gray-400"}),j.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Upload your driver's license"})]}),j.jsx("input",{type:"file",accept:"image/*",onChange:e=>{var s;const t=null==(s=e.target.files)?void 0:s[0];if(t){g(t);const e=new FileReader;e.onload=e=>{var s;y(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"})]})]}),j.jsxs("div",{className:"flex justify-end space-x-3",children:[j.jsx(r,{to:"/account",children:j.jsx(Jt,{variant:"secondary",disabled:n,children:"Cancel"})}),j.jsx(Jt,{type:"submit",disabled:n,children:n?j.jsxs(j.Fragment,{children:[j.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):j.jsxs(j.Fragment,{children:[j.jsx(Tt,{size:16,className:"mr-2"}),"Save Changes"]})})]})]})})]})})}):j.jsx(Xt,{children:j.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:j.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[j.jsx(Vt,{size:64,className:"mx-auto text-red-500 mb-4"}),j.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),j.jsx("p",{className:"text-gray-600 mb-6",children:"You need to be logged in to edit your profile."}),j.jsxs("div",{className:"flex gap-3 justify-center",children:[j.jsx(r,{to:"/login",children:j.jsx(Jt,{className:"flex items-center",children:"Go to Login"})}),j.jsx(r,{to:"/account",children:j.jsxs(Jt,{variant:"secondary",className:"flex items-center",children:[j.jsx(ct,{size:16,className:"mr-2"}),"Back to Account"]})})]})]})})})},Dr=({element:e,isAuthenticated:s,redirectPath:t="/login"})=>s?j.jsx(j.Fragment,{children:e}):j.jsx(n,{to:t,replace:!0});function Lr(){return e.useEffect((()=>{document.title="Park & Rent - Peer-to-Peer Car Rentals"}),[]),j.jsx(Vs,{children:j.jsx(Gs,{children:j.jsx(Xs,{children:j.jsx(st,{children:j.jsx(rt,{children:j.jsxs(i,{children:[j.jsx(l,{path:"/",element:j.jsx(ir,{})}),j.jsx(l,{path:"/cars",element:j.jsx(cr,{})}),j.jsx(l,{path:"/cars/:id",element:j.jsx(ur,{})}),j.jsx(l,{path:"/drivers",element:j.jsx(pr,{})}),j.jsx(l,{path:"/drivers/:id",element:j.jsx(gr,{})}),j.jsx(l,{path:"/login",element:j.jsx(wr,{})}),j.jsx(l,{path:"/signup",element:j.jsx(kr,{})}),j.jsx(l,{path:"/account/verification",element:j.jsx(Dr,{element:j.jsx(Cr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner",element:j.jsx(Dr,{element:j.jsx(Ar,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/dashboard",element:j.jsx(Dr,{element:j.jsx(Ar,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/add-car",element:j.jsx(Dr,{element:j.jsx(_r,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/edit-car/:id",element:j.jsx(Dr,{element:j.jsx(zr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/gps-request",element:j.jsx(Dr,{element:j.jsx(Rr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/messages",element:j.jsx(Dr,{element:j.jsx(Pr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/owner/bookings",element:j.jsx(Dr,{element:j.jsx(Er,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/driver/register",element:j.jsx(Dr,{element:j.jsx(fr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/driver",element:j.jsx(Dr,{element:j.jsx(yr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/driver/dashboard",element:j.jsx(Dr,{element:j.jsx(yr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/account",element:j.jsx(Dr,{element:j.jsx(jr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/account/edit-profile",element:j.jsx(Dr,{element:j.jsx(Tr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/admin",element:j.jsx(Dr,{element:j.jsx(Nr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"/admin/dashboard",element:j.jsx(Dr,{element:j.jsx(Nr,{}),isAuthenticated:!0})}),j.jsx(l,{path:"*",element:j.jsx(n,{to:"/",replace:!0})})]})})})})})})}y(document.getElementById("root")).render(j.jsx(e.StrictMode,{children:j.jsx(c,{children:j.jsx(Lr,{})})}));
