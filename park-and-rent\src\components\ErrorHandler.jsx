import React from 'react';

// Component to display detailed error messages with suggestions
export const ErrorDisplay = ({ error, suggestions = [] }) => {
  if (!error) return null;
  
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div className="text-red-800 font-semibold mb-2">
        {error}
      </div>
      
      {suggestions.length > 0 && (
        <div className="text-red-700 text-sm">
          <div className="font-medium mb-1">Suggestions:</div>
          <ul className="list-disc list-inside space-y-1">
            {suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Component to display success messages
export const SuccessDisplay = ({ message }) => {
  if (!message) return null;
  
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <div className="text-green-800 font-semibold">
        {message}
      </div>
    </div>
  );
};

// Hook to handle API responses with detailed error handling
export const useApiResponse = () => {
  const [error, setError] = React.useState('');
  const [success, setSuccess] = React.useState('');
  const [suggestions, setSuggestions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  
  const handleResponse = async (response) => {
    const data = await response.json();
    
    if (response.ok) {
      // Success
      setError('');
      setSuggestions([]);
      setSuccess(data.message || 'Operation successful!');
      return { success: true, data };
    } else {
      // Error
      setSuccess('');
      setError(data.message || 'An error occurred');
      setSuggestions(data.suggestions || []);
      return { success: false, data };
    }
  };
  
  const clearMessages = () => {
    setError('');
    setSuccess('');
    setSuggestions([]);
  };
  
  return {
    error,
    success,
    suggestions,
    loading,
    setLoading,
    handleResponse,
    clearMessages,
    setError,
    setSuccess
  };
};
