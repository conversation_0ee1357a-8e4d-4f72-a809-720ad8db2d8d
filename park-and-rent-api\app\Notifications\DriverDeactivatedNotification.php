<?php

namespace App\Notifications;

use App\Models\Driver;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DriverDeactivatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $driver;
    public $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(Driver $driver, ?string $reason = null)
    {
        $this->driver = $driver;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject('Driver Profile Deactivated - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We are writing to inform you that your driver profile has been deactivated by our administration team.')
            ->line('**Driver Profile Details:**')
            ->line('• Driver Name: ' . $this->driver->name)
            ->line('• Driver ID: #' . $this->driver->id)
            ->line('• License Number: ' . $this->driver->license_number)
            ->line('• Location: ' . $this->driver->location)
            ->line('• Experience: ' . $this->driver->experience . ' years')
            ->line('• Deactivation Date: ' . now()->format('M j, Y \a\t g:i A'));

        if ($this->reason) {
            $mailMessage->line('• Reason: ' . $this->reason);
        }

        $mailMessage->line('**What this means:**')
            ->line('• Your driver profile is no longer visible to potential clients')
            ->line('• No new driving service bookings can be made')
            ->line('• Existing confirmed bookings will continue as scheduled')
            ->line('• You can still access your profile details in your dashboard')
            ->line('• Your account remains active for other services')
            ->line('**Next Steps:**')
            ->line('If you believe this deactivation was made in error, or if you would like to understand the reason for deactivation, please contact our support team immediately.')
            ->line('Our team will review your case and provide assistance to help you get your driver profile reactivated if appropriate.')
            ->line('**Possible reasons for deactivation:**')
            ->line('• License verification issues')
            ->line('• Customer complaints or safety concerns')
            ->line('• Violation of platform terms and conditions')
            ->line('• Incomplete or inaccurate profile information')
            ->action('Contact Support', 'mailto:<EMAIL>')
            ->line('**Contact Information:**')
            ->line('• Email: <EMAIL>')
            ->line('• Phone: **********')
            ->line('• Support Hours: Monday - Friday, 8:00 AM - 6:00 PM')
            ->line('We appreciate your understanding and look forward to resolving this matter quickly.')
            ->line('Thank you for being part of the Park & Rent community!');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'driver_id' => $this->driver->id,
            'type' => 'driver_deactivated',
            'title' => 'Driver Profile Deactivated',
            'message' => 'Your driver profile (' . $this->driver->name . ') has been deactivated by administration.',
            'reason' => $this->reason,
            'action_url' => '/driver/profile',
        ];
    }
}
