import React, { useState, useEffect } from 'react';
import { testApiConnection, checkApiHealth, API_BASE_URL } from '../config/api';
import { carService } from '../services/carService';

const ApiDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    const results: any = {};

    try {
      // Test 1: API Base URL
      results.baseUrl = API_BASE_URL;

      // Test 2: Health Check
      try {
        const healthResult = await checkApiHealth();
        results.healthCheck = healthResult;
      } catch (error) {
        results.healthCheck = { success: false, error: error.message };
      }

      // Test 3: Connection Test
      try {
        const connectionResult = await testApiConnection();
        results.connectionTest = connectionResult;
      } catch (error) {
        results.connectionTest = { success: false, error: error.message };
      }

      // Test 4: Cars API
      try {
        const carsResult = await carService.getCars();
        results.carsApi = { 
          success: true, 
          count: carsResult.data?.length || 0,
          pagination: carsResult.pagination 
        };
      } catch (error) {
        results.carsApi = { success: false, error: error.message };
      }

      // Test 5: Network Info
      results.networkInfo = {
        userAgent: navigator.userAgent,
        online: navigator.onLine,
        connection: (navigator as any).connection?.effectiveType || 'unknown'
      };

    } catch (error) {
      results.generalError = error.message;
    }

    setDebugInfo(results);
    setIsLoading(false);
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white shadow-lg rounded-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">API Debug Information</h2>
        <button
          onClick={runTests}
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {isLoading ? 'Testing...' : 'Run Tests'}
        </button>
      </div>

      <div className="space-y-6">
        {/* API Base URL */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">API Configuration</h3>
          <p className="text-sm text-gray-700">
            <strong>Base URL:</strong> {debugInfo.baseUrl}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Environment:</strong> {import.meta.env.MODE}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Production:</strong> {import.meta.env.PROD ? 'Yes' : 'No'}
          </p>
        </div>

        {/* Health Check */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Health Check</h3>
          <div className={`p-3 rounded ${debugInfo.healthCheck?.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {debugInfo.healthCheck?.success ? (
              <div>
                <p>✅ Health check passed</p>
                <pre className="text-xs mt-2">{JSON.stringify(debugInfo.healthCheck.data, null, 2)}</pre>
              </div>
            ) : (
              <div>
                <p>❌ Health check failed</p>
                <p className="text-xs mt-2">{debugInfo.healthCheck?.error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Connection Test */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Connection Test</h3>
          <div className={`p-3 rounded ${debugInfo.connectionTest?.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {debugInfo.connectionTest?.success ? (
              <div>
                <p>✅ Connection test passed</p>
                <p className="text-xs">Endpoint: {debugInfo.connectionTest.endpoint}</p>
                {debugInfo.connectionTest.method && (
                  <p className="text-xs">Method: {debugInfo.connectionTest.method}</p>
                )}
              </div>
            ) : (
              <div>
                <p>❌ Connection test failed</p>
                <p className="text-xs mt-2">{debugInfo.connectionTest?.error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Cars API Test */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Cars API Test</h3>
          <div className={`p-3 rounded ${debugInfo.carsApi?.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {debugInfo.carsApi?.success ? (
              <div>
                <p>✅ Cars API working</p>
                <p className="text-xs">Cars found: {debugInfo.carsApi.count}</p>
                {debugInfo.carsApi.pagination && (
                  <pre className="text-xs mt-2">{JSON.stringify(debugInfo.carsApi.pagination, null, 2)}</pre>
                )}
              </div>
            ) : (
              <div>
                <p>❌ Cars API failed</p>
                <p className="text-xs mt-2">{debugInfo.carsApi?.error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Network Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Network Information</h3>
          <div className="text-sm text-gray-700">
            <p><strong>Online:</strong> {debugInfo.networkInfo?.online ? 'Yes' : 'No'}</p>
            <p><strong>Connection:</strong> {debugInfo.networkInfo?.connection}</p>
            <p><strong>User Agent:</strong> {debugInfo.networkInfo?.userAgent}</p>
          </div>
        </div>

        {/* Raw Debug Data */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Raw Debug Data</h3>
          <pre className="text-xs text-gray-700 overflow-auto max-h-64 bg-white p-3 rounded border">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ApiDebug;
