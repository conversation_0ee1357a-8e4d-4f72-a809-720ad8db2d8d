@extends('layouts.app')

@section('title', 'Find Drivers')

@section('content')
<div class="bg-white">
    <!-- Header -->
    <div class="bg-primary-600">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold text-white sm:text-5xl">
                    Find Professional Drivers
                </h1>
                <p class="mt-4 text-xl text-primary-100">
                    Hire experienced and verified drivers for your trips
                </p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form method="GET" action="{{ route('drivers.index') }}" class="bg-gray-50 p-6 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="Driver name or location..." 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" name="location" id="location" value="{{ request('location') }}" 
                           placeholder="Enter location..." 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="min_experience" class="block text-sm font-medium text-gray-700">Min Experience (Years)</label>
                    <input type="number" name="min_experience" id="min_experience" value="{{ request('min_experience') }}" 
                           placeholder="0" min="0" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                </div>
                <div>
                    <label for="specialty" class="block text-sm font-medium text-gray-700">Specialty</label>
                    <select name="specialty" id="specialty" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        <option value="">All Specialties</option>
                        <option value="City Driving" {{ request('specialty') == 'City Driving' ? 'selected' : '' }}>City Driving</option>
                        <option value="Airport Transfers" {{ request('specialty') == 'Airport Transfers' ? 'selected' : '' }}>Airport Transfers</option>
                        <option value="Long Distance" {{ request('specialty') == 'Long Distance' ? 'selected' : '' }}>Long Distance</option>
                        <option value="Tourism" {{ request('specialty') == 'Tourism' ? 'selected' : '' }}>Tourism</option>
                        <option value="Business Trips" {{ request('specialty') == 'Business Trips' ? 'selected' : '' }}>Business Trips</option>
                        <option value="Events" {{ request('specialty') == 'Events' ? 'selected' : '' }}>Events</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex justify-between items-center">
                <div>
                    <label for="sort_by" class="block text-sm font-medium text-gray-700">Sort by</label>
                    <select name="sort_by" id="sort_by" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        <option value="rating" {{ request('sort_by') == 'rating' ? 'selected' : '' }}>Highest Rated</option>
                        <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="experience" {{ request('sort_by') == 'experience' ? 'selected' : '' }}>Most Experienced</option>
                        <option value="reviews" {{ request('sort_by') == 'reviews' ? 'selected' : '' }}>Most Reviews</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <a href="{{ route('drivers.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
                        Clear
                    </a>
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900">
                {{ $drivers->total() }} {{ Str::plural('driver', $drivers->total()) }} found
            </h2>
        </div>

        @if($drivers->count() > 0)
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                @foreach($drivers as $driver)
                <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                    <div class="h-48 bg-gray-200">
                        @if($driver->profile_image)
                            <img class="h-full w-full object-cover" src="{{ asset('storage/' . $driver->profile_image) }}" alt="{{ $driver->name }}">
                        @else
                            <div class="h-full w-full flex items-center justify-center bg-gray-300">
                                <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ $driver->name }}</h3>
                            <span class="text-sm text-gray-500">{{ $driver->age }} years old</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-600 flex items-center">
                            <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ $driver->location }}
                        </p>
                        <p class="mt-1 text-sm text-gray-600">{{ $driver->experience }} years experience</p>
                        
                        <!-- Rating -->
                        <div class="mt-2 flex items-center">
                            <div class="flex items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= floor($driver->rating))
                                        <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @else
                                        <svg class="h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endif
                                @endfor
                                <span class="ml-2 text-sm text-gray-600">{{ number_format($driver->rating, 1) }} ({{ $driver->reviews }} reviews)</span>
                            </div>
                        </div>

                        <!-- Specialties -->
                        @if($driver->specialties && count($driver->specialties) > 0)
                        <div class="mt-3 flex flex-wrap gap-1">
                            @foreach(array_slice($driver->specialties, 0, 3) as $specialty)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                {{ $specialty }}
                            </span>
                            @endforeach
                            @if(count($driver->specialties) > 3)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                +{{ count($driver->specialties) - 3 }} more
                            </span>
                            @endif
                        </div>
                        @endif

                        <div class="mt-4 flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-primary-600">${{ $driver->price_per_hour }}</span>
                                <span class="text-sm text-gray-500">/hour</span>
                            </div>
                            <a href="{{ route('drivers.show', $driver) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                View Profile
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $drivers->withQueryString()->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No drivers found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
                <div class="mt-6">
                    <a href="{{ route('drivers.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        View All Drivers
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
