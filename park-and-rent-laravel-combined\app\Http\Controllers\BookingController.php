<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Notifications\BookingConfirmationNotification;
use App\Notifications\NewBookingRequestNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class BookingController extends Controller
{
    /**
     * Display a listing of the user's bookings.
     */
    public function myBookings()
    {
        $bookings = Booking::where('user_id', auth()->id())
            ->with(['item'])
            ->latest()
            ->paginate(10);

        return view('bookings.my-bookings', compact('bookings'));
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $bookings = Booking::where('user_id', auth()->id())
            ->with(['item'])
            ->latest()
            ->paginate(10);

        return view('bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:start_date',
            'car_id' => 'nullable|exists:cars,id',
            'driver_id' => 'nullable|exists:drivers,id',
            'message' => 'nullable|string|max:1000',
            'pickup_location' => 'nullable|string|max:255',
            'destination' => 'nullable|string|max:255',
            'service_type' => 'nullable|string',
            'start_time' => 'nullable',
            'duration' => 'nullable|integer|min:1',
        ]);

        // Determine booking type and calculate total
        $bookingData = [
            'user_id' => auth()->id(),
            'start_date' => $request->start_date,
            'end_date' => $request->end_date ?? $request->start_date,
            'message' => $request->message,
            'status' => 'pending',
            'pickup_location' => $request->pickup_location,
            'destination' => $request->destination,
        ];

        if ($request->car_id) {
            // Car booking
            $car = \App\Models\Car::findOrFail($request->car_id);
            $days = \Carbon\Carbon::parse($request->start_date)->diffInDays(\Carbon\Carbon::parse($request->end_date)) + 1;

            $bookingData['bookable_type'] = 'App\\Models\\Car';
            $bookingData['bookable_id'] = $car->id;
            $bookingData['total_amount'] = $car->price_per_day * $days;
        } elseif ($request->driver_id) {
            // Driver booking
            $driver = \App\Models\Driver::findOrFail($request->driver_id);
            $hours = $request->duration ?? 1;

            $bookingData['bookable_type'] = 'App\\Models\\Driver';
            $bookingData['bookable_id'] = $driver->id;
            $bookingData['total_amount'] = $driver->price_per_hour * $hours;
            $bookingData['service_type'] = $request->service_type;
            $bookingData['start_time'] = $request->start_time;
            $bookingData['duration'] = $hours;
        }

        $booking = Booking::create($bookingData);

        // Load the booking with relationships for notifications
        $booking->load(['bookable', 'user']);

        // Send notification to customer
        $booking->user->notify(new BookingConfirmationNotification($booking));

        // Send notification to owner/driver
        if ($booking->bookable_type === 'App\\Models\\Car') {
            $owner = $booking->bookable->owner;
            if ($owner) {
                $owner->notify(new NewBookingRequestNotification($booking));
            }
        } elseif ($booking->bookable_type === 'App\\Models\\Driver') {
            $driver = $booking->bookable->user;
            if ($driver) {
                $driver->notify(new NewBookingRequestNotification($booking));
            }
        }

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking request sent successfully! The owner will contact you soon.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        // Ensure user can only view their own bookings
        if ($booking->user_id !== auth()->id()) {
            abort(403);
        }

        $booking->load(['item', 'user']);

        return view('bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        //
    }
}
