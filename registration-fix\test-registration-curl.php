<?php

echo "=== TESTING REGISTRATION WITH CURL ===\n\n";

// Test data scenarios
$testCases = [
    [
        'name' => 'Valid Registration',
        'data' => [
            'name' => 'Test User Valid',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Missing Password Confirmation',
        'data' => [
            'name' => 'Test User No Confirm',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Short Password',
        'data' => [
            'name' => 'Test User Short',
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Missing Name',
        'data' => [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ],
    [
        'name' => 'Invalid Email',
        'data' => [
            'name' => 'Test User Invalid Email',
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'client'
        ]
    ]
];

foreach ($testCases as $index => $testCase) {
    echo "🧪 TEST " . ($index + 1) . ": {$testCase['name']}\n";
    echo "Data: " . json_encode($testCase['data'], JSON_PRETTY_PRINT) . "\n";
    
    // Create curl command
    $jsonData = json_encode($testCase['data']);
    $curlCommand = "curl -X POST https://ebisera.com/api/public/api/register " .
                   "-H 'Content-Type: application/json' " .
                   "-H 'Accept: application/json' " .
                   "-d '" . addslashes($jsonData) . "' " .
                   "-w '\\nHTTP Status: %{http_code}\\n' " .
                   "-s";
    
    echo "CURL Command:\n{$curlCommand}\n\n";
    
    // Execute curl command
    echo "Response:\n";
    $response = shell_exec($curlCommand);
    echo $response . "\n";
    echo str_repeat("-", 50) . "\n\n";
}

echo "📋 MANUAL TESTING COMMANDS:\n\n";

echo "1. Test valid registration:\n";
echo "curl -X POST https://ebisera.com/api/public/api/register \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Accept: application/json' \\\n";
echo "  -d '{\n";
echo "    \"name\": \"Manual Test User\",\n";
echo "    \"email\": \"<EMAIL>\",\n";
echo "    \"password\": \"password123\",\n";
echo "    \"password_confirmation\": \"password123\",\n";
echo "    \"role\": \"client\"\n";
echo "  }' \\\n";
echo "  -v\n\n";

echo "2. Test with missing field:\n";
echo "curl -X POST https://ebisera.com/api/public/api/register \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Accept: application/json' \\\n";
echo "  -d '{\n";
echo "    \"name\": \"Missing Confirmation\",\n";
echo "    \"email\": \"<EMAIL>\",\n";
echo "    \"password\": \"password123\",\n";
echo "    \"role\": \"client\"\n";
echo "  }' \\\n";
echo "  -v\n\n";

echo "3. Check registration route exists:\n";
echo "curl -X OPTIONS https://ebisera.com/api/public/api/register -v\n\n";

echo "4. Check what methods are allowed:\n";
echo "curl -X GET https://ebisera.com/api/public/api/register -v\n\n";

echo "📝 NOTES:\n";
echo "- Look for HTTP status codes in responses\n";
echo "- 422 = Validation error (check response body for details)\n";
echo "- 404 = Route not found\n";
echo "- 405 = Method not allowed\n";
echo "- 500 = Server error\n";
echo "- 201 = Success (user created)\n\n";

echo "🔍 DEBUGGING TIPS:\n";
echo "1. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "2. Check web server logs\n";
echo "3. Verify route exists: php artisan route:list | grep register\n";
echo "4. Test with Postman or similar tool\n";
echo "5. Check browser Network tab for exact request/response\n";
