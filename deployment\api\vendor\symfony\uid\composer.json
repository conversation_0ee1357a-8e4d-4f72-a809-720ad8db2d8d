{"name": "symfony/uid", "type": "library", "description": "Provides an object-oriented API to generate and represent UIDs", "keywords": ["uid", "uuid", "ulid"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}