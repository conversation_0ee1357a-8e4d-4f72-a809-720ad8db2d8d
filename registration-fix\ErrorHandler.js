// ErrorHandler.js - Frontend component to handle API errors gracefully

export const handleApiError = (error, setError) => {
  console.log('API Error:', error);
  
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 422:
        // Validation errors
        handleValidationErrors(data, setError);
        break;
        
      case 401:
        // Authentication errors
        handleAuthenticationErrors(data, setError);
        break;
        
      case 500:
        // Server errors
        handleServerErrors(data, setError);
        break;
        
      default:
        setError('An unexpected error occurred. Please try again.');
    }
  } else if (error.request) {
    // Network error
    setError('Unable to connect to the server. Please check your internet connection.');
  } else {
    // Other error
    setError('An unexpected error occurred. Please try again.');
  }
};

const handleValidationErrors = (data, setError) => {
  if (data.message) {
    // Use the main message from the API
    setError(data.message);
  } else if (data.errors) {
    // Extract specific field errors
    const errorMessages = [];
    
    Object.keys(data.errors).forEach(field => {
      const fieldError = data.errors[field];
      errorMessages.push(fieldError);
    });
    
    setError(errorMessages.join(' '));
  } else {
    setError('Please check your information and try again.');
  }
};

const handleAuthenticationErrors = (data, setError) => {
  if (data.message) {
    setError(data.message);
  } else {
    setError('Invalid email or password. Please try again.');
  }
};

const handleServerErrors = (data, setError) => {
  if (data.message) {
    setError(data.message);
  } else {
    setError('Server error. Please try again later.');
  }
};

// Component to display errors with suggestions
export const ErrorDisplay = ({ error, suggestions = [] }) => {
  if (!error) return null;
  
  return (
    <div className="error-container" style={{
      backgroundColor: '#fee2e2',
      border: '1px solid #fecaca',
      borderRadius: '8px',
      padding: '16px',
      marginBottom: '16px'
    }}>
      <div className="error-message" style={{
        color: '#dc2626',
        fontWeight: '600',
        marginBottom: suggestions.length > 0 ? '8px' : '0'
      }}>
        {error}
      </div>
      
      {suggestions.length > 0 && (
        <div className="error-suggestions">
          <div style={{ color: '#7f1d1d', fontSize: '14px', marginBottom: '4px' }}>
            Suggestions:
          </div>
          <ul style={{ color: '#7f1d1d', fontSize: '14px', margin: '0', paddingLeft: '20px' }}>
            {suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Success message component
export const SuccessDisplay = ({ message }) => {
  if (!message) return null;
  
  return (
    <div className="success-container" style={{
      backgroundColor: '#dcfce7',
      border: '1px solid #bbf7d0',
      borderRadius: '8px',
      padding: '16px',
      marginBottom: '16px',
      color: '#166534',
      fontWeight: '600'
    }}>
      {message}
    </div>
  );
};

// Example usage in registration form
export const useRegistration = () => {
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const register = async (userData) => {
    setLoading(true);
    setError('');
    setSuccess('');
    setSuggestions([]);
    
    try {
      const response = await fetch('/api/public/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(userData)
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Success
        setSuccess(data.message || 'Registration successful!');
        return { success: true, data };
      } else {
        // Error
        setError(data.message || 'Registration failed');
        setSuggestions(data.suggestions || []);
        return { success: false, data };
      }
    } catch (error) {
      setError('Unable to connect to the server. Please try again.');
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };
  
  return {
    register,
    error,
    success,
    suggestions,
    loading,
    setError,
    setSuccess
  };
};
