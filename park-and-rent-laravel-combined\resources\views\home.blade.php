@extends('layouts.app')

@section('title', 'Home')

@section('content')
<!-- Hero Section -->
<div class="relative bg-gradient-to-r from-primary-600 to-primary-800 overflow-hidden">
    <div class="max-w-7xl mx-auto">
        <div class="relative z-10 pb-8 bg-gradient-to-r from-primary-600 to-primary-800 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main class="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
                <div class="sm:text-center lg:text-left">
                    <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                        <span class="block xl:inline">Rent Cars &</span>
                        <span class="block text-primary-200 xl:inline">Hire Drivers</span>
                    </h1>
                    <p class="mt-3 text-base text-primary-100 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                        Your trusted peer-to-peer platform for car rentals and professional driver services. Safe, reliable, and affordable.
                    </p>
                    <div class="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                        <div class="rounded-md shadow">
                            <a href="{{ route('cars.index') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10">
                                Browse Cars
                            </a>
                        </div>
                        <div class="mt-3 sm:mt-0 sm:ml-3">
                            <a href="{{ route('drivers.index') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-500 hover:bg-primary-400 md:py-4 md:text-lg md:px-10">
                                Find Drivers
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <div class="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <img class="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full" src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Car rental">
    </div>
</div>

<!-- Features Section -->
<div class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Features</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Why Choose Park & Rent?
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                We provide a secure and reliable platform for all your transportation needs.
            </p>
        </div>

        <div class="mt-10">
            <div class="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
                <div class="relative">
                    <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Secure & Safe</p>
                    <p class="mt-2 ml-16 text-base text-gray-500">
                        All users are verified and vehicles are inspected for your safety and peace of mind.
                    </p>
                </div>

                <div class="relative">
                    <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Fast & Easy</p>
                    <p class="mt-2 ml-16 text-base text-gray-500">
                        Book cars and drivers in minutes with our streamlined booking process.
                    </p>
                </div>

                <div class="relative">
                    <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Affordable Rates</p>
                    <p class="mt-2 ml-16 text-base text-gray-500">
                        Competitive pricing with transparent fees and no hidden charges.
                    </p>
                </div>

                <div class="relative">
                    <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <p class="ml-16 text-lg leading-6 font-medium text-gray-900">24/7 Support</p>
                    <p class="mt-2 ml-16 text-base text-gray-500">
                        Round-the-clock customer support to assist you whenever you need help.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Cars Section -->
@if($featuredCars->count() > 0)
<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Featured</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Popular Cars
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                Check out some of our most popular rental cars.
            </p>
        </div>

        <div class="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($featuredCars as $car)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="h-48 bg-gray-200">
                    @if($car->images && count($car->images) > 0)
                        <img class="h-full w-full object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                    @else
                        <div class="h-full w-full flex items-center justify-center bg-gray-300">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900">{{ $car->make }} {{ $car->model }}</h3>
                    <p class="text-sm text-gray-500">{{ $car->year }}</p>
                    <p class="mt-2 text-sm text-gray-600">{{ Str::limit($car->description, 100) }}</p>
                    <div class="mt-4 flex items-center justify-between">
                        <span class="text-lg font-bold text-primary-600">${{ $car->price_per_hour }}/hour</span>
                        <a href="{{ route('cars.show', $car) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="mt-8 text-center">
            <a href="{{ route('cars.index') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-base font-medium">
                View All Cars
            </a>
        </div>
    </div>
</div>
@endif

<!-- Featured Drivers Section -->
@if($featuredDrivers->count() > 0)
<div class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Featured</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Top Drivers
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                Meet our experienced and professional drivers.
            </p>
        </div>

        <div class="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($featuredDrivers as $driver)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="h-48 bg-gray-200">
                    @if($driver->profile_image)
                        <img class="h-full w-full object-cover" src="{{ asset('storage/' . $driver->profile_image) }}" alt="{{ $driver->name }}">
                    @else
                        <div class="h-full w-full flex items-center justify-center bg-gray-300">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900">{{ $driver->name }}</h3>
                    <p class="text-sm text-gray-500">{{ $driver->experience }} years experience</p>
                    <div class="mt-2 flex items-center">
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= floor($driver->rating))
                                    <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @else
                                    <svg class="h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @endif
                            @endfor
                            <span class="ml-2 text-sm text-gray-600">({{ $driver->reviews }} reviews)</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center justify-between">
                        <span class="text-lg font-bold text-primary-600">${{ $driver->price_per_hour }}/hour</span>
                        <a href="{{ route('drivers.show', $driver) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            View Profile
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="mt-8 text-center">
            <a href="{{ route('drivers.index') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-base font-medium">
                View All Drivers
            </a>
        </div>
    </div>
</div>
@endif

<!-- CTA Section -->
<div class="bg-primary-600">
    <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
            <span class="block">Ready to get started?</span>
            <span class="block">Join Park & Rent today.</span>
        </h2>
        <p class="mt-4 text-lg leading-6 text-primary-200">
            Whether you want to rent a car, hire a driver, or list your vehicle, we've got you covered.
        </p>
        <div class="mt-8 flex justify-center space-x-4">
            @guest
                <a href="{{ route('register') }}" class="bg-white text-primary-600 hover:bg-gray-50 px-6 py-3 rounded-md text-base font-medium">
                    Sign Up Now
                </a>
                <a href="{{ route('login') }}" class="border border-white text-white hover:bg-primary-500 px-6 py-3 rounded-md text-base font-medium">
                    Login
                </a>
            @else
                <a href="{{ route('cars.index') }}" class="bg-white text-primary-600 hover:bg-gray-50 px-6 py-3 rounded-md text-base font-medium">
                    Browse Cars
                </a>
                <a href="{{ route('drivers.index') }}" class="border border-white text-white hover:bg-primary-500 px-6 py-3 rounded-md text-base font-medium">
                    Find Drivers
                </a>
            @endguest
        </div>
    </div>
</div>
@endsection
