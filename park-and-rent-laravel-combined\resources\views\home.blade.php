@extends('layouts.app')

@section('title', 'Home')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-black via-transparent to-transparent opacity-50"></div>

    <!-- Background Image -->
    <div class="absolute inset-0">
        <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
             alt="Car rental background"
             class="w-full h-full object-cover opacity-30">
    </div>

    <div class="container mx-auto px-4 py-16 md:py-24 relative z-10">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                Rent Cars Directly From Local Owners
            </h1>
            <p class="text-xl mb-8 text-gray-100">
                Find affordable rentals from car owners in your area.
                No middlemen, no hidden fees — just direct peer-to-peer car rentals.
            </p>
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <a href="{{ route('cars.index') }}" class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200 w-full sm:w-auto">
                    Browse Available Cars
                </a>
                <a href="{{ route('register') }}" class="inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white bg-white bg-opacity-10 hover:bg-opacity-20 transition-all duration-200 w-full sm:w-auto">
                    List Your Car
                </a>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Getting started with Park & Rent is simple. Follow these easy steps to rent a car or list your vehicle.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- For Renters -->
            <div class="text-center p-6 rounded-lg">
                <svg class="w-12 h-12 text-primary-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <h3 class="text-xl font-semibold mb-3">Find a Car</h3>
                <p class="text-gray-600">
                    Browse our selection of available cars in your area.
                    Filter by location, price, and features to find your perfect match.
                </p>
            </div>

            <!-- Contact -->
            <div class="text-center p-6 rounded-lg">
                <svg class="w-12 h-12 text-primary-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="text-xl font-semibold mb-3">Connect Directly</h3>
                <p class="text-gray-600">
                    Once verified, contact car owners directly through our platform.
                    Arrange pickup times and locations that work for both of you.
                </p>
            </div>

            <!-- For Owners -->
            <div class="text-center p-6 rounded-lg">
                <svg class="w-12 h-12 text-primary-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <h3 class="text-xl font-semibold mb-3">Earn Money</h3>
                <p class="text-gray-600">
                    List your car when you're not using it and earn extra income.
                    Set your own prices and availability to maximize your earnings.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Cars Section -->
@if($featuredCars->count() > 0)
<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Featured</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Popular Cars
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                Check out some of our most popular rental cars.
            </p>
        </div>

        <div class="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($featuredCars as $car)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="h-48 bg-gray-200">
                    @if($car->images && count($car->images) > 0)
                        <img class="h-full w-full object-cover" src="{{ asset('storage/' . $car->images[0]) }}" alt="{{ $car->make }} {{ $car->model }}">
                    @else
                        <div class="h-full w-full flex items-center justify-center bg-gray-300">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900">{{ $car->make }} {{ $car->model }}</h3>
                    <p class="text-sm text-gray-500">{{ $car->year }}</p>
                    <p class="mt-2 text-sm text-gray-600">{{ Str::limit($car->description, 100) }}</p>
                    <div class="mt-4 flex items-center justify-between">
                        <span class="text-lg font-bold text-primary-600">${{ $car->price_per_hour }}/hour</span>
                        <a href="{{ route('cars.show', $car) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="mt-8 text-center">
            <a href="{{ route('cars.index') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-base font-medium">
                View All Cars
            </a>
        </div>
    </div>
</div>
@endif

<!-- Featured Drivers Section -->
@if($featuredDrivers->count() > 0)
<div class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
            <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Featured</h2>
            <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                Top Drivers
            </p>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                Meet our experienced and professional drivers.
            </p>
        </div>

        <div class="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($featuredDrivers as $driver)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="h-48 bg-gray-200">
                    @if($driver->profile_image)
                        <img class="h-full w-full object-cover" src="{{ asset('storage/' . $driver->profile_image) }}" alt="{{ $driver->name }}">
                    @else
                        <div class="h-full w-full flex items-center justify-center bg-gray-300">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900">{{ $driver->name }}</h3>
                    <p class="text-sm text-gray-500">{{ $driver->experience }} years experience</p>
                    <div class="mt-2 flex items-center">
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= floor($driver->rating))
                                    <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @else
                                    <svg class="h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @endif
                            @endfor
                            <span class="ml-2 text-sm text-gray-600">({{ $driver->reviews }} reviews)</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center justify-between">
                        <span class="text-lg font-bold text-primary-600">${{ $driver->price_per_hour }}/hour</span>
                        <a href="{{ route('drivers.show', $driver) }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            View Profile
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="mt-8 text-center">
            <a href="{{ route('drivers.index') }}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-base font-medium">
                View All Drivers
            </a>
        </div>
    </div>
</div>
@endif

<!-- CTA Section -->
<div class="bg-primary-600">
    <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
            <span class="block">Ready to get started?</span>
            <span class="block">Join Park & Rent today.</span>
        </h2>
        <p class="mt-4 text-lg leading-6 text-primary-200">
            Whether you want to rent a car, hire a driver, or list your vehicle, we've got you covered.
        </p>
        <div class="mt-8 flex justify-center space-x-4">
            @guest
                <a href="{{ route('register') }}" class="bg-white text-primary-600 hover:bg-gray-50 px-6 py-3 rounded-md text-base font-medium">
                    Sign Up Now
                </a>
                <a href="{{ route('login') }}" class="border border-white text-white hover:bg-primary-500 px-6 py-3 rounded-md text-base font-medium">
                    Login
                </a>
            @else
                <a href="{{ route('cars.index') }}" class="bg-white text-primary-600 hover:bg-gray-50 px-6 py-3 rounded-md text-base font-medium">
                    Browse Cars
                </a>
                <a href="{{ route('drivers.index') }}" class="border border-white text-white hover:bg-primary-500 px-6 py-3 rounded-md text-base font-medium">
                    Find Drivers
                </a>
            @endguest
        </div>
    </div>
</div>
@endsection
