<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== COMPLETE REGISTRATION DEBUG ===\n\n";

try {
    echo "🔍 STEP 1: CHECKING ROUTES...\n";
    
    // Check if registration route exists
    $routes = Route::getRoutes();
    $hasRegisterRoute = false;
    
    foreach ($routes as $route) {
        if ($route->uri() === 'api/register' && in_array('POST', $route->methods())) {
            $hasRegisterRoute = true;
            echo "✅ Registration route found: POST /api/register\n";
            echo "   Controller: " . $route->getActionName() . "\n";
            break;
        }
    }
    
    if (!$hasRegisterRoute) {
        echo "❌ Registration route NOT found\n";
        echo "Available API routes:\n";
        foreach ($routes as $route) {
            if (strpos($route->uri(), 'api/') === 0) {
                echo "  " . implode('|', $route->methods()) . " /" . $route->uri() . "\n";
            }
        }
    }
    
    echo "\n🔍 STEP 2: CHECKING DATABASE...\n";
    
    // Check users table
    $columns = DB::select("SHOW COLUMNS FROM users");
    $requiredFields = [];
    
    foreach ($columns as $column) {
        if ($column->Null === 'NO' && $column->Default === null && $column->Extra !== 'auto_increment') {
            $requiredFields[] = $column->Field;
        }
    }
    
    echo "Required fields in users table: " . implode(', ', $requiredFields) . "\n";
    
    // Check if we can create a user manually
    $testEmail = '<EMAIL>';
    DB::table('users')->where('email', $testEmail)->delete();
    
    try {
        DB::table('users')->insert([
            'name' => 'Debug Test User',
            'email' => $testEmail,
            'password' => Hash::make('password123'),
            'role' => 'client',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✅ Can create users in database\n";
        
        // Clean up
        DB::table('users')->where('email', $testEmail)->delete();
    } catch (Exception $e) {
        echo "❌ Cannot create users: " . $e->getMessage() . "\n";
    }
    
    echo "\n🔍 STEP 3: TESTING API ENDPOINT...\n";
    
    // Test the actual API endpoint
    $testData = [
        'name' => 'API Test User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'role' => 'client'
    ];
    
    echo "Testing with data: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n";
    
    // Make HTTP request to our own API
    $url = 'https://ebisera.com/api/public/api/register';
    $jsonData = json_encode($testData);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Status Code: {$httpCode}\n";
    
    if ($error) {
        echo "CURL Error: {$error}\n";
    } else {
        echo "Response: {$response}\n";
        
        // Try to decode JSON response
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "Parsed Response:\n";
            print_r($responseData);
        }
    }
    
    echo "\n🔍 STEP 4: CHECKING VALIDATION RULES...\n";
    
    // Test different validation scenarios
    $validationTests = [
        ['name' => '', 'email' => '<EMAIL>', 'password' => 'password123', 'password_confirmation' => 'password123'],
        ['name' => 'Test', 'email' => 'invalid-email', 'password' => 'password123', 'password_confirmation' => 'password123'],
        ['name' => 'Test', 'email' => '<EMAIL>', 'password' => '123', 'password_confirmation' => '123'],
        ['name' => 'Test', 'email' => '<EMAIL>', 'password' => 'password123', 'password_confirmation' => 'different'],
    ];
    
    foreach ($validationTests as $index => $testData) {
        echo "\nValidation Test " . ($index + 1) . ":\n";
        echo "Data: " . json_encode($testData) . "\n";
        
        // Check what would fail
        $issues = [];
        if (empty($testData['name'])) $issues[] = "Empty name";
        if (!filter_var($testData['email'], FILTER_VALIDATE_EMAIL)) $issues[] = "Invalid email";
        if (strlen($testData['password']) < 8) $issues[] = "Password too short";
        if ($testData['password'] !== $testData['password_confirmation']) $issues[] = "Password confirmation mismatch";
        
        if (empty($issues)) {
            echo "Expected: ✅ Should pass validation\n";
        } else {
            echo "Expected: ❌ Should fail - " . implode(', ', $issues) . "\n";
        }
    }
    
    echo "\n🔍 STEP 5: ENVIRONMENT CHECK...\n";
    
    echo "APP_ENV: " . config('app.env') . "\n";
    echo "APP_DEBUG: " . (config('app.debug') ? 'true' : 'false') . "\n";
    echo "APP_URL: " . config('app.url') . "\n";
    echo "Database: " . config('database.default') . "\n";
    
    echo "\n📋 SUMMARY:\n";
    echo "Registration route exists: " . ($hasRegisterRoute ? "✅ YES" : "❌ NO") . "\n";
    echo "Database accessible: ✅ YES\n";
    echo "API endpoint HTTP code: {$httpCode}\n";
    
    if ($httpCode === 422) {
        echo "\n🎯 DIAGNOSIS: 422 Unprocessable Content\n";
        echo "This means the route exists but validation is failing.\n";
        echo "Common causes:\n";
        echo "- Missing required fields (name, email, password, password_confirmation)\n";
        echo "- Password too short (minimum 8 characters)\n";
        echo "- Email already exists in database\n";
        echo "- Password confirmation doesn't match\n";
        echo "- Invalid email format\n";
        
        if ($response) {
            echo "\nCheck the response above for specific validation errors.\n";
        }
    } elseif ($httpCode === 404) {
        echo "\n🎯 DIAGNOSIS: 404 Not Found\n";
        echo "The registration route doesn't exist or isn't accessible.\n";
    } elseif ($httpCode === 500) {
        echo "\n🎯 DIAGNOSIS: 500 Server Error\n";
        echo "There's a server-side error. Check Laravel logs.\n";
    }
    
    echo "\n🔧 NEXT STEPS:\n";
    echo "1. Check the response details above\n";
    echo "2. Run: php check-logs.php\n";
    echo "3. Test with browser DevTools Network tab\n";
    echo "4. Try the CURL commands in test-registration-curl.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
