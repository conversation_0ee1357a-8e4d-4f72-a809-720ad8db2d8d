# 🔧 REGISTRATION DEBUG INSTRUCTIONS

## 📋 WHAT THESE FILES DO

✅ **Diagnose 422 registration errors**  
✅ **Check database and validation rules**  
✅ **Test API endpoints directly**  
✅ **Analyze <PERSON>vel logs**  
✅ **Provide specific fix recommendations**  

## 🔧 FILES CREATED

1. **`debug-registration.php`** - Checks routes, database, validation
2. **`fix-registration.php`** - Tests different registration scenarios
3. **`check-auth-controller.php`** - Finds and analyzes auth controllers
4. **`test-registration-curl.php`** - Tests API with various data combinations
5. **`check-logs.php`** - Examines <PERSON><PERSON> logs for errors
6. **`complete-registration-debug.php`** - Comprehensive diagnosis

## 🚀 DEBUGGING STEPS (RUN IN ORDER)

```bash
cd /home/<USER>/domains/ebisera.com/public_html/api

# Step 1: Complete diagnosis
php complete-registration-debug.php

# Step 2: Check specific components
php debug-registration.php
php check-auth-controller.php

# Step 3: Test with different data
php fix-registration.php

# Step 4: Check logs for errors
php check-logs.php

# Step 5: Test with CURL
php test-registration-curl.php
```

## 🎯 WHAT TO LOOK FOR

### ✅ **If Route Exists (422 Error)**
- Check validation rules in response
- Verify required fields are sent
- Check password length (minimum 8 chars)
- Ensure email is unique
- Verify password_confirmation matches

### ❌ **If Route Missing (404 Error)**
- Check if AuthController exists
- Verify routes are registered
- Check route file configuration

### 🔥 **If Server Error (500)**
- Check Laravel logs
- Look for PHP errors
- Verify database connection

## 🧪 MANUAL TESTING

### Test Valid Registration:
```bash
curl -X POST https://ebisera.com/api/public/api/register \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "role": "client"
  }' \
  -v
```

### Test Invalid Data:
```bash
curl -X POST https://ebisera.com/api/public/api/register \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "name": "Test",
    "email": "invalid-email",
    "password": "123"
  }' \
  -v
```

## 🔍 COMMON ISSUES & SOLUTIONS

### **Issue 1: Missing password_confirmation**
**Solution**: Frontend must send `password_confirmation` field

### **Issue 2: Password too short**
**Solution**: Minimum 8 characters required

### **Issue 3: Email already exists**
**Solution**: Use unique email or implement proper error handling

### **Issue 4: Missing required fields**
**Solution**: Send all required fields: name, email, password, password_confirmation

### **Issue 5: Route not found**
**Solution**: Check if AuthController exists and routes are registered

## 📊 EXPECTED RESPONSES

### ✅ **Success (201)**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "name": "Test User",
    "email": "<EMAIL>",
    "role": "client"
  },
  "token": "1|abc123..."
}
```

### ❌ **Validation Error (422)**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "email": ["The email has already been taken."],
    "password": ["The password must be at least 8 characters."]
  }
}
```

### ❌ **Route Not Found (404)**
```json
{
  "message": "Not Found"
}
```

## 🔧 FRONTEND DEBUGGING

### Check Browser DevTools:
1. **Network Tab**: See exact request/response
2. **Console**: Check for JavaScript errors
3. **Request Headers**: Verify Content-Type is application/json
4. **Request Body**: Ensure all required fields are sent

### Common Frontend Issues:
- Missing `password_confirmation` field
- Incorrect API URL
- Missing Content-Type header
- Form validation preventing submission

## 📞 TROUBLESHOOTING CHECKLIST

- [ ] Registration route exists (POST /api/register)
- [ ] AuthController has register method
- [ ] Database users table accessible
- [ ] All required fields sent in request
- [ ] Password meets minimum length (8 chars)
- [ ] Email is unique in database
- [ ] password_confirmation matches password
- [ ] Content-Type header is application/json
- [ ] Laravel logs checked for errors

## 🎯 NEXT STEPS

1. **Run complete-registration-debug.php** first
2. **Check the HTTP status code** in output
3. **Look at response details** for specific errors
4. **Follow the diagnosis** provided by the script
5. **Test with browser** after identifying issues

Run the scripts and share the output to get specific fix recommendations!
