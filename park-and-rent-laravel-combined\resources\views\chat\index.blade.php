@extends('layouts.app')

@section('title', 'Messages')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Messages</h1>
            <p class="mt-2 text-gray-600">Communicate with car owners, drivers, and customers</p>
        </div>

        @if($chats->count() > 0)
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="divide-y divide-gray-200">
                    @foreach($chats as $chat)
                        @php
                            $otherUser = $chat->user_id === auth()->id() ? $chat->recipient : $chat->user;
                            $lastMessage = $chat->messages->first();
                            $unreadCount = $chat->messages->where('sender_id', '!=', auth()->id())->where('is_read', false)->count();
                        @endphp
                        
                        <a href="{{ route('chat.show', $chat) }}" class="block hover:bg-gray-50 transition-colors duration-200">
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4 flex-1">
                                        <!-- Avatar -->
                                        <div class="h-12 w-12 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        
                                        <!-- Chat Info -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-lg font-medium text-gray-900 truncate">
                                                    {{ $otherUser->name }}
                                                </h3>
                                                @if($lastMessage)
                                                    <span class="text-sm text-gray-500">
                                                        {{ $lastMessage->created_at->diffForHumans() }}
                                                    </span>
                                                @endif
                                            </div>
                                            
                                            <!-- Related Item -->
                                            @if($chat->relatedTo)
                                                <div class="flex items-center mt-1">
                                                    @if($chat->related_to_type === 'App\\Models\\Car')
                                                        <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                                        </svg>
                                                        <span class="text-sm text-gray-600">{{ $chat->relatedTo->make }} {{ $chat->relatedTo->model }}</span>
                                                    @elseif($chat->related_to_type === 'App\\Models\\Driver')
                                                        <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                        </svg>
                                                        <span class="text-sm text-gray-600">Driver Service</span>
                                                    @endif
                                                </div>
                                            @endif
                                            
                                            <!-- Last Message -->
                                            @if($lastMessage)
                                                <p class="text-sm text-gray-600 mt-1 truncate">
                                                    @if($lastMessage->sender_id === auth()->id())
                                                        <span class="font-medium">You:</span>
                                                    @endif
                                                    {{ $lastMessage->content }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <!-- Unread Badge -->
                                    @if($unreadCount > 0)
                                        <div class="ml-4">
                                            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-primary-600 text-white text-xs font-medium">
                                                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No conversations yet</h3>
                <p class="mt-1 text-sm text-gray-500">Start a conversation by contacting a car owner or driver.</p>
                <div class="mt-6 flex justify-center space-x-4">
                    <a href="{{ route('cars.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Browse Cars
                    </a>
                    <a href="{{ route('drivers.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Find Drivers
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
