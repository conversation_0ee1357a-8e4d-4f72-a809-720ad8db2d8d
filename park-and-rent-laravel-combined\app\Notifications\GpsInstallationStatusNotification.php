<?php

namespace App\Notifications;

use App\Models\GpsInstallationRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GpsInstallationStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $gpsRequest;
    protected $oldStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(GpsInstallationRequest $gpsRequest, string $oldStatus)
    {
        $this->gpsRequest = $gpsRequest;
        $this->oldStatus = $oldStatus;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $carName = $this->gpsRequest->car->make . ' ' . $this->gpsRequest->car->model;
        $statusMessage = $this->getStatusMessage();

        $mailMessage = (new MailMessage)
            ->subject('GPS Installation Update - Park & Rent')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($statusMessage)
            ->line('**Installation Details:**')
            ->line('• Car: ' . $carName . ' (' . $this->gpsRequest->car->license_plate . ')')
            ->line('• Request ID: #' . $this->gpsRequest->id)
            ->line('• Previous Status: ' . ucfirst(str_replace('_', ' ', $this->oldStatus)))
            ->line('• Current Status: ' . ucfirst(str_replace('_', ' ', $this->gpsRequest->status)));

        // Add scheduled information if applicable
        if ($this->gpsRequest->status === 'scheduled' && $this->gpsRequest->scheduled_date) {
            $mailMessage->line('• Scheduled Date: ' . \Carbon\Carbon::parse($this->gpsRequest->scheduled_date)->format('M d, Y'))
                        ->line('• Scheduled Time: ' . $this->gpsRequest->scheduled_time);
        }

        // Add technician notes if available
        if ($this->gpsRequest->technician_notes) {
            $mailMessage->line('• Technician Notes: ' . $this->gpsRequest->technician_notes);
        }

        $mailMessage->action('View Request Details', route('gps-requests.show', $this->gpsRequest))
                   ->line('If you have any questions, please contact <NAME_EMAIL> or call 0788613669.')
                   ->line('Thank you for choosing Park & Rent!');

        return $mailMessage;
    }

    /**
     * Get status-specific message
     */
    private function getStatusMessage(): string
    {
        switch ($this->gpsRequest->status) {
            case 'scheduled':
                return 'Great news! Your GPS installation has been scheduled.';
            case 'in_progress':
                return 'Our technician is currently installing GPS in your car.';
            case 'completed':
                return 'Your GPS installation has been completed successfully! Your car is now equipped with GPS tracking.';
            case 'cancelled':
                return 'We regret to inform you that your GPS installation request has been cancelled.';
            default:
                return 'Your GPS installation request status has been updated.';
        }
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'gps_request_id' => $this->gpsRequest->id,
            'type' => 'gps_installation_status',
            'old_status' => $this->oldStatus,
            'new_status' => $this->gpsRequest->status,
            'car_name' => $this->gpsRequest->car->make . ' ' . $this->gpsRequest->car->model,
            'message' => 'GPS installation status updated'
        ];
    }
}
