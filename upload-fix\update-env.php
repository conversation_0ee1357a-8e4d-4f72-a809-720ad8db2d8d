<?php

echo "=== UPDATING ENVIRONMENT CONFIGURATION ===\n\n";

try {
    $envFile = '.env';
    
    if (!file_exists($envFile)) {
        echo "❌ .env file not found\n";
        exit(1);
    }
    
    echo "📋 Reading current .env file...\n";
    $envContent = file_get_contents($envFile);
    
    // Update FILESYSTEM_DISK
    if (strpos($envContent, 'FILESYSTEM_DISK=') !== false) {
        $envContent = preg_replace('/FILESYSTEM_DISK=.*/', 'FILESYSTEM_DISK=public_direct', $envContent);
        echo "✅ Updated FILESYSTEM_DISK to public_direct\n";
    } else {
        $envContent .= "\nFILESYSTEM_DISK=public_direct\n";
        echo "✅ Added FILESYSTEM_DISK=public_direct\n";
    }
    
    // Ensure APP_URL is correct
    if (strpos($envContent, 'APP_URL=https://ebisera.com') === false) {
        $envContent = preg_replace('/APP_URL=.*/', 'APP_URL=https://ebisera.com', $envContent);
        echo "✅ Updated APP_URL to https://ebisera.com\n";
    }
    
    // Add ASSET_URL if not present
    if (strpos($envContent, 'ASSET_URL=') === false) {
        $envContent .= "ASSET_URL=https://ebisera.com/api/public\n";
        echo "✅ Added ASSET_URL=https://ebisera.com/api/public\n";
    }
    
    // Write back to file
    file_put_contents($envFile, $envContent);
    
    echo "\n📋 Updated .env configuration:\n";
    echo "FILESYSTEM_DISK=public_direct\n";
    echo "APP_URL=https://ebisera.com\n";
    echo "ASSET_URL=https://ebisera.com/api/public\n";
    
    echo "\n✅ Environment configuration updated successfully!\n";
    echo "📋 Next: Run 'php artisan config:clear' to apply changes\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
