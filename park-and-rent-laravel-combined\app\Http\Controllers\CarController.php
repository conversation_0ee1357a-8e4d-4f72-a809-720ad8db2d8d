<?php

namespace App\Http\Controllers;

use App\Models\Car;
use Illuminate\Http\Request;

class CarController extends Controller
{
    /**
     * Display a listing of cars.
     */
    public function index(Request $request)
    {
        $query = Car::where('is_active', true)->with('owner');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('make', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by location
        if ($request->has('location') && $request->location) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by price range (daily)
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price_per_day', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price_per_day', '<=', $request->max_price);
        }

        // Filter by car make
        if ($request->has('make') && $request->make) {
            $query->where('make', $request->make);
        }

        // Filter by transmission
        if ($request->has('transmission') && $request->transmission) {
            $query->where('transmission', $request->transmission);
        }

        // Filter by fuel type
        if ($request->has('fuel_type') && $request->fuel_type) {
            $query->where('fuel_type', $request->fuel_type);
        }

        // Filter by year range
        if ($request->has('min_year') && $request->min_year) {
            $query->where('year', '>=', $request->min_year);
        }

        if ($request->has('max_year') && $request->max_year) {
            $query->where('year', '<=', $request->max_year);
        }

        // Filter by number of seats
        if ($request->has('seats') && $request->seats) {
            $query->where('seats', '>=', $request->seats);
        }

        // Filter by features
        if ($request->has('features') && is_array($request->features)) {
            foreach ($request->features as $feature) {
                $query->whereJsonContains('features', $feature);
            }
        }

        // Filter by GPS availability
        if ($request->has('has_gps') && $request->has_gps) {
            $query->where('has_gps', true);
        }

        // Filter by availability (cars with no active bookings)
        if ($request->has('available_from') && $request->available_from) {
            $availableFrom = $request->available_from;
            $availableTo = $request->available_to ?? $availableFrom;

            $query->whereDoesntHave('bookings', function($q) use ($availableFrom, $availableTo) {
                $q->where('status', '!=', 'cancelled')
                  ->where(function($dateQuery) use ($availableFrom, $availableTo) {
                      $dateQuery->whereBetween('start_date', [$availableFrom, $availableTo])
                               ->orWhereBetween('end_date', [$availableFrom, $availableTo])
                               ->orWhere(function($overlapQuery) use ($availableFrom, $availableTo) {
                                   $overlapQuery->where('start_date', '<=', $availableFrom)
                                               ->where('end_date', '>=', $availableTo);
                               });
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'latest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price_per_hour', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price_per_hour', 'desc');
                break;
            case 'year_new':
                $query->orderBy('year', 'desc');
                break;
            case 'year_old':
                $query->orderBy('year', 'asc');
                break;
            default:
                $query->latest();
                break;
        }

        $cars = $query->paginate(12);

        // Get filter options for the frontend
        $filterOptions = $this->getFilterOptions();

        return view('cars.index', compact('cars', 'filterOptions'));
    }

    /**
     * Display the specified car.
     */
    public function show(Car $car)
    {
        // Load relationships
        $car->load('owner');

        // Get related cars (same make or similar price range)
        $relatedCars = Car::where('is_active', true)
            ->where('id', '!=', $car->id)
            ->where(function ($query) use ($car) {
                $query->where('make', $car->make)
                      ->orWhereBetween('price_per_hour', [
                          $car->price_per_hour - 10,
                          $car->price_per_hour + 10
                      ]);
            })
            ->with('owner')
            ->take(3)
            ->get();

        return view('cars.show', compact('car', 'relatedCars'));
    }

    /**
     * Get filter options for advanced search
     */
    private function getFilterOptions()
    {
        return [
            'makes' => Car::where('is_active', true)
                         ->distinct()
                         ->orderBy('make')
                         ->pluck('make')
                         ->filter()
                         ->values(),
            'locations' => Car::where('is_active', true)
                             ->distinct()
                             ->orderBy('location')
                             ->pluck('location')
                             ->filter()
                             ->values(),
            'transmissions' => ['automatic', 'manual'],
            'fuel_types' => ['petrol', 'diesel', 'hybrid', 'electric'],
            'features' => [
                'Air Conditioning', 'GPS Navigation', 'Bluetooth', 'USB Charging',
                'Backup Camera', 'Sunroof', 'Leather Seats', 'Heated Seats',
                'Cruise Control', 'Parking Sensors', 'Keyless Entry', 'Premium Sound'
            ],
            'year_range' => [
                'min' => Car::where('is_active', true)->min('year') ?? date('Y') - 20,
                'max' => Car::where('is_active', true)->max('year') ?? date('Y')
            ],
            'price_range' => [
                'min' => Car::where('is_active', true)->min('price_per_day') ?? 0,
                'max' => Car::where('is_active', true)->max('price_per_day') ?? 1000
            ],
            'seat_options' => [2, 4, 5, 7, 8, 9]
        ];
    }
}
