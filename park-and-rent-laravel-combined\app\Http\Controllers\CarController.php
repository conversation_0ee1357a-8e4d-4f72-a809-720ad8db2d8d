<?php

namespace App\Http\Controllers;

use App\Models\Car;
use Illuminate\Http\Request;

class CarController extends Controller
{
    /**
     * Display a listing of cars.
     */
    public function index(Request $request)
    {
        $query = Car::where('is_active', true)->with('owner');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('make', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by location
        if ($request->has('location') && $request->location) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by price range
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'latest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price_per_hour', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price_per_hour', 'desc');
                break;
            case 'year_new':
                $query->orderBy('year', 'desc');
                break;
            case 'year_old':
                $query->orderBy('year', 'asc');
                break;
            default:
                $query->latest();
                break;
        }

        $cars = $query->paginate(12);

        return view('cars.index', compact('cars'));
    }

    /**
     * Display the specified car.
     */
    public function show(Car $car)
    {
        // Load relationships
        $car->load('owner');

        // Get related cars (same make or similar price range)
        $relatedCars = Car::where('is_active', true)
            ->where('id', '!=', $car->id)
            ->where(function ($query) use ($car) {
                $query->where('make', $car->make)
                      ->orWhereBetween('price_per_hour', [
                          $car->price_per_hour - 10,
                          $car->price_per_hour + 10
                      ]);
            })
            ->with('owner')
            ->take(3)
            ->get();

        return view('cars.show', compact('car', 'relatedCars'));
    }
}
