<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\Chat;
use App\Models\Driver;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // Get all chats where the user is either the initiator or recipient
        $chats = Chat::where('user_id', auth()->id())
            ->orWhere('recipient_id', auth()->id())
            ->with(['user', 'recipient'])
            ->orderBy('last_message_at', 'desc')
            ->get();

        return response()->json($chats);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recipient_id' => 'required|exists:users,id',
            'related_to_type' => 'nullable|in:car,driver',
            'related_to_id' => 'nullable|integer|required_with:related_to_type',
            'initial_message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if recipient exists
        $recipient = User::find($request->recipient_id);
        if (!$recipient) {
            return response()->json(['message' => 'Recipient not found'], 404);
        }

        // Check if related item exists
        if ($request->has('related_to_type') && $request->has('related_to_id')) {
            if ($request->related_to_type === 'car') {
                $car = Car::find($request->related_to_id);
                if (!$car) {
                    return response()->json(['message' => 'Related car not found'], 404);
                }
            } elseif ($request->related_to_type === 'driver') {
                $driver = Driver::find($request->related_to_id);
                if (!$driver) {
                    return response()->json(['message' => 'Related driver not found'], 404);
                }
            }
        }

        // Check if chat already exists between these users for this item
        $existingChat = Chat::where(function ($query) use ($request) {
                $query->where('user_id', auth()->id())
                    ->where('recipient_id', $request->recipient_id);
            })
            ->orWhere(function ($query) use ($request) {
                $query->where('user_id', $request->recipient_id)
                    ->where('recipient_id', auth()->id());
            });

        if ($request->has('related_to_type') && $request->has('related_to_id')) {
            $existingChat->where('related_to_type', $request->related_to_type)
                ->where('related_to_id', $request->related_to_id);
        }

        $existingChat = $existingChat->first();

        if ($existingChat) {
            // If chat exists, create a new message in it
            $message = Message::create([
                'chat_id' => $existingChat->id,
                'sender_id' => auth()->id(),
                'content' => $request->initial_message,
                'is_read' => false,
            ]);

            // Update last_message_at
            $existingChat->update([
                'last_message_at' => now(),
                'is_active' => true,
            ]);

            return response()->json([
                'message' => 'Message sent to existing chat',
                'chat' => $existingChat,
                'new_message' => $message
            ]);
        }

        // Create new chat
        $chat = Chat::create([
            'user_id' => auth()->id(),
            'recipient_id' => $request->recipient_id,
            'related_to_type' => $request->related_to_type,
            'related_to_id' => $request->related_to_id,
            'is_active' => true,
            'last_message_at' => now(),
        ]);

        // Create initial message
        $message = Message::create([
            'chat_id' => $chat->id,
            'sender_id' => auth()->id(),
            'content' => $request->initial_message,
            'is_read' => false,
        ]);

        return response()->json([
            'message' => 'Chat created successfully',
            'chat' => $chat,
            'initial_message' => $message
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $chat = Chat::with(['user', 'recipient', 'messages' => function($query) {
            $query->with('sender')->orderBy('created_at', 'asc');
        }])->findOrFail($id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Mark messages as read if user is the recipient
        if (auth()->id() == $chat->recipient_id) {
            Message::where('chat_id', $chat->id)
                ->where('sender_id', '!=', auth()->id())
                ->where('is_read', false)
                ->update([
                    'is_read' => true,
                    'read_at' => now(),
                ]);
        }

        return response()->json($chat);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $chat = Chat::findOrFail($id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat->update([
            'is_active' => $request->is_active,
        ]);

        return response()->json([
            'message' => 'Chat updated successfully',
            'chat' => $chat
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $chat = Chat::findOrFail($id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Instead of deleting, mark as inactive
        $chat->update(['is_active' => false]);

        return response()->json([
            'message' => 'Chat archived successfully'
        ]);
    }

    /**
     * Get all chats for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function myChats()
    {
        $chats = Chat::where(function ($query) {
                $query->where('user_id', auth()->id())
                    ->orWhere('recipient_id', auth()->id());
            })
            ->with(['user', 'recipient', 'messages' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(1);
            }])
            ->orderBy('last_message_at', 'desc')
            ->get();

        // Add unread count for each chat
        $chats->each(function ($chat) {
            $chat->unread_count = Message::where('chat_id', $chat->id)
                ->where('sender_id', '!=', auth()->id())
                ->where('is_read', false)
                ->count();
        });

        return response()->json($chats);
    }

    /**
     * Get unread message count for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCount()
    {
        $count = Message::whereHas('chat', function ($query) {
                $query->where('user_id', auth()->id())
                    ->orWhere('recipient_id', auth()->id());
            })
            ->where('sender_id', '!=', auth()->id())
            ->where('is_read', false)
            ->count();

        return response()->json([
            'unread_count' => $count
        ]);
    }
}
