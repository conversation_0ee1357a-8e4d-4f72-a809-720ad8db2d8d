@extends('layouts.app')

@section('title', 'Booking Details')

@section('content')
<div class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        {{ session('success') }}
                    </p>
                </div>
            </div>
        </div>
        @endif

        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('bookings.index') }}" class="inline-flex items-center text-primary-600 hover:text-primary-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to My Bookings
            </a>
        </div>

        <!-- Booking Details -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Booking #{{ $booking->id }}
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Booking details and status information.
                </p>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <!-- Status -->
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            @if($booking->status === 'pending')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                            @elseif($booking->status === 'confirmed')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Confirmed
                                </span>
                            @elseif($booking->status === 'cancelled')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Cancelled
                                </span>
                            @elseif($booking->status === 'completed')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Completed
                                </span>
                            @endif
                        </dd>
                    </div>

                    <!-- Booking Type -->
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Type</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            @if($booking->bookable_type === 'App\\Models\\Car')
                                Car Rental
                            @elseif($booking->bookable_type === 'App\\Models\\Driver')
                                Driver Service
                            @endif
                        </dd>
                    </div>

                    <!-- Item Details -->
                    @if($booking->bookable_type === 'App\\Models\\Car')
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Car</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                @if($booking->bookable)
                                    <div class="flex items-center">
                                        @if($booking->bookable->images && count($booking->bookable->images) > 0)
                                            <img class="h-12 w-12 rounded-lg object-cover mr-3" src="{{ asset('storage/' . $booking->bookable->images[0]) }}" alt="Car">
                                        @endif
                                        <div>
                                            <div class="font-medium">{{ $booking->bookable->make }} {{ $booking->bookable->model }}</div>
                                            <div class="text-gray-500">{{ $booking->bookable->year }} • {{ $booking->bookable->location }}</div>
                                        </div>
                                    </div>
                                @endif
                            </dd>
                        </div>
                    @elseif($booking->bookable_type === 'App\\Models\\Driver')
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Driver</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                @if($booking->bookable)
                                    <div class="flex items-center">
                                        @if($booking->bookable->profile_image)
                                            <img class="h-12 w-12 rounded-full object-cover mr-3" src="{{ asset('storage/' . $booking->bookable->profile_image) }}" alt="Driver">
                                        @else
                                            <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                            </div>
                                        @endif
                                        <div>
                                            <div class="font-medium">{{ $booking->bookable->name }}</div>
                                            <div class="text-gray-500">{{ $booking->bookable->location }}</div>
                                        </div>
                                    </div>
                                @endif
                            </dd>
                        </div>
                    @endif

                    <!-- Dates -->
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            @if($booking->bookable_type === 'App\\Models\\Car')
                                Rental Period
                            @else
                                Service Date
                            @endif
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ \Carbon\Carbon::parse($booking->start_date)->format('M d, Y') }}
                            @if($booking->end_date && $booking->end_date !== $booking->start_date)
                                - {{ \Carbon\Carbon::parse($booking->end_date)->format('M d, Y') }}
                            @endif
                            @if($booking->start_time)
                                at {{ \Carbon\Carbon::parse($booking->start_time)->format('g:i A') }}
                            @endif
                        </dd>
                    </div>

                    <!-- Service Type (for drivers) -->
                    @if($booking->service_type)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Service Type</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ ucfirst(str_replace('_', ' ', $booking->service_type)) }}
                            @if($booking->duration)
                                ({{ $booking->duration }} hours)
                            @endif
                        </dd>
                    </div>
                    @endif

                    <!-- Locations -->
                    @if($booking->pickup_location)
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Pickup Location</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $booking->pickup_location }}
                        </dd>
                    </div>
                    @endif

                    @if($booking->destination)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Destination</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $booking->destination }}
                        </dd>
                    </div>
                    @endif

                    <!-- Total Amount -->
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <span class="text-2xl font-bold text-primary-600">${{ number_format($booking->total_amount, 2) }}</span>
                        </dd>
                    </div>

                    <!-- Message -->
                    @if($booking->message)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Message</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $booking->message }}
                        </dd>
                    </div>
                    @endif

                    <!-- Booking Date -->
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Booked On</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $booking->created_at->format('M d, Y \a\t g:i A') }}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex justify-between">
            <div>
                @if($booking->status === 'pending')
                    <form action="{{ route('bookings.destroy', $booking) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" onclick="return confirm('Are you sure you want to cancel this booking?')" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Cancel Booking
                        </button>
                    </form>
                @endif
            </div>
            
            <div class="space-x-3">
                <a href="{{ route('bookings.index') }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
                    Back to Bookings
                </a>
                
                @if($booking->status === 'confirmed')
                    <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Contact Owner
                    </button>
                @endif
            </div>
        </div>

        <!-- Contact Information -->
        @if($booking->status === 'confirmed')
        <div class="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">
                        Booking Confirmed!
                    </h3>
                    <div class="mt-2 text-sm text-green-700">
                        <p>Your booking has been confirmed. Contact details:</p>
                        <p class="mt-1 font-medium">Phone: 0788613669</p>
                        <p class="font-medium">Email: <EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
