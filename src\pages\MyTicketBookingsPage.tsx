import React, { useState, useEffect } from 'react';
import { 
  Plane, Bus, Calendar, MapPin, Users, Clock, 
  CheckCircle, XCircle, AlertCircle, Eye, Filter,
  Download, Mail, Phone
} from 'lucide-react';
import MainLayout from '../components/layout/MainLayout';
import Button from '../components/ui/Button';
import Spinner from '../components/ui/Spinner';
import { useAuth } from '../context/AuthContext';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface TicketBooking {
  id: string;
  ticket_type: 'flight' | 'bus';
  passenger_name: string;
  passenger_email: string;
  passenger_phone: string;
  departure_city: string;
  destination_country: string;
  destination_city: string;
  departure_date: string;
  return_date?: string;
  trip_type: 'one_way' | 'round_trip';
  passengers_count: number;
  class_type: string;
  special_requests?: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

const MyTicketBookingsPage: React.FC = () => {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<TicketBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<TicketBooking | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch user's ticket bookings
  const fetchMyTicketBookings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiClient.get(API_ENDPOINTS.MY_TICKET_BOOKINGS);
      if (response.data.success) {
        setBookings(response.data.data || []);
      } else {
        setError(response.data.message || 'Failed to fetch ticket bookings');
      }
    } catch (err: any) {
      console.error('Error fetching ticket bookings:', err);
      if (err.response?.data?.message) {
        setError(err.response.data.message);
      } else {
        setError('Failed to load your ticket bookings. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchMyTicketBookings();
    }
  }, [user]);

  // Filter bookings based on status
  const filteredBookings = bookings.filter(booking => {
    if (statusFilter === 'all') return true;
    return booking.status === statusFilter;
  });

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'confirmed':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle size={16} className="text-green-600" />,
          text: 'Confirmed'
        };
      case 'pending':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <Clock size={16} className="text-yellow-600" />,
          text: 'Pending'
        };
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle size={16} className="text-red-600" />,
          text: 'Cancelled'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <AlertCircle size={16} className="text-gray-600" />,
          text: status
        };
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Please Log In</h2>
            <p className="text-gray-600">You need to be logged in to view your ticket bookings.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Ticket Bookings</h1>
            <p className="text-gray-600">Track and manage your flight and bus ticket bookings</p>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Filter size={20} className="text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Filter by status:</span>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">All Bookings</option>
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <div className="text-sm text-gray-600">
                {filteredBookings.length} of {bookings.length} bookings
              </div>
            </div>
          </div>

          {/* Content */}
          {isLoading ? (
            <div className="flex justify-center py-12">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
              <XCircle size={48} className="mx-auto text-red-400 mb-4" />
              <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Bookings</h3>
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={fetchMyTicketBookings} variant="outline" className="border-red-500 text-red-700 hover:bg-red-50">
                Try Again
              </Button>
            </div>
          ) : filteredBookings.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <Plane size={64} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {statusFilter === 'all' ? 'No Ticket Bookings Yet' : `No ${statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)} Bookings`}
              </h3>
              <p className="text-gray-600 mb-6">
                {statusFilter === 'all' 
                  ? "You haven't made any ticket bookings yet. Start by booking your first flight or bus ticket!"
                  : `You don't have any ${statusFilter} bookings at the moment.`
                }
              </p>
              {statusFilter === 'all' && (
                <Button onClick={() => window.location.href = '/ticket-booking'}>
                  Book Your First Ticket
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredBookings.map((booking) => {
                const statusDisplay = getStatusDisplay(booking.status);
                return (
                  <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                      {/* Left side - Booking info */}
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          {booking.ticket_type === 'flight' ? (
                            <div className="flex items-center gap-2 text-blue-600">
                              <Plane size={20} />
                              <span className="font-medium">Flight</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-green-600">
                              <Bus size={20} />
                              <span className="font-medium">Bus</span>
                            </div>
                          )}
                          <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${statusDisplay.color}`}>
                            {statusDisplay.icon}
                            {statusDisplay.text}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <div>
                            <div className="flex items-center gap-2 text-gray-600 mb-1">
                              <MapPin size={16} />
                              <span className="text-sm font-medium">Route</span>
                            </div>
                            <p className="text-gray-900 font-medium">
                              {booking.departure_city} → {booking.destination_city}
                            </p>
                            <p className="text-sm text-gray-600">{booking.destination_country}</p>
                          </div>

                          <div>
                            <div className="flex items-center gap-2 text-gray-600 mb-1">
                              <Calendar size={16} />
                              <span className="text-sm font-medium">Departure</span>
                            </div>
                            <p className="text-gray-900 font-medium">{formatDate(booking.departure_date)}</p>
                            {booking.return_date && (
                              <p className="text-sm text-gray-600">Return: {formatDate(booking.return_date)}</p>
                            )}
                          </div>

                          <div>
                            <div className="flex items-center gap-2 text-gray-600 mb-1">
                              <Users size={16} />
                              <span className="text-sm font-medium">Passengers</span>
                            </div>
                            <p className="text-gray-900 font-medium">{booking.passengers_count} passenger(s)</p>
                            <p className="text-sm text-gray-600 capitalize">{booking.class_type} class</p>
                          </div>
                        </div>
                      </div>

                      {/* Right side - Actions */}
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedBooking(booking);
                            setShowDetailsModal(true);
                          }}
                          className="flex items-center gap-2"
                        >
                          <Eye size={16} />
                          View Details
                        </Button>
                      </div>
                    </div>

                    {/* Booking metadata */}
                    <div className="mt-4 pt-4 border-t border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm text-gray-500">
                      <div>Booking ID: <span className="font-mono">{booking.id}</span></div>
                      <div>Booked on: {formatDate(booking.created_at)}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Booking Details Modal */}
          {showDetailsModal && selectedBooking && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Booking Details</h3>
                    <button
                      onClick={() => {
                        setShowDetailsModal(false);
                        setSelectedBooking(null);
                      }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XCircle size={24} />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Trip Information */}
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        {selectedBooking.ticket_type === 'flight' ? (
                          <Plane className="mr-2 text-blue-600" size={20} />
                        ) : (
                          <Bus className="mr-2 text-green-600" size={20} />
                        )}
                        Trip Information
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Ticket Type</label>
                          <p className="text-gray-900 font-medium capitalize flex items-center">
                            {selectedBooking.ticket_type === 'flight' ? (
                              <><Plane className="mr-1 text-blue-600" size={16} /> Flight</>
                            ) : (
                              <><Bus className="mr-1 text-green-600" size={16} /> Bus</>
                            )}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Route</label>
                          <p className="text-gray-900 font-medium">
                            {selectedBooking.departure_city} → {selectedBooking.destination_city}
                          </p>
                          <p className="text-sm text-gray-600">{selectedBooking.destination_country}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Departure Date</label>
                          <p className="text-gray-900 font-medium">{formatDateTime(selectedBooking.departure_date)}</p>
                        </div>
                        {selectedBooking.return_date && (
                          <div>
                            <label className="text-sm font-medium text-gray-600">Return Date</label>
                            <p className="text-gray-900 font-medium">{formatDateTime(selectedBooking.return_date)}</p>
                          </div>
                        )}
                        <div>
                          <label className="text-sm font-medium text-gray-600">Trip Type</label>
                          <p className="text-gray-900 capitalize">{selectedBooking.trip_type?.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Class</label>
                          <p className="text-gray-900 capitalize">{selectedBooking.class_type}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Passengers</label>
                          <p className="text-gray-900 font-medium">{selectedBooking.passengers_count} passenger(s)</p>
                        </div>
                      </div>
                    </div>

                    {/* Passenger Information */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Users className="mr-2" size={20} />
                        Passenger Information
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Full Name</label>
                          <p className="text-gray-900 font-medium">{selectedBooking.passenger_name}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Email Address</label>
                          <p className="text-gray-900 flex items-center">
                            <Mail size={16} className="mr-2 text-gray-500" />
                            {selectedBooking.passenger_email}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Phone Number</label>
                          <p className="text-gray-900 flex items-center">
                            <Phone size={16} className="mr-2 text-gray-500" />
                            {selectedBooking.passenger_phone || 'Not provided'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Booking Status */}
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Calendar className="mr-2" size={20} />
                        Booking Status
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Current Status</label>
                          <p className="mt-1">
                            <span className={`inline-flex items-center gap-2 px-3 py-1 text-sm font-semibold rounded-full border ${getStatusDisplay(selectedBooking.status).color}`}>
                              {getStatusDisplay(selectedBooking.status).icon}
                              {getStatusDisplay(selectedBooking.status).text}
                            </span>
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Booking Date</label>
                          <p className="text-gray-900">{formatDateTime(selectedBooking.created_at)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Last Updated</label>
                          <p className="text-gray-900">{formatDateTime(selectedBooking.updated_at)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Booking ID</label>
                          <p className="text-gray-900 font-mono text-sm">{selectedBooking.id}</p>
                        </div>
                      </div>
                    </div>

                    {/* Special Requests */}
                    <div className="bg-purple-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <AlertCircle className="mr-2" size={20} />
                        Special Requests
                      </h4>
                      <div className="bg-white p-3 rounded border min-h-[80px]">
                        <p className="text-gray-900">
                          {selectedBooking.special_requests || 'No special requests'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Status Information */}
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">What does my booking status mean?</h5>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Clock size={16} className="text-yellow-600" />
                        <span><strong>Pending:</strong> Your booking is being reviewed and will be confirmed soon.</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle size={16} className="text-green-600" />
                        <span><strong>Confirmed:</strong> Your booking is confirmed! You'll receive further details via email.</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <XCircle size={16} className="text-red-600" />
                        <span><strong>Cancelled:</strong> This booking has been cancelled. Contact support if you have questions.</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-6 flex justify-end pt-4 border-t border-gray-200">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowDetailsModal(false);
                        setSelectedBooking(null);
                      }}
                    >
                      Close
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default MyTicketBookingsPage;
