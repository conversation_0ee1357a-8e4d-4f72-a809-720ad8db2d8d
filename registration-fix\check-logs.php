<?php

echo "=== CHECKING LARAVEL LOGS ===\n\n";

try {
    $logPath = 'storage/logs/laravel.log';
    
    if (!file_exists($logPath)) {
        echo "❌ Laravel log file not found at: {$logPath}\n";
        
        // Check for other possible log locations
        $possibleLogs = [
            'storage/logs/laravel-' . date('Y-m-d') . '.log',
            'storage/logs/daily/' . date('Y-m-d') . '.log',
            'storage/logs/single.log'
        ];
        
        foreach ($possibleLogs as $possibleLog) {
            if (file_exists($possibleLog)) {
                echo "✅ Found log file: {$possibleLog}\n";
                $logPath = $possibleLog;
                break;
            }
        }
        
        if (!file_exists($logPath)) {
            echo "Creating empty log file...\n";
            touch($logPath);
        }
    } else {
        echo "✅ Laravel log file found: {$logPath}\n";
    }
    
    if (file_exists($logPath)) {
        $logSize = filesize($logPath);
        echo "Log file size: " . number_format($logSize) . " bytes\n\n";
        
        if ($logSize > 0) {
            echo "🔍 RECENT LOG ENTRIES (last 50 lines):\n";
            echo str_repeat("=", 60) . "\n";
            
            // Get last 50 lines
            $lines = file($logPath);
            $recentLines = array_slice($lines, -50);
            
            foreach ($recentLines as $line) {
                echo $line;
            }
            
            echo str_repeat("=", 60) . "\n\n";
            
            // Look for registration-related errors
            echo "🔍 SEARCHING FOR REGISTRATION ERRORS:\n";
            $registrationErrors = [];
            
            foreach ($lines as $lineNum => $line) {
                if (stripos($line, 'register') !== false || 
                    stripos($line, '422') !== false || 
                    stripos($line, 'validation') !== false ||
                    stripos($line, 'unprocessable') !== false) {
                    $registrationErrors[] = "Line " . ($lineNum + 1) . ": " . trim($line);
                }
            }
            
            if (!empty($registrationErrors)) {
                echo "Found " . count($registrationErrors) . " registration-related entries:\n";
                foreach (array_slice($registrationErrors, -10) as $error) {
                    echo "  {$error}\n";
                }
            } else {
                echo "No registration-related errors found in logs\n";
            }
            
        } else {
            echo "Log file is empty\n";
        }
    }
    
    echo "\n🔍 CHECKING LOG CONFIGURATION:\n";
    
    // Check logging configuration
    echo "Log channel: " . config('logging.default') . "\n";
    echo "Log level: " . config('logging.channels.single.level', 'not set') . "\n";
    echo "Log path: " . config('logging.channels.single.path', 'not set') . "\n";
    
    echo "\n📋 LOG MONITORING COMMANDS:\n";
    echo "1. Watch logs in real-time:\n";
    echo "   tail -f {$logPath}\n\n";
    
    echo "2. Search for specific errors:\n";
    echo "   grep -i 'register' {$logPath}\n";
    echo "   grep -i '422' {$logPath}\n";
    echo "   grep -i 'validation' {$logPath}\n\n";
    
    echo "3. Clear logs:\n";
    echo "   > {$logPath}\n\n";
    
    echo "4. Check last 20 lines:\n";
    echo "   tail -20 {$logPath}\n\n";
    
    echo "🔧 ENABLE DEBUG MODE:\n";
    echo "To get more detailed error information:\n";
    echo "1. Edit .env file\n";
    echo "2. Set APP_DEBUG=true\n";
    echo "3. Set LOG_LEVEL=debug\n";
    echo "4. Clear config cache: php artisan config:clear\n";
    echo "5. Try registration again\n";
    echo "6. Check logs for detailed error information\n\n";
    
    echo "⚠️ SECURITY NOTE:\n";
    echo "Remember to set APP_DEBUG=false in production!\n";
    
} catch (Exception $e) {
    echo "❌ Error checking logs: " . $e->getMessage() . "\n";
}
