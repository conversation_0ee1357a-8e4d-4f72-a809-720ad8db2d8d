<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create car owner user
        User::create([
            'name' => 'Car Owner',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'owner',
            'email_verified_at' => now(),
        ]);

        // Create driver user
        User::create([
            'name' => 'Driver User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'driver',
            'email_verified_at' => now(),
            'license_image_url' => 'https://example.com/license.jpg',
            'license_verification_status' => 'verified',
        ]);

        // Create client user
        User::create([
            'name' => 'Client User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'client',
            'email_verified_at' => now(),
        ]);
    }
}
