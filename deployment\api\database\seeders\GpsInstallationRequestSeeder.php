<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GpsInstallationRequest;
use App\Models\User;
use App\Models\Car;

class GpsInstallationRequestSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get some users and cars
        $users = User::where('role', 'owner')->take(3)->get();
        $cars = Car::take(3)->get();

        if ($users->count() > 0 && $cars->count() > 0) {
            // Create sample GPS installation requests
            GpsInstallationRequest::create([
                'user_id' => $users[0]->id,
                'car_id' => $cars[0]->id,
                'car_make' => $cars[0]->make,
                'car_model' => $cars[0]->model,
                'car_year' => $cars[0]->year,
                'license_plate' => $cars[0]->license_plate ?? 'ABC-100',
                'reason' => 'I want to track my vehicle for security purposes and monitor its location when rented out.',
                'contact_phone' => '******-0123',
                'preferred_installation_date' => '2025-05-30',
                'status' => 'pending',
            ]);

            if ($users->count() > 1 && $cars->count() > 1) {
                GpsInstallationRequest::create([
                    'user_id' => $users[1]->id,
                    'car_id' => $cars[1]->id,
                    'car_make' => $cars[1]->make,
                    'car_model' => $cars[1]->model,
                    'car_year' => $cars[1]->year,
                    'license_plate' => $cars[1]->license_plate,
                    'reason' => 'Need GPS tracking for fleet management and theft protection.',
                    'contact_phone' => '******-0456',
                    'preferred_installation_date' => '2025-06-01',
                    'status' => 'approved',
                    'admin_notes' => 'Approved for installation. Contact customer to schedule.',
                    'approved_at' => now(),
                ]);
            }

            if ($users->count() > 2 && $cars->count() > 2) {
                GpsInstallationRequest::create([
                    'user_id' => $users[2]->id,
                    'car_id' => $cars[2]->id,
                    'car_make' => $cars[2]->make,
                    'car_model' => $cars[2]->model,
                    'car_year' => $cars[2]->year,
                    'license_plate' => $cars[2]->license_plate,
                    'reason' => 'Want to monitor vehicle usage and ensure customer safety.',
                    'contact_phone' => '******-0789',
                    'preferred_installation_date' => '2025-05-28',
                    'status' => 'completed',
                    'admin_notes' => 'GPS device installed successfully. System is active.',
                    'approved_at' => now()->subDays(2),
                    'completed_at' => now()->subDays(1),
                ]);
            }
        }
    }
}
