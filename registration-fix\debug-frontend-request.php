<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== DEBUGGING FRONTEND REGISTRATION REQUESTS ===\n\n";

try {
    echo "🔍 STEP 1: CHECKING RECENT REGISTRATION ATTEMPTS...\n";
    
    // Check Laravel logs for recent 422 errors
    $logPath = 'storage/logs/laravel.log';
    
    if (file_exists($logPath)) {
        $logContent = file_get_contents($logPath);
        $lines = explode("\n", $logContent);
        
        // Look for recent validation errors
        $recentErrors = [];
        $currentTime = time();
        
        foreach ($lines as $line) {
            if (strpos($line, '422') !== false || 
                strpos($line, 'validation') !== false ||
                strpos($line, 'register') !== false) {
                
                // Check if it's from today
                if (strpos($line, date('Y-m-d')) !== false) {
                    $recentErrors[] = $line;
                }
            }
        }
        
        if (!empty($recentErrors)) {
            echo "Found recent registration errors:\n";
            foreach (array_slice($recentErrors, -5) as $error) {
                echo "  " . trim($error) . "\n";
            }
        } else {
            echo "No recent registration errors found in logs\n";
        }
    }
    
    echo "\n🔍 STEP 2: TESTING DIFFERENT VALIDATION SCENARIOS...\n";
    
    // Test scenarios that might cause 422
    $testCases = [
        [
            'name' => 'Missing password_confirmation',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Empty name',
            'data' => [
                'name' => '',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Invalid email',
            'data' => [
                'name' => 'Test User',
                'email' => 'invalid-email',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Short password',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => '123',
                'password_confirmation' => '123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Password mismatch',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'different123',
                'role' => 'client'
            ]
        ],
        [
            'name' => 'Missing role',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123'
            ]
        ],
        [
            'name' => 'Invalid role',
            'data' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'invalid_role'
            ]
        ]
    ];
    
    foreach ($testCases as $test) {
        echo "\nTesting: {$test['name']}\n";
        echo "Data: " . json_encode($test['data']) . "\n";
        
        // Make request to API
        $url = 'https://ebisera.com/api/public/api/register';
        $jsonData = json_encode($test['data']);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP Code: {$httpCode}\n";
        
        if ($httpCode === 422) {
            echo "Response: {$response}\n";
            $responseData = json_decode($response, true);
            if (isset($responseData['errors'])) {
                echo "Validation Errors:\n";
                foreach ($responseData['errors'] as $field => $errors) {
                    echo "  {$field}: " . implode(', ', $errors) . "\n";
                }
            }
        } elseif ($httpCode === 201) {
            echo "✅ Success\n";
        } else {
            echo "❌ Unexpected response: {$response}\n";
        }
        
        echo str_repeat("-", 50) . "\n";
    }
    
    echo "\n🔍 STEP 3: CHECKING VALIDATION RULES...\n";
    
    // Show current validation rules
    echo "Current validation rules in AuthController:\n";
    echo "- name: required|string|max:255\n";
    echo "- email: required|string|email|max:255|unique:users\n";
    echo "- password: required|confirmed|Password::defaults()\n";
    echo "- role: required|in:client,owner,driver\n";
    echo "- phone_number: nullable|string|max:20\n";
    echo "- license_image_url: nullable|string\n";
    
    echo "\n📋 COMMON FRONTEND ISSUES:\n";
    echo "1. Missing 'password_confirmation' field\n";
    echo "2. Sending 'confirmPassword' instead of 'password_confirmation'\n";
    echo "3. Empty required fields (name, email, password, role)\n";
    echo "4. Invalid email format\n";
    echo "5. Password too short (minimum 8 characters)\n";
    echo "6. Role not in allowed values (client, owner, driver)\n";
    echo "7. Email already exists in database\n";
    
    echo "\n🔧 DEBUGGING TIPS:\n";
    echo "1. Check browser DevTools Network tab\n";
    echo "2. Look at exact request payload\n";
    echo "3. Check response body for specific validation errors\n";
    echo "4. Verify all required fields are being sent\n";
    echo "5. Check field names match exactly (password_confirmation)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
