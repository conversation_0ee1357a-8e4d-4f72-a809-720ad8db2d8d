<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('ticket_type', ['flight', 'bus']);
            $table->string('passenger_name');
            $table->string('passenger_email');
            $table->string('passenger_phone');
            $table->string('departure_country')->default('Rwanda');
            $table->string('departure_city');
            $table->string('destination_country'); // East African countries
            $table->string('destination_city');
            $table->date('departure_date');
            $table->date('return_date')->nullable();
            $table->enum('trip_type', ['one_way', 'round_trip']);
            $table->integer('passengers_count')->default(1);
            $table->enum('class_type', ['economy', 'business', 'first'])->default('economy');
            $table->text('special_requests')->nullable();
            $table->decimal('estimated_price', 10, 2)->nullable();
            $table->enum('status', ['pending', 'contacted', 'confirmed', 'cancelled'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->timestamp('contacted_at')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_bookings');
    }
};
