<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Car;
use App\Models\Driver;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone_number' => '0788613669',
            'email_verified_at' => now(),
        ]);

        // Create sample owner
        $owner = User::create([
            'name' => 'John Owner',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'owner',
            'phone_number' => '0788613669',
            'email_verified_at' => now(),
        ]);

        // Create sample client
        $client = User::create([
            'name' => 'Jane Client',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'client',
            'phone_number' => '0788613669',
            'email_verified_at' => now(),
        ]);

        // Create sample driver user
        $driverUser = User::create([
            'name' => 'Mike Driver',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'driver',
            'phone_number' => '0788613669',
            'email_verified_at' => now(),
        ]);

        // Create sample cars
        $cars = [
            [
                'owner_id' => $owner->id,
                'make' => 'Toyota',
                'model' => 'Camry',
                'year' => 2022,
                'images' => [],
                'description' => 'Comfortable and reliable sedan perfect for city driving and long trips.',
                'features' => ['Air Conditioning', 'Bluetooth', 'GPS Navigation', 'Backup Camera'],
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 25.00,
                'availability_notes' => 'Available 24/7',
                'is_active' => true,
            ],
            [
                'owner_id' => $owner->id,
                'make' => 'Honda',
                'model' => 'CR-V',
                'year' => 2021,
                'images' => [],
                'description' => 'Spacious SUV ideal for family trips and outdoor adventures.',
                'features' => ['All-Wheel Drive', 'Sunroof', 'Heated Seats', 'Apple CarPlay'],
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 35.00,
                'availability_notes' => 'Available weekdays and weekends',
                'is_active' => true,
            ],
            [
                'owner_id' => $owner->id,
                'make' => 'BMW',
                'model' => 'X3',
                'year' => 2023,
                'images' => [],
                'description' => 'Luxury SUV with premium features and excellent performance.',
                'features' => ['Leather Seats', 'Premium Sound', 'Panoramic Roof', 'Wireless Charging'],
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 50.00,
                'availability_notes' => 'Available by appointment',
                'is_active' => true,
            ],
        ];

        foreach ($cars as $carData) {
            Car::create($carData);
        }

        // Create sample drivers
        $drivers = [
            [
                'user_id' => $driverUser->id,
                'name' => 'Mike Professional',
                'age' => 35,
                'experience' => 10,
                'profile_image' => null,
                'license_number' => 'DL123456789',
                'license_verification_status' => 'verified',
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 15.00,
                'rating' => 4.8,
                'reviews' => 127,
                'specialties' => ['City Driving', 'Airport Transfers', 'Long Distance'],
                'availability_notes' => 'Available 24/7',
                'is_available' => true,
            ],
            [
                'user_id' => $driverUser->id,
                'name' => 'Sarah Expert',
                'age' => 28,
                'experience' => 7,
                'profile_image' => null,
                'license_number' => 'DL987654321',
                'license_verification_status' => 'verified',
                'location' => 'Kigali, Rwanda',
                'price_per_hour' => 18.00,
                'rating' => 4.9,
                'reviews' => 89,
                'specialties' => ['Tourism', 'Business Trips', 'Events'],
                'availability_notes' => 'Available weekdays 8AM-6PM',
                'is_available' => true,
            ],
        ];

        foreach ($drivers as $driverData) {
            Driver::create($driverData);
        }
    }
}
