<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->adminDashboard();
            case 'owner':
                return $this->ownerDashboard();
            case 'driver':
                return $this->driverDashboard();
            case 'client':
            default:
                return $this->clientDashboard();
        }
    }

    /**
     * Client dashboard
     */
    private function clientDashboard()
    {
        $userId = auth()->id();
        
        // Get booking statistics
        $totalBookings = Booking::where('user_id', $userId)->count();
        $pendingBookings = Booking::where('user_id', $userId)->where('status', 'pending')->count();
        $confirmedBookings = Booking::where('user_id', $userId)->where('status', 'confirmed')->count();
        $totalSpent = Booking::where('user_id', $userId)->where('status', '!=', 'cancelled')->sum('total_amount');
        
        // Get recent bookings
        $recentBookings = Booking::where('user_id', $userId)
            ->with(['bookable'])
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard.client', compact(
            'totalBookings',
            'pendingBookings', 
            'confirmedBookings',
            'totalSpent',
            'recentBookings'
        ));
    }

    /**
     * Owner dashboard
     */
    private function ownerDashboard()
    {
        $userId = auth()->id();
        
        // Get car statistics
        $totalCars = Car::where('owner_id', $userId)->count();
        $activeCars = Car::where('owner_id', $userId)->where('is_active', true)->count();
        
        // Get booking statistics for owner's cars
        $pendingBookings = Booking::whereHas('bookable', function($query) use ($userId) {
            $query->where('bookable_type', 'App\\Models\\Car')
                  ->whereHas('owner', function($q) use ($userId) {
                      $q->where('id', $userId);
                  });
        })->where('status', 'pending')->count();
        
        $totalEarnings = Booking::whereHas('bookable', function($query) use ($userId) {
            $query->where('bookable_type', 'App\\Models\\Car')
                  ->whereHas('owner', function($q) use ($userId) {
                      $q->where('id', $userId);
                  });
        })->where('status', '!=', 'cancelled')->sum('total_amount');
        
        // Get recent cars
        $recentCars = Car::where('owner_id', $userId)
            ->latest()
            ->take(5)
            ->get();
            
        // Get recent bookings for owner's cars
        $recentBookings = Booking::whereHas('bookable', function($query) use ($userId) {
            $query->where('bookable_type', 'App\\Models\\Car')
                  ->whereHas('owner', function($q) use ($userId) {
                      $q->where('id', $userId);
                  });
        })->with(['bookable', 'user'])
        ->latest()
        ->take(5)
        ->get();

        return view('dashboard.owner', compact(
            'totalCars',
            'activeCars',
            'pendingBookings',
            'totalEarnings',
            'recentCars',
            'recentBookings'
        ));
    }

    /**
     * Driver dashboard
     */
    private function driverDashboard()
    {
        $userId = auth()->id();
        
        // Get driver profile
        $driver = Driver::where('user_id', $userId)->first();
        
        if (!$driver) {
            return redirect()->route('driver.profile.create')
                ->with('info', 'Please complete your driver profile first.');
        }
        
        // Get booking statistics for this driver
        $totalBookings = Booking::where('bookable_type', 'App\\Models\\Driver')
            ->where('bookable_id', $driver->id)
            ->count();
            
        $pendingBookings = Booking::where('bookable_type', 'App\\Models\\Driver')
            ->where('bookable_id', $driver->id)
            ->where('status', 'pending')
            ->count();
            
        $confirmedBookings = Booking::where('bookable_type', 'App\\Models\\Driver')
            ->where('bookable_id', $driver->id)
            ->where('status', 'confirmed')
            ->count();
            
        $totalEarnings = Booking::where('bookable_type', 'App\\Models\\Driver')
            ->where('bookable_id', $driver->id)
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');
        
        // Get recent bookings
        $recentBookings = Booking::where('bookable_type', 'App\\Models\\Driver')
            ->where('bookable_id', $driver->id)
            ->with(['user'])
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard.driver', compact(
            'driver',
            'totalBookings',
            'pendingBookings',
            'confirmedBookings',
            'totalEarnings',
            'recentBookings'
        ));
    }

    /**
     * Admin dashboard
     */
    private function adminDashboard()
    {
        // Get overall statistics
        $totalUsers = \App\Models\User::count();
        $totalCars = Car::count();
        $totalDrivers = Driver::count();
        $totalBookings = Booking::count();
        $pendingBookings = Booking::where('status', 'pending')->count();
        $totalRevenue = Booking::where('status', '!=', 'cancelled')->sum('total_amount');
        
        // Get recent activity
        $recentBookings = Booking::with(['bookable', 'user'])
            ->latest()
            ->take(10)
            ->get();
            
        $recentUsers = \App\Models\User::latest()
            ->take(5)
            ->get();

        return view('dashboard.admin', compact(
            'totalUsers',
            'totalCars',
            'totalDrivers',
            'totalBookings',
            'pendingBookings',
            'totalRevenue',
            'recentBookings',
            'recentUsers'
        ));
    }
}
