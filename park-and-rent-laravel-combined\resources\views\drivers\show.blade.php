@extends('layouts.app')

@section('title', $driver->name . ' - Professional Driver')

@section('content')
<div class="bg-white">
    <!-- Back Button -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <a href="{{ route('drivers.index') }}" class="inline-flex items-center text-primary-600 hover:text-primary-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Drivers
        </a>
    </div>

    <!-- Driver Profile -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Driver Info -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Header -->
                <div class="flex items-start space-x-6">
                    <div class="h-32 w-32 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                        @if($driver->profile_image)
                            <img class="h-32 w-32 rounded-full object-cover" src="{{ asset('storage/' . $driver->profile_image) }}" alt="{{ $driver->name }}">
                        @else
                            <svg class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        @endif
                    </div>
                    <div class="flex-1">
                        <h1 class="text-3xl font-bold text-gray-900">{{ $driver->name }}</h1>
                        <p class="text-lg text-gray-600">Professional Driver</p>
                        <div class="flex items-center mt-2">
                            <svg class="h-5 w-5 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span class="text-gray-600">{{ $driver->location }}</span>
                        </div>

                        <!-- Rating -->
                        <div class="flex items-center mt-3">
                            <div class="flex items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= floor($driver->rating))
                                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @else
                                        <svg class="h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endif
                                @endfor
                                <span class="ml-2 text-lg font-medium text-gray-900">{{ number_format($driver->rating, 1) }}</span>
                                <span class="ml-1 text-gray-600">({{ $driver->reviews }} reviews)</span>
                            </div>
                        </div>

                        <!-- Availability Status -->
                        <div class="mt-3">
                            @if($driver->is_available)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    Available
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    Unavailable
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Driver Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-600">Experience</div>
                        <div class="text-xl font-bold text-gray-900">{{ $driver->experience }} years</div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-600">Age</div>
                        <div class="text-xl font-bold text-gray-900">{{ $driver->age }} years old</div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-600">License Status</div>
                        <div class="text-sm font-medium">
                            @if($driver->license_verification_status === 'verified')
                                <span class="text-green-600">✓ Verified</span>
                            @elseif($driver->license_verification_status === 'pending')
                                <span class="text-yellow-600">⏳ Pending</span>
                            @else
                                <span class="text-red-600">✗ Not Verified</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Specialties -->
                @if($driver->specialties && count($driver->specialties) > 0)
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Specialties</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($driver->specialties as $specialty)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                            {{ $specialty }}
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Bio -->
                @if($driver->bio)
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">About</h3>
                    <p class="text-gray-600 leading-relaxed">{{ $driver->bio }}</p>
                </div>
                @endif

                <!-- Languages -->
                @if($driver->languages && count($driver->languages) > 0)
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Languages</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($driver->languages as $language)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            {{ $language }}
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Reviews Section -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Reviews</h3>
                    <div class="space-y-4">
                        <!-- Sample reviews - in real app, these would come from database -->
                        <div class="border-l-4 border-primary-500 pl-4">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm text-gray-600">2 days ago</span>
                            </div>
                            <p class="text-gray-700">"Excellent driver! Very professional and punctual. Made our trip comfortable and safe."</p>
                            <p class="text-sm text-gray-500 mt-1">- John D.</p>
                        </div>

                        <div class="border-l-4 border-primary-500 pl-4">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm text-gray-600">1 week ago</span>
                            </div>
                            <p class="text-gray-700">"Great local knowledge and very friendly. Highly recommend for airport transfers."</p>
                            <p class="text-sm text-gray-500 mt-1">- Sarah M.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white border border-gray-200 rounded-lg p-6 sticky top-6">
                    <!-- Price -->
                    <div class="text-center mb-6">
                        <div class="text-3xl font-bold text-primary-600">${{ $driver->price_per_hour }}</div>
                        <div class="text-gray-600">per hour</div>
                        @if($driver->price_per_day)
                        <div class="text-xl font-semibold text-primary-600 mt-1">${{ $driver->price_per_day }}</div>
                        <div class="text-gray-600 text-sm">per day</div>
                        @endif
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        @auth
                            @if(auth()->user()->role === 'client' && $driver->is_available)
                                <button onclick="openHireModal()" class="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-lg font-medium transition-colors">
                                    Hire Driver
                                </button>
                                <div class="grid grid-cols-2 gap-3">
                                    <a href="{{ route('chat.start-driver', $driver) }}" class="flex items-center justify-center border border-primary-600 text-primary-600 hover:bg-primary-50 px-4 py-2 rounded-md font-medium transition-colors">
                                        <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        Chat
                                    </a>
                                    <button onclick="openContactModal()" class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-md font-medium transition-colors">
                                        Contact
                                    </button>
                                </div>
                            @elseif(!$driver->is_available)
                                <div class="text-center text-gray-600 py-4">
                                    <p>Driver is currently unavailable</p>
                                </div>
                            @else
                                <div class="text-center text-gray-600 py-4">
                                    <p>Only verified clients can hire drivers.</p>
                                </div>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="block w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center transition-colors">
                                Login to Hire
                            </a>
                            <a href="{{ route('register') }}" class="block w-full border border-primary-600 text-primary-600 hover:bg-primary-50 px-6 py-3 rounded-md font-medium text-center transition-colors">
                                Sign Up
                            </a>
                        @endauth
                    </div>

                    <!-- Driver Stats -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Response time:</span>
                                <span class="font-medium">Within 1 hour</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Acceptance rate:</span>
                                <span class="font-medium">95%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Member since:</span>
                                <span class="font-medium">{{ $driver->created_at->format('M Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hire Driver Modal -->
<div id="hireModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Hire {{ $driver->name }}</h3>
                <button onclick="closeHireModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="{{ route('bookings.store') }}" method="POST">
                @csrf
                <input type="hidden" name="driver_id" value="{{ $driver->id }}">
                <input type="hidden" name="type" value="driver">

                <div class="space-y-4">
                    <div>
                        <label for="service_type" class="block text-sm font-medium text-gray-700">Service Type</label>
                        <select name="service_type" id="service_type" required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select service type</option>
                            <option value="hourly">Hourly Service</option>
                            <option value="daily">Daily Service</option>
                            <option value="airport_transfer">Airport Transfer</option>
                            <option value="city_tour">City Tour</option>
                            <option value="long_distance">Long Distance</option>
                        </select>
                    </div>

                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="date" name="start_date" id="start_date" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="start_time" class="block text-sm font-medium text-gray-700">Start Time</label>
                        <input type="time" name="start_time" id="start_time" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700">Duration (hours)</label>
                        <input type="number" name="duration" id="duration" min="1" max="24" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="pickup_location" class="block text-sm font-medium text-gray-700">Pickup Location</label>
                        <input type="text" name="pickup_location" id="pickup_location" required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Enter pickup address">
                    </div>

                    <div>
                        <label for="destination" class="block text-sm font-medium text-gray-700">Destination (Optional)</label>
                        <input type="text" name="destination" id="destination"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Enter destination">
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700">Special Instructions</label>
                        <textarea name="message" id="message" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Any special requirements or instructions..."></textarea>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="flex justify-between text-sm">
                            <span>Rate per hour:</span>
                            <span>${{ $driver->price_per_hour }}</span>
                        </div>
                        <div class="flex justify-between text-sm mt-1">
                            <span>Duration:</span>
                            <span id="durationDisplay">- hours</span>
                        </div>
                        <div class="flex justify-between font-medium text-lg mt-2 pt-2 border-t">
                            <span>Estimated Total:</span>
                            <span id="totalPrice">$0</span>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex space-x-3">
                    <button type="button" onclick="closeHireModal()"
                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md">
                        Cancel
                    </button>
                    <button type="submit"
                            class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md">
                        Send Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Contact Modal -->
<div id="contactModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Contact Driver</h3>
                <button onclick="closeContactModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="text-center">
                <div class="mb-4">
                    <svg class="mx-auto h-12 w-12 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                </div>
                <p class="text-gray-600 mb-4">Contact {{ $driver->name }} directly:</p>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="font-medium text-lg">0788613669</p>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                </div>
                <button onclick="closeContactModal()"
                        class="mt-4 w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function openHireModal() {
    document.getElementById('hireModal').classList.remove('hidden');
    calculateDriverPrice();
}

function closeHireModal() {
    document.getElementById('hireModal').classList.add('hidden');
}

function openContactModal() {
    document.getElementById('contactModal').classList.remove('hidden');
}

function closeContactModal() {
    document.getElementById('contactModal').classList.add('hidden');
}

function calculateDriverPrice() {
    const durationInput = document.getElementById('duration');
    const pricePerHour = {{ $driver->price_per_hour }};

    durationInput.addEventListener('input', updateDriverPrice);

    function updateDriverPrice() {
        const duration = parseInt(durationInput.value) || 0;
        document.getElementById('durationDisplay').textContent = duration + ' hours';
        document.getElementById('totalPrice').textContent = '$' + (duration * pricePerHour);
    }
}

// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
});
</script>
@endsection
