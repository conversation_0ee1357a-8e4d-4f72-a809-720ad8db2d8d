# Park & Rent - Complete Deployment Guide

## 🚀 Quick Start

This guide will help you combine your React frontend and Laravel backend for easy hosting on Hostinger with minimal errors.

## 📁 Project Structure

```
park_and_rent/
├── src/                    # React frontend source
├── park-and-rent-api/      # Laravel backend
├── deploy.bat             # Windows deployment script
├── deploy.sh              # Linux/Mac deployment script
├── setup-laravel.bat      # Windows Laravel setup
├── setup-laravel.sh       # Linux/Mac Laravel setup
└── production.env.template # Production environment template
```

## 🛠️ Development Setup

### 1. Setup Laravel Backend

**Windows:**
```bash
setup-laravel.bat
```

**Linux/Mac:**
```bash
chmod +x setup-laravel.sh
./setup-laravel.sh
```

### 2. Setup React Frontend

```bash
npm install
npm run dev
```

### 3. Start Development Servers

**Terminal 1 (Laravel API):**
```bash
cd park-and-rent-api
php artisan serve
```

**Terminal 2 (React Frontend):**
```bash
npm run dev
```

Your app will be available at:
- Frontend: http://localhost:5173
- Backend API: http://127.0.0.1:8000

## 🌐 Production Deployment

### Automated Deployment

**Windows:**
```bash
deploy.bat
```

**Linux/Mac:**
```bash
chmod +x deploy.sh
./deploy.sh
```

This will:
1. ✅ Build the React frontend
2. ✅ Optimize the Laravel backend
3. ✅ Create a deployment package
4. ✅ Generate .htaccess configuration
5. ✅ Create production .env template

### Manual Deployment Steps

1. **Build Frontend:**
   ```bash
   npm run build
   ```

2. **Prepare Laravel:**
   ```bash
   cd park-and-rent-api
   composer install --optimize-autoloader --no-dev
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Upload to Hostinger:**
   - Upload `dist/*` to `public_html/`
   - Upload `park-and-rent-api/` to `public_html/api/`

## 🔧 Server Configuration

### 1. Database Setup (Hostinger)

1. Create MySQL database in hPanel
2. Note down:
   - Database name: `u555127771_parkrent`
   - Username: `u555127771_parkrent`
   - Password: [your password]

### 2. Environment Configuration

1. Copy `production.env.template` to `public_html/api/.env`
2. Update database credentials:
   ```env
   DB_DATABASE=u555127771_parkrent
   DB_USERNAME=u555127771_parkrent
   DB_PASSWORD=your_actual_password
   ```

### 3. Server Commands

```bash
# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force
php artisan db:seed --force

# Set permissions
chmod -R 755 .
chmod -R 775 api/storage/
chmod -R 775 api/bootstrap/cache/

# Create storage link
cd api/
php artisan storage:link
```

## 🌍 URL Structure

After deployment, your application will have:

- **Frontend:** https://ebisera.com
- **API Base:** https://ebisera.com/api
- **Health Check:** https://ebisera.com/api/up
- **Cars API:** https://ebisera.com/api/cars
- **Admin Panel:** https://ebisera.com/admin

## 🔒 Security Features

The deployment includes:
- ✅ Security headers (XSS, CSRF protection)
- ✅ CORS configuration
- ✅ File access protection (.env, composer.json)
- ✅ Directory browsing disabled
- ✅ Production optimizations

## 🐛 Troubleshooting

### Common Issues

1. **API not working:**
   - Check `api/storage/logs/laravel.log`
   - Verify database connection
   - Ensure .env is configured correctly

2. **Frontend not loading:**
   - Check .htaccess syntax
   - Verify React build completed successfully

3. **CORS errors:**
   - Update CORS origins in `park-and-rent-api/config/cors.php`
   - Check FRONTEND_URL in .env

4. **Database errors:**
   - Verify database credentials
   - Check if migrations ran successfully
   - Ensure database exists

### Debug Commands

```bash
# Check Laravel configuration
php artisan config:show

# Test database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Clear all caches
php artisan optimize:clear
```

## 📊 Performance Optimizations

The deployment includes:
- ✅ Gzip compression
- ✅ Browser caching
- ✅ Minified assets
- ✅ Code splitting
- ✅ Laravel optimizations

## 🔄 Updates

To update your deployment:

1. Make changes to your code
2. Run deployment script again
3. Upload only changed files
4. Run any new migrations if needed

## 📞 Support

For issues related to:
- **Hosting:** Contact Hostinger support
- **Application:** Check logs and troubleshooting section
- **Database:** Verify credentials and connection

## 🎯 Benefits of This Setup

1. **Single Domain:** No CORS issues
2. **SEO Friendly:** React app served from root
3. **Easy Maintenance:** Automated deployment
4. **Cost Effective:** Single hosting account
5. **Scalable:** Can easily add CDN or load balancer
6. **Secure:** Production-ready security headers

---

**Contact Information:**
- Phone: **********
- Email: <EMAIL>
