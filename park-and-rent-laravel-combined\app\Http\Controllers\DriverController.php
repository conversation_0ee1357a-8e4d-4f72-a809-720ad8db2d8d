<?php

namespace App\Http\Controllers;

use App\Models\Driver;
use Illuminate\Http\Request;

class DriverController extends Controller
{
    /**
     * Display a listing of drivers.
     */
    public function index(Request $request)
    {
        $query = Driver::where('is_available', true)
            ->where('license_verification_status', 'verified')
            ->with('user');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhereJsonContains('specialties', $search);
            });
        }

        // Filter by location
        if ($request->has('location') && $request->location) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by experience
        if ($request->has('min_experience') && $request->min_experience) {
            $query->where('experience', '>=', $request->min_experience);
        }

        // Filter by price range
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        // Filter by specialties
        if ($request->has('specialty') && $request->specialty) {
            $query->whereJsonContains('specialties', $request->specialty);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'rating');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price_per_hour', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price_per_hour', 'desc');
                break;
            case 'experience':
                $query->orderBy('experience', 'desc');
                break;
            case 'reviews':
                $query->orderBy('reviews', 'desc');
                break;
            default:
                $query->orderBy('rating', 'desc');
                break;
        }

        $drivers = $query->paginate(12);

        return view('drivers.index', compact('drivers'));
    }

    /**
     * Display the specified driver.
     */
    public function show(Driver $driver)
    {
        // Load relationships
        $driver->load('user');

        // Get related drivers (similar location or specialties)
        $relatedDrivers = Driver::where('is_available', true)
            ->where('license_verification_status', 'verified')
            ->where('id', '!=', $driver->id)
            ->where(function ($query) use ($driver) {
                $query->where('location', $driver->location)
                      ->orWhereBetween('price_per_hour', [
                          $driver->price_per_hour - 5,
                          $driver->price_per_hour + 5
                      ]);
            })
            ->with('user')
            ->take(3)
            ->get();

        return view('drivers.show', compact('driver', 'relatedDrivers'));
    }
}
