<?php

require 'vendor/autoload.php';
require 'bootstrap/app.php';

use App\Models\Car;

$cars = Car::all();

foreach ($cars as $index => $car) {
    $licenseNumber = 'ABC-' . str_pad($index + 100, 3, '0', STR_PAD_LEFT);
    $car->update(['license_plate' => $licenseNumber]);
    echo "Updated car {$car->id} with license plate: {$licenseNumber}\n";
}

echo "All license plates updated successfully!\n";
