<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    /**
     * Upload car images
     */
    public function uploadCarImages(Request $request)
    {
        $request->validate([
            'images' => 'required|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $uploadedPaths = [];

        foreach ($request->file('images') as $image) {
            $filename = Str::random(40) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('cars', $filename, 'public');
            $uploadedPaths[] = $path;
        }

        return response()->json([
            'success' => true,
            'paths' => $uploadedPaths,
            'message' => 'Images uploaded successfully'
        ]);
    }

    /**
     * Upload profile image
     */
    public function uploadProfileImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $image = $request->file('image');
        $filename = Str::random(40) . '.' . $image->getClientOriginalExtension();
        $path = $image->storeAs('profiles', $filename, 'public');

        return response()->json([
            'success' => true,
            'path' => $path,
            'url' => asset('storage/' . $path),
            'message' => 'Profile image uploaded successfully'
        ]);
    }

    /**
     * Delete uploaded file
     */
    public function deleteFile(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        $path = $request->path;

        if (Storage::disk('public')->exists($path)) {
            Storage::disk('public')->delete($path);
            
            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'File not found'
        ], 404);
    }

    /**
     * Upload driver documents
     */
    public function uploadDriverDocuments(Request $request)
    {
        $request->validate([
            'license' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'id_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $uploadedFiles = [];

        if ($request->hasFile('license')) {
            $filename = 'license_' . Str::random(30) . '.' . $request->file('license')->getClientOriginalExtension();
            $path = $request->file('license')->storeAs('driver-documents', $filename, 'public');
            $uploadedFiles['license'] = $path;
        }

        if ($request->hasFile('id_document')) {
            $filename = 'id_' . Str::random(30) . '.' . $request->file('id_document')->getClientOriginalExtension();
            $path = $request->file('id_document')->storeAs('driver-documents', $filename, 'public');
            $uploadedFiles['id_document'] = $path;
        }

        if ($request->hasFile('profile_image')) {
            $filename = 'profile_' . Str::random(30) . '.' . $request->file('profile_image')->getClientOriginalExtension();
            $path = $request->file('profile_image')->storeAs('profiles', $filename, 'public');
            $uploadedFiles['profile_image'] = $path;
        }

        return response()->json([
            'success' => true,
            'files' => $uploadedFiles,
            'message' => 'Documents uploaded successfully'
        ]);
    }
}
